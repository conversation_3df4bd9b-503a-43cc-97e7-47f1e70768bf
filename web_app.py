"""
Web interface for the AI Cryptocurrency Trading Agent
"""

from flask import Flask, render_template, jsonify, request, redirect, url_for
from flask_jwt_extended import JW<PERSON>anager, jwt_required, get_jwt_identity, verify_jwt_in_request
from flask_cors import CORS
from datetime import datetime, timedelta
import json
import threading
import time
import os
from config import Config
from trading_agent import TradingAgent
from logger_config import setup_logger
from database import get_db_manager
from autonomous_agent import get_autonomous_agent
from auth_service import auth_service
from auth_models import auth_db

app = Flask(__name__)
app.config['JWT_SECRET_KEY'] = os.environ.get('JWT_SECRET_KEY', 'your-secret-string')
app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(days=7)
app.config['SECRET_KEY'] = os.environ.get('FLASK_SECRET_KEY', 'webauthn-session-secret-key-for-ez-money-trading')

# Initialize extensions
jwt = JWTManager(app)
CORS(app)

# Add security headers for WebAuthn
@app.after_request
def add_security_headers(response):
    # Enable WebAuthn permissions with multiple formats for browser compatibility
    response.headers['Permissions-Policy'] = 'publickey-credentials-get=*, publickey-credentials-create=*, cross-origin-isolated=*'
    response.headers['Feature-Policy'] = 'publickey-credentials-get *, publickey-credentials-create *'
    # Add Content Security Policy for WebAuthn
    response.headers['Content-Security-Policy'] = "frame-ancestors 'self' *.replit.dev *.repl.it;"
    # Additional security headers
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'SAMEORIGIN'  # Changed from DENY to allow same-origin frames
    response.headers['X-XSS-Protection'] = '1; mode=block'
    # Ensure HTTPS context for WebAuthn
    if request.is_secure or request.headers.get('X-Forwarded-Proto') == 'https':
        response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
    return response

# Register blueprints
from auth_blueprint import auth_bp
app.register_blueprint(auth_bp, url_prefix='/auth')

logger = setup_logger("WebApp")

# Global variables
trading_agent = None
autonomous_agent = None
agent_config = None

def initialize_agent():
    """Initialize the trading agent and autonomous agent"""
    global trading_agent, autonomous_agent, agent_config
    try:
        agent_config = Config()
        trading_agent = TradingAgent(agent_config)
        trading_agent.start()
        
        # Initialize autonomous agent with LLM enabled
        autonomous_agent = get_autonomous_agent(
            agent_config,
            trading_agent.wallet_manager,
            trading_agent.market_data,
            trading_agent.trading_strategy
        )
        
        # Load saved agent configuration or use defaults
        from database import get_db_manager
        db_manager = get_db_manager()
        saved_name = db_manager.get_agent_config("agent_name", "BitHunter")
        saved_risk = float(db_manager.get_agent_config("risk_tolerance", "0.7"))
        saved_aggression = float(db_manager.get_agent_config("aggression_level", "0.8"))
        saved_llm_provider = db_manager.get_agent_config("llm_provider", "openai")
        saved_llm_enabled = db_manager.get_agent_config("llm_enabled", "True").lower() == "true"
        
        # Configure agent with saved settings
        autonomous_agent.set_personality(
            name=saved_name,
            mode="autonomous",
            risk_tolerance=saved_risk,
            aggression_level=saved_aggression,
            llm_provider=saved_llm_provider if saved_llm_provider else "openai",
            llm_enabled=saved_llm_enabled
        )
        
        # Start the autonomous agent for LLM analysis
        autonomous_agent.start_agent()
        
        logger.info("Trading agent and autonomous agent initialized for web interface")
    except Exception as e:
        logger.error(f"Failed to initialize trading agent: {str(e)}")

# Initialize authentication database
try:
    auth_db.create_tables()
    logger.info("Authentication database initialized")
except Exception as e:
    logger.error(f"Failed to initialize auth database: {e}")

# Authentication middleware
def require_auth():
    """Check if request is authenticated"""
    try:
        verify_jwt_in_request()
        return True
    except:
        return False

@jwt.user_identity_loader
def user_identity_lookup(user_id):
    return user_id

@jwt.user_lookup_loader
def user_lookup_callback(_jwt_header, jwt_data):
    identity = jwt_data["sub"]
    user = auth_service.auth_db.get_user_by_id(identity)
    return user

# Authentication routes
@app.route('/auth')
def auth_page():
    """Authentication page"""
    return render_template('auth.html')

@app.route('/auth/register', methods=['POST'])
def register():
    """User registration endpoint"""
    result = auth_service.register_user(
        email=request.json.get('email'),
        password=request.json.get('password'),
        first_name=request.json.get('first_name', ''),
        last_name=request.json.get('last_name', '')
    )
    
    status_code = result.get('code', 200 if result.get('success') else 400)
    return jsonify(result), status_code

@app.route('/auth/login', methods=['POST'])
def login():
    """User login endpoint"""
    result = auth_service.authenticate_user(
        email=request.json.get('email'),
        password=request.json.get('password'),
        mfa_token=request.json.get('mfa_token')
    )
    
    status_code = result.get('code', 200 if result.get('success') else 400)
    return jsonify(result), status_code

@app.route('/auth/profile')
@jwt_required()
def get_profile():
    """Get user profile"""
    user_id = get_jwt_identity()
    result = auth_service.get_user_profile(user_id)
    
    status_code = result.get('code', 200 if result.get('success') else 400)
    return jsonify(result), status_code

@app.route('/auth/mfa/enable', methods=['POST'])
@jwt_required()
def enable_mfa():
    """Enable MFA for user"""
    user_id = get_jwt_identity()
    result = auth_service.enable_mfa(user_id)
    
    status_code = result.get('code', 200 if result.get('success') else 400)
    return jsonify(result), status_code

@app.route('/auth/google')
def google_auth():
    """Redirect to Google OAuth"""
    google_client_id = os.environ.get("GOOGLE_OAUTH_CLIENT_ID")
    if not google_client_id:
        return jsonify({"error": "Google OAuth not configured"}), 500
    
    # Redirect to Google OAuth
    from oauthlib.oauth2 import WebApplicationClient
    import requests
    
    client = WebApplicationClient(google_client_id)
    google_provider_cfg = requests.get("https://accounts.google.com/.well-known/openid-configuration").json()
    authorization_endpoint = google_provider_cfg["authorization_endpoint"]
    
    request_uri = client.prepare_request_uri(
        authorization_endpoint,
        redirect_uri=request.base_url.replace("http://", "https://") + "/callback",
        scope=["openid", "email", "profile"],
    )
    return redirect(request_uri)

@app.route('/auth/google/callback')
def google_callback():
    """Handle Google OAuth callback"""
    # Implementation for Google OAuth callback
    return jsonify({"message": "Google authentication callback"})

@app.route('/')
@app.route('/dashboard')
def dashboard():
    """Main dashboard page - requires authentication"""
    # Check for token in URL parameter (from OAuth redirect)
    token = request.args.get('token')
    if token:
        # Redirect to clean URL and let frontend handle token
        return f'''
        <script>
            localStorage.setItem('access_token', '{token}');
            window.location.href = '/';
        </script>
        '''
    
    # For initial page load, serve the dashboard and let frontend handle auth
    # The frontend will check localStorage for token and redirect if needed
    return render_template('dashboard.html')

@app.route('/api/status')
@jwt_required()
def get_status():
    """Get current trading agent status"""
    if not trading_agent:
        return jsonify({"error": "Trading agent not initialized"}), 500
    
    try:
        status = trading_agent.get_status()
        return jsonify(status)
    except Exception as e:
        logger.error(f"Error getting status: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/portfolio')
@jwt_required()
def get_portfolio():
    """Get portfolio information including balances"""
    if not trading_agent:
        return jsonify({"error": "Trading agent not initialized"}), 500
    
    try:
        # Get wallet balance
        balance = trading_agent.wallet_manager.get_balance()
        
        # Get current market data
        trading_pair = agent_config.TRADING_PAIR if agent_config else "BTC-USD"
        market_data = trading_agent.market_data.get_current_data(trading_pair)
        current_price = market_data.get('price', 0) if market_data else 0
        
        # Calculate portfolio values
        portfolio = {}
        total_value_usd = 0
        
        if balance:
            for currency, amount in balance.items():
                if currency == 'USD':
                    value_usd = amount
                elif currency == 'BTC':
                    value_usd = amount * current_price
                else:
                    value_usd = 0  # Add more currencies as needed
                
                portfolio[currency] = {
                    'amount': amount,
                    'value_usd': value_usd,
                    'percentage': 0  # Will calculate after getting total
                }
                total_value_usd += value_usd
        
        # Calculate percentages
        for currency in portfolio:
            if total_value_usd > 0:
                portfolio[currency]['percentage'] = (portfolio[currency]['value_usd'] / total_value_usd) * 100
        
        return jsonify({
            'portfolio': portfolio,
            'total_value_usd': total_value_usd,
            'current_btc_price': current_price,
            'last_updated': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error getting portfolio: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/transactions')
@jwt_required()
def get_transactions():
    """Get transaction history from database"""
    try:
        limit = request.args.get('limit', 20, type=int)
        db_manager = get_db_manager()
        transactions = db_manager.get_transactions(limit)
        return jsonify({
            'transactions': transactions,
            'count': len(transactions)
        })
    except Exception as e:
        logger.error(f"Error getting transactions: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/market-data')
@jwt_required()
def get_market_data():
    """Get current market data"""
    if not trading_agent:
        return jsonify({"error": "Trading agent not initialized"}), 500
    
    try:
        trading_pair = agent_config.TRADING_PAIR if agent_config else "BTC-USD"
        market_data = trading_agent.market_data.get_current_data(trading_pair)
        return jsonify(market_data if market_data else {})
    except Exception as e:
        logger.error(f"Error getting market data: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/strategy')
def get_strategy_info():
    """Get trading strategy information"""
    if not trading_agent:
        return jsonify({"error": "Trading agent not initialized"}), 500
    
    try:
        strategy_status = trading_agent.trading_strategy.get_strategy_status()
        performance = trading_agent.trading_strategy.get_performance_metrics()
        
        return jsonify({
            'strategy': strategy_status,
            'performance': performance
        })
    except Exception as e:
        logger.error(f"Error getting strategy info: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/force-trade', methods=['POST'])
def force_trade():
    """Force execute a trade (for testing)"""
    if not trading_agent:
        return jsonify({"error": "Trading agent not initialized"}), 500
    
    try:
        data = request.get_json()
        action = data.get('action')  # 'buy' or 'sell'
        amount = data.get('amount')
        
        if action not in ['buy', 'sell']:
            return jsonify({"error": "Invalid action. Must be 'buy' or 'sell'"}), 400
        
        success = trading_agent.force_trade_signal(action, amount)
        
        # Save portfolio snapshot after trade
        if success:
            try:
                portfolio_data = trading_agent.wallet_manager.get_balance()
                trading_pair = agent_config.TRADING_PAIR if agent_config else "BTC-USD"
                market_data = trading_agent.market_data.get_current_data(trading_pair)
                current_price = market_data.get('price', 0) if market_data else 0
                
                # Calculate portfolio values for database
                portfolio_snapshot = {'portfolio': {}, 'total_value_usd': 0}
                total_value_usd = 0
                
                if portfolio_data:
                    for currency, amount in portfolio_data.items():
                        if currency == 'USD':
                            value_usd = amount
                        elif currency == 'BTC':
                            value_usd = amount * current_price
                        else:
                            value_usd = 0
                        
                        portfolio_snapshot['portfolio'][currency] = {
                            'amount': amount,
                            'value_usd': value_usd,
                            'percentage': 0
                        }
                        total_value_usd += value_usd
                
                # Calculate percentages
                for currency in portfolio_snapshot['portfolio']:
                    if total_value_usd > 0:
                        portfolio_snapshot['portfolio'][currency]['percentage'] = (
                            portfolio_snapshot['portfolio'][currency]['value_usd'] / total_value_usd
                        ) * 100
                
                portfolio_snapshot['total_value_usd'] = total_value_usd
                
                # Save to database
                db_manager = get_db_manager()
                db_manager.save_portfolio_snapshot(portfolio_snapshot)
                
            except Exception as e:
                logger.error(f"Error saving portfolio snapshot: {str(e)}")
        
        return jsonify({
            'success': success,
            'message': f"{'Successfully executed' if success else 'Failed to execute'} {action} order"
        })
        
    except Exception as e:
        logger.error(f"Error forcing trade: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/portfolio-history')
def get_portfolio_history():
    """Get portfolio value history"""
    try:
        days = request.args.get('days', 30, type=int)
        currency = request.args.get('currency', 'BTC')
        
        db_manager = get_db_manager()
        history = db_manager.get_portfolio_history(currency, days)
        
        return jsonify({
            'history': history,
            'count': len(history),
            'days': days
        })
        
    except Exception as e:
        logger.error(f"Error getting portfolio history: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/trading-performance')
def get_trading_performance():
    """Get trading performance metrics"""
    try:
        days = request.args.get('days', 30, type=int)
        
        db_manager = get_db_manager()
        performance = db_manager.get_trading_performance(days)
        
        return jsonify(performance)
        
    except Exception as e:
        logger.error(f"Error getting trading performance: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/wallet-address')
def get_wallet_address():
    """Get Bitcoin wallet address for receiving funds"""
    try:
        if trading_agent:
            wallet_address = trading_agent.wallet_manager.get_wallet_address()
            return jsonify({
                "address": wallet_address,
                "network": "Bitcoin",
                "status": "active" if wallet_address else "error"
            })
        else:
            return jsonify({"error": "Trading agent not available"}), 500
    except Exception as e:
        logger.error(f"Error getting wallet address: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/send-bitcoin', methods=['POST'])
def send_bitcoin():
    """Send Bitcoin to another address"""
    try:
        if not trading_agent:
            return jsonify({"error": "Trading agent not available"}), 500
        
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        to_address = data.get('to_address', '').strip()
        amount_usd = data.get('amount_usd', 0)
        
        # Validate inputs
        if not to_address:
            return jsonify({"error": "Destination address is required"}), 400
        
        try:
            amount_usd = float(amount_usd)
            if amount_usd <= 0:
                return jsonify({"error": "Amount must be greater than 0"}), 400
        except (ValueError, TypeError):
            return jsonify({"error": "Invalid amount format"}), 400
        
        # Execute send transaction
        success = trading_agent.wallet_manager.send_bitcoin(to_address, amount_usd)
        
        if success:
            logger.info(f"Bitcoin send successful: ${amount_usd} to {to_address}")
            return jsonify({
                "success": True,
                "message": f"Successfully sent ${amount_usd:.2f} worth of Bitcoin to {to_address}",
                "amount_usd": amount_usd,
                "to_address": to_address
            })
        else:
            return jsonify({
                "success": False,
                "error": "Failed to send Bitcoin. Check logs for details."
            }), 400
            
    except Exception as e:
        logger.error(f"Error sending Bitcoin: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/agent/status', methods=['GET'])
def get_agent_status():
    """Get autonomous agent status"""
    try:
        if not autonomous_agent:
            return jsonify({"error": "Autonomous agent not available"}), 500
        
        status = autonomous_agent.get_agent_status()
        return jsonify(status)
        
    except Exception as e:
        logger.error(f"Error getting agent status: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/agent/start', methods=['POST'])
def start_agent():
    """Start the autonomous agent"""
    try:
        if not autonomous_agent:
            return jsonify({"error": "Autonomous agent not available"}), 500
        
        success = autonomous_agent.start_agent()
        if success:
            return jsonify({"success": True, "message": "Autonomous agent started"})
        else:
            return jsonify({"success": False, "error": "Agent is already running"}), 400
            
    except Exception as e:
        logger.error(f"Error starting agent: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/agent/stop', methods=['POST'])
def stop_agent():
    """Stop the autonomous agent"""
    try:
        if not autonomous_agent:
            return jsonify({"error": "Autonomous agent not available"}), 500
        
        success = autonomous_agent.stop_agent()
        return jsonify({"success": True, "message": "Autonomous agent stopped"})
        
    except Exception as e:
        logger.error(f"Error stopping agent: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/agent/configure', methods=['POST'])
def configure_agent():
    """Configure agent personality and settings"""
    try:
        if not autonomous_agent:
            return jsonify({"error": "Autonomous agent not available"}), 500
        
        # Security check: prevent configuration changes while agent is running
        agent_status = autonomous_agent.get_agent_status()
        if agent_status.get('is_running', False):
            return jsonify({
                "error": "Configuration changes are blocked while agent is running for security",
                "security_locked": True
            }), 403
        
        data = request.get_json()
        if not data:
            return jsonify({"error": "No configuration data provided"}), 400
        
        name = data.get('name', '').strip()
        avatar_path = data.get('avatar_path', '').strip() or None
        risk_tolerance = data.get('risk_tolerance', 0.7)
        aggression_level = data.get('aggression_level', 0.8)
        sensitivity = data.get('sensitivity', 0.5)
        
        # Validate inputs
        if not name:
            return jsonify({"error": "Agent name is required"}), 400
        
        try:
            risk_tolerance = float(risk_tolerance)
            aggression_level = float(aggression_level)
            sensitivity = float(sensitivity)
        except (ValueError, TypeError):
            return jsonify({"error": "Risk tolerance, aggression level, and sensitivity must be numbers"}), 400
        
        # Validate ranges
        if not (0.0 <= risk_tolerance <= 1.0):
            return jsonify({"error": "Risk tolerance must be between 0.0 and 1.0"}), 400
        
        if not (0.0 <= aggression_level <= 1.0):
            return jsonify({"error": "Aggression level must be between 0.0 and 1.0"}), 400
        
        if not (0.1 <= sensitivity <= 2.0):
            return jsonify({"error": "Sensitivity must be between 0.1 and 2.0"}), 400
        
        # Get mode parameter and LLM settings
        mode = data.get('mode', 'autonomous')
        llm_enabled = data.get('llm_enabled', False)
        llm_provider = data.get('llm_provider', None)
        custom_endpoint = data.get('custom_endpoint', None)
        
        # If custom provider is selected, save the endpoint
        if llm_provider == 'custom' and custom_endpoint:
            os.environ['CUSTOM_ENDPOINT'] = custom_endpoint.strip()
        
        # Configure agent
        autonomous_agent.set_personality(name, mode, avatar_path, risk_tolerance, aggression_level, sensitivity, llm_provider, llm_enabled)
        
        # Save configuration to database for persistence
        from database import get_db_manager
        db_manager = get_db_manager()
        db_manager.save_agent_config("agent_name", name)
        db_manager.save_agent_config("risk_tolerance", str(risk_tolerance))
        db_manager.save_agent_config("aggression_level", str(aggression_level))
        db_manager.save_agent_config("sensitivity", str(sensitivity))
        db_manager.save_agent_config("llm_provider", llm_provider or "")
        db_manager.save_agent_config("llm_enabled", str(llm_enabled))
        
        return jsonify({
            "success": True,
            "message": f"Agent '{name}' configured successfully",
            "config": {
                "name": name,
                "mode": mode,
                "avatar_path": avatar_path,
                "risk_tolerance": risk_tolerance,
                "aggression_level": aggression_level,
                "sensitivity": sensitivity,
                "llm_enabled": llm_enabled,
                "llm_provider": llm_provider
            }
        })
        
    except Exception as e:
        logger.error(f"Error configuring agent: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/agent/decisions', methods=['GET'])
def get_agent_decisions():
    """Get recent agent trading decisions from database"""
    try:
        from database import get_db_manager
        db_manager = get_db_manager()
        
        limit = request.args.get('limit', 20, type=int)
        
        # Get trading strategies/decisions from database
        with db_manager.get_session() as session:
            from database import TradingStrategy
            strategies = session.query(TradingStrategy)\
                .filter(TradingStrategy.strategy_name == 'autonomous_agent')\
                .order_by(TradingStrategy.timestamp.desc())\
                .limit(limit)\
                .all()
            
            decisions = []
            for strategy in strategies:
                decisions.append({
                    'id': strategy.id,
                    'timestamp': strategy.timestamp.isoformat(),
                    'action': strategy.signal_type,
                    'confidence': float(strategy.confidence),
                    'reasoning': strategy.reason or 'No reasoning provided',
                    'executed': strategy.executed,
                    'status': strategy.result or 'pending',
                    'profit_loss': float(strategy.profit_loss) if strategy.profit_loss is not None else 0.0
                })
        
        return jsonify({
            "decisions": decisions,
            "count": len(decisions)
        })
        
    except Exception as e:
        logger.error(f"Error getting agent decisions from database: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/agent/force-analysis', methods=['POST'])
def force_market_analysis():
    """Force immediate market analysis"""
    try:
        if not autonomous_agent:
            return jsonify({"error": "Autonomous agent not available"}), 500
        
        success = autonomous_agent.force_market_analysis()
        if success:
            return jsonify({"success": True, "message": "Market analysis completed"})
        else:
            return jsonify({"success": False, "error": "Failed to perform market analysis"}), 500
            
    except Exception as e:
        logger.error(f"Error forcing market analysis: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/agent/enable-testing', methods=['POST'])
def enable_testing_mode():
    """Enable testing mode for 10 minutes with forced buy/sell decisions"""
    try:
        if not autonomous_agent:
            return jsonify({"error": "Autonomous agent not available"}), 500
        
        if not autonomous_agent.is_running:
            return jsonify({"error": "Agent must be running to enable testing mode"}), 400
        
        autonomous_agent.enable_testing_mode()
        return jsonify({"success": True, "message": "Testing mode enabled for 10 minutes"})
            
    except Exception as e:
        logger.error(f"Error enabling testing mode: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/agent/update-settings', methods=['POST'])
def update_agent_settings():
    """Update agent personality settings while running"""
    try:
        if not autonomous_agent:
            return jsonify({"error": "Autonomous agent not available"}), 500
        
        # Security check: prevent configuration changes while agent is running
        agent_status = autonomous_agent.get_agent_status()
        if agent_status.get('is_running', False):
            return jsonify({
                "error": "Configuration changes are blocked while agent is running for security",
                "security_locked": True
            }), 403
        
        data = request.get_json()
        if not data:
            return jsonify({"error": "No settings data provided"}), 400
        
        risk_tolerance = data.get('risk_tolerance')
        aggression_level = data.get('aggression_level')
        sensitivity = data.get('sensitivity')
        
        # Validate inputs if provided
        if risk_tolerance is not None:
            try:
                risk_tolerance = float(risk_tolerance)
            except (ValueError, TypeError):
                return jsonify({"error": "Risk tolerance must be a number"}), 400
        
        if aggression_level is not None:
            try:
                aggression_level = float(aggression_level)
            except (ValueError, TypeError):
                return jsonify({"error": "Aggression level must be a number"}), 400
        
        if sensitivity is not None:
            try:
                sensitivity = float(sensitivity)
            except (ValueError, TypeError):
                return jsonify({"error": "Sensitivity must be a number"}), 400
        
        # Update settings
        success = autonomous_agent.update_personality_settings(risk_tolerance, aggression_level, sensitivity)
        
        if success:
            return jsonify({"success": True, "message": "Agent settings updated"})
        else:
            return jsonify({"success": False, "error": "Failed to update settings"}), 500
            
    except Exception as e:
        logger.error(f"Error updating agent settings: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/agent/pending-trades', methods=['GET'])
def get_pending_trades():
    """Get trades awaiting approval in interactive mode"""
    try:
        if not autonomous_agent:
            return jsonify({"error": "Autonomous agent not available"}), 500
        
        pending = autonomous_agent.get_pending_trades()
        return jsonify({"pending_trades": pending})
        
    except Exception as e:
        logger.error(f"Error getting pending trades: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/agent/approve-trade/<int:trade_id>', methods=['POST'])
def approve_trade(trade_id):
    """Approve and execute a pending trade"""
    try:
        if not autonomous_agent:
            return jsonify({"error": "Autonomous agent not available"}), 500
        
        success = autonomous_agent.approve_trade(trade_id)
        if success:
            return jsonify({"success": True, "message": f"Trade {trade_id} approved and executed"})
        else:
            return jsonify({"error": f"Failed to approve trade {trade_id}"}), 400
        
    except Exception as e:
        logger.error(f"Error approving trade: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/agent/reject-trade/<int:trade_id>', methods=['POST'])
def reject_trade(trade_id):
    """Reject a pending trade"""
    try:
        if not autonomous_agent:
            return jsonify({"error": "Autonomous agent not available"}), 500
        
        success = autonomous_agent.reject_trade(trade_id)
        if success:
            return jsonify({"success": True, "message": f"Trade {trade_id} rejected"})
        else:
            return jsonify({"error": f"Failed to reject trade {trade_id}"}), 400
        
    except Exception as e:
        logger.error(f"Error rejecting trade: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/llm/providers', methods=['GET'])
def get_llm_providers():
    """Get available LLM providers"""
    try:
        if not autonomous_agent:
            return jsonify({"error": "Autonomous agent not available"}), 500
        
        providers = autonomous_agent.llm_manager.get_available_providers()
        return jsonify({"providers": providers})
        
    except Exception as e:
        logger.error(f"Error getting LLM providers: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/llm/status', methods=['GET'])
def get_llm_status():
    """Get current LLM status"""
    try:
        if not autonomous_agent:
            return jsonify({"error": "Autonomous agent not available"}), 500
        
        status = autonomous_agent.llm_manager.get_status()
        return jsonify(status)
        
    except Exception as e:
        logger.error(f"Error getting LLM status: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/llm/set-provider', methods=['POST'])
def set_llm_provider():
    """Set the active LLM provider"""
    try:
        if not autonomous_agent:
            return jsonify({"error": "Autonomous agent not available"}), 500
        
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        provider_key = data.get('provider')
        if not provider_key:
            return jsonify({"error": "Provider key is required"}), 400
        
        success = autonomous_agent.llm_manager.set_active_provider(provider_key)
        if success:
            # Update agent personality
            autonomous_agent.personality.llm_provider = provider_key
            autonomous_agent.personality.llm_enabled = True
            
            return jsonify({
                "success": True,
                "message": f"LLM provider set to {provider_key}",
                "active_provider": provider_key
            })
        else:
            return jsonify({"error": f"Failed to set provider {provider_key}. Check API key configuration."}), 400
        
    except Exception as e:
        logger.error(f"Error setting LLM provider: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/llm/disable', methods=['POST'])
def disable_llm():
    """Disable LLM analysis"""
    try:
        if not autonomous_agent:
            return jsonify({"error": "Autonomous agent not available"}), 500
        
        autonomous_agent.personality.llm_enabled = False
        autonomous_agent.personality.llm_provider = None
        
        return jsonify({
            "success": True,
            "message": "LLM analysis disabled"
        })
        
    except Exception as e:
        logger.error(f"Error disabling LLM: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/check-api-key', methods=['POST'])
def check_api_key():
    """Check if API key exists for the specified provider"""
    try:
        data = request.get_json()
        provider = data.get('provider')
        
        if not provider:
            return jsonify({"error": "Provider not specified"}), 400
        
        # Map providers to their environment variable names
        key_mapping = {
            'openai': 'OPENAI_API_KEY',
            'deepseek': 'DEEPSEEK_API_KEY',
            'fingpt': 'FINGPT_API_KEY',
            'bloomberg': 'BLOOMBERG_API_KEY',
            'custom': 'CUSTOM_API_KEY'
        }
        
        env_var = key_mapping.get(provider)
        if not env_var:
            return jsonify({"error": "Unknown provider"}), 400
        
        # Check if the API key exists in environment
        has_key = bool(os.environ.get(env_var))
        
        return jsonify({
            "has_key": has_key,
            "provider": provider,
            "key_name": env_var
        })
        
    except Exception as e:
        logger.error(f"Error checking API key: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/save-api-key', methods=['POST'])
def save_api_key():
    """Save API key for the specified provider"""
    try:
        data = request.get_json()
        provider = data.get('provider')
        key_name = data.get('key_name')
        api_key = data.get('api_key')
        
        if not all([provider, key_name, api_key]):
            return jsonify({"error": "Missing required fields"}), 400
        
        # Validate the API key format (basic validation)
        if len(api_key.strip()) < 10:
            return jsonify({"error": "API key appears to be too short"}), 400
        
        # Set the environment variable for this session
        os.environ[key_name] = api_key.strip()
        
        # If custom provider, also save the endpoint
        if provider == 'custom':
            custom_endpoint = data.get('custom_endpoint')
            if custom_endpoint:
                os.environ['CUSTOM_ENDPOINT'] = custom_endpoint.strip()
        
        return jsonify({
            "success": True,
            "message": f"API key saved for {provider}",
            "provider": provider
        })
        
    except Exception as e:
        logger.error(f"Error saving API key: {e}")
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    # Initialize the trading agent in a separate thread
    init_thread = threading.Thread(target=initialize_agent)
    init_thread.daemon = True
    init_thread.start()
    
    # Give it a moment to initialize
    time.sleep(2)
    
    # Run the Flask app
    logger.info("Starting web interface on port 5000")
    app.run(host='0.0.0.0', port=5000, debug=False)