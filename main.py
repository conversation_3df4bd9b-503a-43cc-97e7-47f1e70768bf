#!/usr/bin/env python3
"""
Main entry point for the AI Cryptocurrency Trading Agent
"""

import sys
import os
import time
import signal
from dotenv import load_dotenv

from config import Config
from trading_agent import TradingAgent
from cli_interface import CLIInterface
from logger_config import setup_logger

# Load environment variables
load_dotenv()

# Global variables for graceful shutdown
agent = None
running = True

def signal_handler(signum, frame):
    """Handle shutdown signals gracefully"""
    global running, agent
    logger = setup_logger()
    logger.info(f"Received signal {signum}. Shutting down gracefully...")
    running = False
    if agent:
        agent.stop()
    sys.exit(0)

def main():
    """Main application entry point"""
    global agent, running
    
    # Setup logging
    logger = setup_logger()
    logger.info("Starting EZ Money Trading Agent")
    
    try:
        # Load configuration
        config = Config()
        logger.info("Configuration loaded successfully")
        
        # Initialize trading agent
        agent = TradingAgent(config)
        logger.info("Trading agent initialized")
        
        # Setup CLI interface
        cli = CLIInterface(agent)
        
        # Register signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # Start the agent
        logger.info("Starting trading agent...")
        agent.start()
        
        # Main execution loop
        while running:
            try:
                # Display current status
                cli.display_status()
                
                # Check for user input (non-blocking)
                user_input = cli.get_user_input()
                if user_input:
                    if user_input.lower() in ['quit', 'exit', 'q']:
                        logger.info("User requested shutdown")
                        break
                    else:
                        cli.handle_command(user_input)
                
                # Sleep before next iteration
                time.sleep(config.STATUS_UPDATE_INTERVAL)
                
            except KeyboardInterrupt:
                logger.info("Received keyboard interrupt")
                break
            except Exception as e:
                logger.error(f"Error in main loop: {str(e)}")
                time.sleep(5)  # Wait before retrying
                
    except Exception as e:
        logger.error(f"Fatal error: {str(e)}")
        return 1
    
    finally:
        # Cleanup
        if agent:
            logger.info("Stopping trading agent...")
            agent.stop()
        logger.info("Application shutdown complete")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
