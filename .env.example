# Coinbase CDP API Configuration (Required for Agent<PERSON>it)
CDP_API_KEY_ID=your_cdp_api_key_id_here
CDP_API_KEY_SECRET=your_cdp_api_key_secret_here
CDP_WALLET_SECRET=your_cdp_wallet_secret_here

# Legacy: Additional Coinbase API keys (if needed for other features)
CDP_API_KEY_NAME=your_cdp_api_key_name_here
CDP_API_KEY_PRIVATE_KEY=your_cdp_private_key_here
COINBASE_API_KEY=your_coinbase_api_key
COINBASE_API_SECRET=your_coinbase_api_secret

# Trading Configuration
DEFAULT_BASE_CURRENCY=USD
DEFAULT_QUOTE_CURRENCY=BTC
MAX_TRADE_AMOUNT=100.0
MIN_TRADE_AMOUNT=10.0
MAX_DAILY_TRADES=10
STOP_LOSS_PERCENTAGE=5.0
TAKE_PROFIT_PERCENTAGE=10.0

# Trading Strategy
STRATEGY_TYPE=trend_following
RSI_PERIOD=14
RSI_OVERSOLD=30.0
RSI_OVERBOUGHT=70.0
MOVING_AVERAGE_PERIOD=20

# Execution Settings (in seconds)
TRADING_INTERVAL=300
STATUS_UPDATE_INTERVAL=30
MARKET_DATA_REFRESH_INTERVAL=60

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=trading_agent.log
LOG_MAX_SIZE=10485760
LOG_BACKUP_COUNT=5

# Development Settings
DRY_RUN=true
DEBUG_MODE=false
