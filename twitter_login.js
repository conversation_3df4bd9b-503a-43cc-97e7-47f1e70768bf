const TwitterStrategy = require('@superfaceai/passport-twitter-oauth2');

/**
 * Twitter/X OAuth Login Module
 * Handles Twitter OAuth 2.0 authentication strategy and routes
 */

class TwitterLogin {
  constructor(passport, db) {
    this.passport = passport;
    this.db = db;
    this.config = {
      clientID: process.env.TWITTER_CLIENT_ID || 'TWITTER_CLIENT_ID',
      clientSecret: process.env.TWITTER_CLIENT_SECRET || 'TWITTER_CLIENT_SECRET',
      callbackURL: process.env.TWITTER_CALLBACK_URL || '/auth/twitter/callback'
    };
  }

  /**
   * Initialize Twitter OAuth strategy
   */
  initializeStrategy() {
    console.log('Initializing Twitter/X OAuth strategy...');
    console.log('Twitter Client ID:', this.config.clientID.substring(0, 10) + '...');
    console.log('Twitter Callback URL:', this.config.callbackURL);
    console.log('Twitter credentials configured:', this.config.clientID !== 'TWITTER_CLIENT_ID');

    this.passport.use('twitter', new TwitterStrategy({
      clientType: 'confidential',
      clientID: this.config.clientID,
      clientSecret: this.config.clientSecret,
      callbackURL: this.config.callbackURL,
      scope: ['tweet.read', 'users.read', 'offline.access']
    }, (accessToken, refreshToken, profile, done) => {
      console.log('Twitter OAuth callback triggered for user:', profile.id);
      console.log('Twitter profile data:', profile);
      this.handleOAuthCallback(accessToken, refreshToken, profile, done);
    }));
  }

  /**
   * Handle OAuth callback from Twitter
   */
  handleOAuthCallback(accessToken, refreshToken, profile, done) {
    const providerId = profile.id;
    const email = profile.emails && profile.emails[0] && profile.emails[0].value;
    const name = profile.displayName || profile.username;

    console.log('Processing Twitter user:', { id: providerId, email, name, username: profile.username });

    // Check if user already exists
    this.db.get(
      'SELECT * FROM users WHERE provider = ? AND provider_id = ?', 
      ['twitter', providerId], 
      (err, row) => {
        if (err) {
          console.error('Database error during Twitter user lookup:', err);
          return done(err);
        }

        if (row) {
          console.log('Existing Twitter user found:', row.id);
          return done(null, row);
        }

        // Check if user exists with same email but different provider (if email is available)
        if (email) {
          this.db.get(
            'SELECT * FROM users WHERE email = ?',
            [email],
            (err, existingUser) => {
              if (err) {
                console.error('Database error during email lookup:', err);
                return done(err);
              }

              if (existingUser) {
                // Link Twitter account to existing user
                console.log('Linking Twitter account to existing user:', existingUser.id);
                this.db.run(
                  'UPDATE users SET provider = ?, provider_id = ? WHERE id = ?',
                  ['twitter', providerId, existingUser.id],
                  (err) => {
                    if (err) {
                      console.error('Error linking Twitter account:', err);
                      return done(err);
                    }
                    
                    // Return updated user
                    this.db.get('SELECT * FROM users WHERE id = ?', existingUser.id, (e, updatedUser) => {
                      if (e) return done(e);
                      console.log('Successfully linked Twitter account');
                      done(null, updatedUser);
                    });
                  }
                );
              } else {
                // Create new user
                console.log('Creating new Twitter user');
                this.createNewUser(email, providerId, name, done);
              }
            }
          );
        } else {
          // No email available, create user without email
          console.log('Creating new Twitter user (no email available)');
          this.createNewUser(null, providerId, name, done);
        }
      }
    );
  }

  /**
   * Create a new user from Twitter OAuth
   */
  createNewUser(email, providerId, name, done) {
    const db = this.db; // Capture db reference for use in callback
    
    // Generate a unique email if none provided (Twitter doesn't always provide email)
    const userEmail = email || `twitter_${providerId}@twitter.local`;
    
    db.run(
      'INSERT INTO users (email, provider, provider_id, name) VALUES (?, ?, ?, ?)',
      [userEmail, 'twitter', providerId, name],
      function(err) {
        if (err) {
          console.error('Error creating Twitter user:', err);
          return done(err);
        }

        const newUserId = this.lastID;
        console.log('New Twitter user created with ID:', newUserId);

        // Fetch the newly created user
        db.get('SELECT * FROM users WHERE id = ?', newUserId, (e, newUser) => {
          if (e) {
            console.error('Error fetching newly created user:', e);
            return done(e);
          }
          if (!newUser) {
            console.error('Failed to create Twitter user - user not found after insertion');
            return done(null, false);
          }
          console.log('Successfully created and retrieved Twitter user');
          done(null, newUser);
        });
      }
    );
  }

  /**
   * Setup Twitter OAuth routes
   */
  setupRoutes(app) {
    console.log('Setting up Twitter/X OAuth routes...');

    // Initiate Twitter OAuth
    app.get('/auth/twitter',
      this.passport.authenticate('twitter', {
        scope: ['tweet.read', 'users.read', 'offline.access']
      })
    );

    // Twitter OAuth callback
    app.get('/auth/twitter/callback',
      this.passport.authenticate('twitter', {
        successRedirect: '/trading',
        failureRedirect: '/login?error=twitter_auth_failed',
        failureFlash: false
      })
    );

    console.log('Twitter/X OAuth routes configured');
  }

  /**
   * Get configuration status
   */
  getConfigStatus() {
    return {
      provider: 'twitter',
      configured: this.config.clientID !== 'TWITTER_CLIENT_ID' && 
                  this.config.clientSecret !== 'TWITTER_CLIENT_SECRET',
      clientID: this.config.clientID.substring(0, 10) + '...',
      callbackURL: this.config.callbackURL
    };
  }
}

module.exports = TwitterLogin;
