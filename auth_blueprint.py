"""
Google OAuth Authentication Blueprint
Enterprise-grade authentication with Google OAuth 2.0
"""

import json
import os
import requests
from datetime import datetime, timedelta
from flask import Blueprint, redirect, request, url_for, jsonify, session
from flask_jwt_extended import create_access_token, jwt_required, get_jwt_identity, decode_token
from oauthlib.oauth2 import WebApplicationClient
from auth_models import auth_db, User, OAuthAccount, SecurityLog
from webauthn_service import webauthn_service

# Google OAuth configuration
GOOGLE_CLIENT_ID = os.environ.get("GOOGLE_OAUTH_CLIENT_ID")
GOOGLE_CLIENT_SECRET = os.environ.get("GOOGLE_OAUTH_CLIENT_SECRET")
GOOGLE_DISCOVERY_URL = "https://accounts.google.com/.well-known/openid-configuration"

# Development redirect URL
DEV_REDIRECT_URL = f'https://{os.environ.get("REPLIT_DEV_DOMAIN", "localhost:5000")}/auth/google/callback'

print(f"""
🔐 Google OAuth Setup Instructions:
1. Go to https://console.cloud.google.com/apis/credentials
2. Create a new OAuth 2.0 Client ID
3. Add {DEV_REDIRECT_URL} to Authorized redirect URIs

For detailed setup guide:
https://docs.replit.com/additional-resources/google-auth-in-flask#set-up-your-oauth-app--client
""")

client = WebApplicationClient(GOOGLE_CLIENT_ID) if GOOGLE_CLIENT_ID else None

auth_bp = Blueprint("auth", __name__, url_prefix="/auth")

def get_google_provider_cfg():
    """Get Google OAuth provider configuration"""
    return requests.get(GOOGLE_DISCOVERY_URL).json()

@auth_bp.route("/google")
def google_login():
    """Initiate Google OAuth login"""
    if not GOOGLE_CLIENT_ID:
        return jsonify({"error": "Google OAuth not configured"}), 500
    
    google_provider_cfg = get_google_provider_cfg()
    authorization_endpoint = google_provider_cfg["authorization_endpoint"]
    
    request_uri = client.prepare_request_uri(
        authorization_endpoint,
        redirect_uri=request.base_url.replace("http://", "https://") + "/callback",
        scope=["openid", "email", "profile"],
    )
    return redirect(request_uri)

@auth_bp.route("/google/callback")
def google_callback():
    """Handle Google OAuth callback"""
    if not GOOGLE_CLIENT_ID:
        return jsonify({"error": "Google OAuth not configured"}), 500
    
    code = request.args.get("code")
    google_provider_cfg = get_google_provider_cfg()
    token_endpoint = google_provider_cfg["token_endpoint"]
    
    # Exchange code for tokens
    token_url, headers, body = client.prepare_token_request(
        token_endpoint,
        authorization_response=request.url.replace("http://", "https://"),
        redirect_url=request.base_url.replace("http://", "https://"),
        code=code,
    )
    
    token_response = requests.post(
        token_url,
        headers=headers,
        data=body,
        auth=(GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET),
    )
    
    client.parse_request_body_response(json.dumps(token_response.json()))
    
    # Get user info
    userinfo_endpoint = google_provider_cfg["userinfo_endpoint"]
    uri, headers, body = client.add_token(userinfo_endpoint)
    userinfo_response = requests.get(uri, headers=headers, data=body)
    userinfo = userinfo_response.json()
    
    if not userinfo.get("email_verified"):
        return jsonify({"error": "User email not verified by Google"}), 400
    
    # Process user authentication
    email = userinfo["email"]
    name = userinfo.get("name", "")
    picture = userinfo.get("picture", "")
    provider_id = userinfo["sub"]
    
    with auth_db.get_session() as db_session:
        # Check existing user
        user = db_session.query(User).filter(User.email == email).first()
        
        if not user:
            # Create new user
            user = User(
                email=email,
                first_name=userinfo.get("given_name", ""),
                last_name=userinfo.get("family_name", ""),
                profile_picture_url=picture,
                is_verified=True,
                email_verified_at=datetime.utcnow()
            )
            db_session.add(user)
            db_session.flush()  # Get user ID
            
            # Log registration
            auth_db.log_security_event(
                user.id, 
                "user_registered", 
                "User registered via Google OAuth",
                request.remote_addr,
                request.headers.get('User-Agent')
            )
        
        # Check/create OAuth account
        oauth_account = db_session.query(OAuthAccount).filter(
            OAuthAccount.user_id == user.id,
            OAuthAccount.provider == "google"
        ).first()
        
        if not oauth_account:
            oauth_account = OAuthAccount(
                user_id=user.id,
                provider="google",
                provider_id=provider_id,
                provider_email=email,
                provider_name=name,
                provider_picture=picture
            )
            db_session.add(oauth_account)
        else:
            # Update existing OAuth account
            oauth_account.provider_name = name
            oauth_account.provider_picture = picture
        
        # Update user last login
        user.last_login = datetime.utcnow()
        
        db_session.commit()
        
        # Log successful login
        auth_db.log_security_event(
            user.id,
            "login_success",
            "Google OAuth login",
            request.remote_addr,
            request.headers.get('User-Agent')
        )
        
        # Create JWT token  
        access_token = create_access_token(identity=str(user.id))
        
        # Store user in session
        session['user_id'] = user.id
        session['access_token'] = access_token
        
        return redirect(url_for('dashboard') + f'?token={access_token}')

@auth_bp.route("/register", methods=["POST"])
def register():
    """Traditional email/password registration"""
    data = request.get_json()
    email = data.get("email")
    password = data.get("password")
    first_name = data.get("first_name", "")
    last_name = data.get("last_name", "")
    
    if not email or not password:
        return jsonify({"error": "Email and password required"}), 400
    
    # Check if user exists
    existing_user = auth_db.get_user_by_email(email)
    if existing_user:
        return jsonify({
            "error": "An account with this email address already exists. Please use a different email or try logging in instead."
        }), 409
    
    # Create user
    try:
        user = auth_db.create_user(
            email=email,
            password=password,
            first_name=first_name,
            last_name=last_name
        )
        
        # Log registration
        auth_db.log_security_event(
            user.id,
            "user_registered",
            "Traditional email registration",
            request.remote_addr,
            request.headers.get('User-Agent')
        )
        
        access_token = create_access_token(identity=str(user.id))
        
        return jsonify({
            "success": True,
            "message": "User registered successfully",
            "access_token": access_token,
            "user": user.to_dict()
        }), 201
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@auth_bp.route("/login", methods=["POST"])
def login():
    """Traditional email/password login"""
    data = request.get_json()
    email = data.get("email")
    password = data.get("password")
    mfa_token = data.get("mfa_token")
    
    if not email or not password:
        return jsonify({"error": "Email and password required"}), 400
    
    user = auth_db.get_user_by_email(email)
    
    if not user or not user.check_password(password):
        # Log failed login
        auth_db.log_security_event(
            str(user.id) if user else None,
            "login_failed",
            f"Failed login attempt for {email}",
            request.remote_addr,
            request.headers.get('User-Agent'),
            risk_level='medium'
        )
        return jsonify({"error": "Invalid credentials"}), 401
    
    if not user.is_active:
        return jsonify({"error": "Account disabled"}), 403
    
    # Check MFA if enabled
    if user.mfa_enabled:
        if not mfa_token:
            return jsonify({"error": "MFA token required", "mfa_required": True}), 422
        
        if not user.verify_mfa_token(mfa_token):
            auth_db.log_security_event(
                user.id,
                "mfa_failed",
                "Invalid MFA token",
                request.remote_addr,
                request.headers.get('User-Agent'),
                risk_level='high'
            )
            return jsonify({"error": "Invalid MFA token"}), 401
    
    # Create access token with string identity
    access_token = create_access_token(identity=str(user.id))
    
    # Update last login and log event
    with auth_db.get_session() as db_session:
        # Refresh user object in current session
        user = db_session.merge(user)
        user.last_login = datetime.utcnow()
        db_session.commit()
        
        # Log successful login
        auth_db.log_security_event(
            str(user.id),
            "login_success",
            "Email/password login",
            request.remote_addr,
            request.headers.get('User-Agent')
        )
    
    return jsonify({
        "success": True,
        "message": "Login successful",
        "access_token": access_token,
        "user": user.to_dict()
    })

@auth_bp.route("/forgot-password", methods=["POST"])
def forgot_password():
    """Send password reset email"""
    data = request.get_json()
    email = data.get("email")
    
    if not email:
        return jsonify({"error": "Email address required"}), 400
    
    user = auth_db.get_user_by_email(email)
    
    # Always return success to prevent email enumeration
    if user:
        # Generate reset token (expires in 1 hour)
        reset_token = create_access_token(
            identity=str(user.id),
            expires_delta=timedelta(hours=1),
            additional_claims={"type": "password_reset"}
        )
        
        # Log password reset request
        auth_db.log_security_event(
            str(user.id),
            "password_reset_requested",
            "Password reset token generated",
            request.remote_addr,
            request.headers.get('User-Agent')
        )
        
        # In a real application, you would send an email here
        # For now, we'll return the reset token for testing
        return jsonify({
            "message": "If an account with this email exists, a password reset link has been sent.",
            "reset_token": reset_token  # Remove this in production
        })
    
    return jsonify({
        "message": "If an account with this email exists, a password reset link has been sent."
    })

@auth_bp.route("/reset-password", methods=["POST"])
def reset_password():
    """Reset password with token"""
    data = request.get_json()
    reset_token = data.get("reset_token")
    new_password = data.get("new_password")
    
    if not reset_token or not new_password:
        return jsonify({"error": "Reset token and new password required"}), 400
    
    try:
        # Verify reset token
        decoded_token = decode_token(reset_token)
        if decoded_token.get("type") != "password_reset":
            return jsonify({"error": "Invalid reset token"}), 401
        
        user_id = decoded_token.get("sub")
        user = auth_db.get_user_by_id(user_id)
        
        if not user:
            return jsonify({"error": "User not found"}), 404
        
        # Update password
        with auth_db.get_session() as db_session:
            user = db_session.merge(user)
            user.set_password(new_password)
            db_session.commit()
        
        # Log password reset
        auth_db.log_security_event(
            str(user.id),
            "password_reset_completed",
            "Password successfully reset",
            request.remote_addr,
            request.headers.get('User-Agent')
        )
        
        return jsonify({"message": "Password reset successfully"})
        
    except Exception as e:
        return jsonify({"error": "Invalid or expired reset token"}), 401

@auth_bp.route("/logout", methods=["POST"])
@jwt_required()
def logout():
    """Logout user"""
    user_id = get_jwt_identity()
    
    # Log logout
    auth_db.log_security_event(
        user_id,
        "logout",
        "User logout",
        request.remote_addr,
        request.headers.get('User-Agent')
    )
    
    # Clear session
    session.clear()
    
    return jsonify({"message": "Logged out successfully"})

@auth_bp.route("/profile")
@jwt_required()
def get_profile():
    """Get user profile"""
    user_id = get_jwt_identity()
    user = auth_db.get_user_by_id(user_id)
    
    if not user:
        return jsonify({"error": "User not found"}), 404
    
    return jsonify({"user": user.to_dict()})

@auth_bp.route("/mfa/enable", methods=["POST"])
@jwt_required()
def enable_mfa():
    """Enable MFA for user"""
    user_id = get_jwt_identity()
    
    with auth_db.get_session() as db_session:
        user = db_session.query(User).filter(User.id == user_id).first()
        
        if not user:
            return jsonify({"error": "User not found"}), 404
        
        if user.mfa_enabled:
            return jsonify({"error": "MFA already enabled"}), 400
        
        secret = user.enable_mfa()
        qr_code = user.get_mfa_qr_code()
        
        db_session.commit()
        
        # Log MFA enable
        auth_db.log_security_event(
            user_id,
            "mfa_enabled",
            "MFA enabled by user",
            request.remote_addr,
            request.headers.get('User-Agent')
        )
        
        return jsonify({
            "message": "MFA enabled successfully",
            "secret": secret,
            "qr_code": qr_code
        })

@auth_bp.route("/mfa/verify", methods=["POST"])
@jwt_required()
def verify_mfa():
    """Verify MFA token"""
    user_id = get_jwt_identity()
    data = request.get_json()
    token = data.get("token")
    
    if not token:
        return jsonify({"error": "MFA token required"}), 400
    
    user = auth_db.get_user_by_id(user_id)
    
    if not user:
        return jsonify({"error": "User not found"}), 404
    
    if user.verify_mfa_token(token):
        return jsonify({"message": "MFA token valid"})
    else:
        return jsonify({"error": "Invalid MFA token"}), 401

# WebAuthn/Passkey endpoints for biometric authentication
@auth_bp.route('/webauthn/register/start', methods=['POST'])
@jwt_required()
def webauthn_register_start():
    """Start WebAuthn registration for biometric authentication"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json() or {}
        authenticator_type = data.get('authenticator_type', 'platform')
        
        result = webauthn_service.start_registration(user_id, authenticator_type)
        
        if result.get('success'):
            # Store challenge in session for verification
            session['webauthn_challenge'] = result['challenge']
            return jsonify(result)
        else:
            return jsonify(result), result.get('code', 400)
    
    except Exception as e:
        return jsonify({"error": f"Registration start failed: {str(e)}"}), 500

@auth_bp.route('/webauthn/register/complete', methods=['POST'])
@jwt_required()
def webauthn_register_complete():
    """Complete WebAuthn registration"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        if not data or 'credential' not in data:
            return jsonify({"error": "Credential data required"}), 400
        
        challenge = session.get('webauthn_challenge')
        if not challenge:
            return jsonify({"error": "No registration in progress"}), 400
        
        credential_name = data.get('name', 'Biometric Key')
        result = webauthn_service.complete_registration(
            user_id, data['credential'], challenge, credential_name
        )
        
        # Clear challenge from session
        session.pop('webauthn_challenge', None)
        
        if result.get('success'):
            return jsonify(result)
        else:
            return jsonify(result), result.get('code', 400)
    
    except Exception as e:
        return jsonify({"error": f"Registration completion failed: {str(e)}"}), 500

@auth_bp.route('/webauthn/authenticate/start', methods=['POST'])
def webauthn_authenticate_start():
    """Start WebAuthn authentication"""
    try:
        data = request.get_json() or {}
        user_id = data.get('user_id')  # Optional for usernameless flow
        
        result = webauthn_service.start_authentication(user_id)
        
        if result.get('success'):
            # Store challenge in session for verification
            session['webauthn_auth_challenge'] = result['challenge']
            return jsonify(result)
        else:
            return jsonify(result), result.get('code', 400)
    
    except Exception as e:
        return jsonify({"error": f"Authentication start failed: {str(e)}"}), 500

@auth_bp.route('/webauthn/authenticate/complete', methods=['POST'])
def webauthn_authenticate_complete():
    """Complete WebAuthn authentication"""
    try:
        data = request.get_json()
        
        if not data or 'credential' not in data:
            return jsonify({"error": "Credential data required"}), 400
        
        challenge = session.get('webauthn_auth_challenge')
        if not challenge:
            return jsonify({"error": "No authentication in progress"}), 400
        
        result = webauthn_service.complete_authentication(data['credential'], challenge)
        
        # Clear challenge from session
        session.pop('webauthn_auth_challenge', None)
        
        if result.get('success'):
            # Create JWT token for successful authentication
            access_token = create_access_token(identity=result['user']['id'])
            result['access_token'] = access_token
            return jsonify(result)
        else:
            return jsonify(result), result.get('code', 401)
    
    except Exception as e:
        return jsonify({"error": f"Authentication failed: {str(e)}"}), 500

@auth_bp.route('/webauthn/credentials', methods=['GET'])
@jwt_required()
def get_webauthn_credentials():
    """Get user's WebAuthn credentials"""
    try:
        user_id = get_jwt_identity()
        credentials = webauthn_service.get_user_credentials(user_id)
        return jsonify({"credentials": credentials})
    
    except Exception as e:
        return jsonify({"error": f"Failed to get credentials: {str(e)}"}), 500

@auth_bp.route('/webauthn/credentials/<credential_id>', methods=['DELETE'])
@jwt_required()
def remove_webauthn_credential(credential_id):
    """Remove a WebAuthn credential"""
    try:
        user_id = get_jwt_identity()
        result = webauthn_service.remove_credential(user_id, credential_id)
        
        if result.get('success'):
            return jsonify(result)
        else:
            return jsonify(result), result.get('code', 400)
    
    except Exception as e:
        return jsonify({"error": f"Failed to remove credential: {str(e)}"}), 500