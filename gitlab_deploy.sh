#!/bin/bash

# GitLab Deployment Script for EZ Money 2 Trading Agent
echo "Deploying EZ Money 2 to GitLab..."

# Configure SSH to use the provided keys
export GIT_SSH_COMMAND="ssh -i ./id_ed25519 -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null"

# Configure Git user
git config user.name "EZ Money Trading Bot"
git config user.email "<EMAIL>"

# Add GitLab remote (force update if exists)
git remote remove origin 2>/dev/null || true
git remote <NAME_EMAIL>:rospoplabs-group/ez-money2.git

# Create comprehensive commit
git add -A
git commit -m "Complete AI Cryptocurrency Trading Agent v1.0

Production-ready trading system with all features implemented:

Core Features:
✅ AI-powered BTC trading with Coinbase AgentKit
✅ Real-time web dashboard with portfolio monitoring  
✅ PostgreSQL database persistence
✅ Custom trade amount handling ($5000+ trades working)
✅ Balance calculation from transaction history
✅ Input field clearing after trades
✅ Technical analysis strategies
✅ Comprehensive error handling
✅ CLI monitoring interface

Recent Fixes:
- Fixed custom amount processing in web interface
- Resolved balance validation using transaction history
- Fixed sell order execution issues  
- Improved database transaction persistence
- Enhanced real-time dashboard updates

Architecture:
- Backend: Python Flask API with SQLAlchemy
- Database: PostgreSQL with full audit trail
- Frontend: Responsive dashboard with live updates
- Trading: Coinbase AgentKit integration
- Config: Environment-based security

Files included:
- main.py (entry point)
- trading_agent.py (core logic)
- web_app.py (Flask interface)
- wallet_manager.py (Coinbase integration)
- trading_strategy.py (technical analysis)
- market_data.py (price feeds)
- database.py (PostgreSQL models)
- config.py (configuration)
- templates/dashboard.html (web UI)

Status: Ready for production deployment
Current mode: DRY RUN (safe testing enabled)"

# Push to GitLab
echo "Pushing to GitLab repository..."
GIT_SSH_COMMAND="ssh -i ./id_ed25519 -o StrictHostKeyChecking=no" git push -u origin main --force

echo ""
echo "✅ Deployment successful!"
echo "Repository: https://gitlab.com/rospoplabs-group/ez-money2"
echo ""
echo "Next steps:"
echo "1. Configure environment variables on your deployment platform"
echo "2. Set up PostgreSQL database"
echo "3. Add Coinbase API credentials for live trading"
echo "4. Change DRY_RUN=false when ready for live trading"