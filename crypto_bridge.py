#!/usr/bin/env python3
"""
CryptoTraderAI Bridge for MultiLogin Integration
Simplified version of the CryptoTraderAI web app without authentication
"""

import os
import sys
import json
from flask import Flask, render_template, jsonify, request
from flask_cors import CORS
from datetime import datetime
import threading
import time

# Add the CryptoTraderAI path to Python path
crypto_path = os.path.join(os.path.dirname(__file__), '..', 'CryptoTraderAI-1')
sys.path.insert(0, crypto_path)

try:
    from config import Config
    from trading_agent import TradingAgent
    from logger_config import setup_logger
    from database import get_db_manager
    from autonomous_agent import get_autonomous_agent
except ImportError as e:
    print(f"Error importing CryptoTraderAI modules: {e}")
    print("Make sure CryptoTraderAI-1 is in the correct location")
    sys.exit(1)

app = Flask(__name__,
           template_folder=os.path.join(crypto_path, 'templates'),
           static_folder=os.path.join(crypto_path, 'static'))

# Configure CORS to allow requests from MultiLogin
CORS(app, origins=['http://localhost:3000', 'http://127.0.0.1:3000'],
     allow_headers=['Content-Type', 'Authorization', 'X-User-ID', 'X-User-Email'],
     methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'])

logger = setup_logger("CryptoBridge")

# Global variables
trading_agent = None
autonomous_agent = None
agent_config = None
is_initialized = False

def initialize_agent():
    """Initialize the trading agent and autonomous agent"""
    global trading_agent, autonomous_agent, agent_config, is_initialized
    
    if is_initialized:
        return True
        
    try:
        logger.info("Initializing CryptoTraderAI components...")
        
        agent_config = Config()
        trading_agent = TradingAgent(agent_config)
        
        # Initialize autonomous agent with LLM enabled
        autonomous_agent = get_autonomous_agent(
            agent_config,
            trading_agent.wallet_manager,
            trading_agent.market_data,
            trading_agent.trading_strategy
        )
        
        # Load saved agent configuration or use defaults
        db_manager = get_db_manager()
        saved_name = db_manager.get_agent_config("agent_name", "BitHunter")
        saved_risk = float(db_manager.get_agent_config("risk_tolerance", "0.7"))
        saved_aggression = float(db_manager.get_agent_config("aggression_level", "0.8"))
        saved_llm_provider = db_manager.get_agent_config("llm_provider", "openai")
        saved_llm_enabled = db_manager.get_agent_config("llm_enabled", "True").lower() == "true"
        
        # Configure agent with saved settings
        autonomous_agent.set_personality(
            name=saved_name,
            mode="autonomous",
            risk_tolerance=saved_risk,
            aggression_level=saved_aggression,
            llm_provider=saved_llm_provider if saved_llm_provider else "openai",
            llm_enabled=saved_llm_enabled
        )
        
        # Start the trading agent
        trading_agent.start()
        
        # Start the autonomous agent for LLM analysis
        autonomous_agent.start_agent()
        
        is_initialized = True
        logger.info("CryptoTraderAI components initialized successfully")
        return True
        
    except Exception as e:
        logger.error(f"Failed to initialize CryptoTraderAI: {str(e)}")
        return False

@app.route('/health')
def health_check():
    """Health check endpoint"""
    logger.info("Health check endpoint accessed")
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'initialized': is_initialized,
        'message': 'CryptoTraderAI Bridge is running'
    }), 200

@app.route('/')
def dashboard():
    """Main trading dashboard"""
    logger.info("Dashboard route accessed")

    # For now, return a simple HTML page instead of the complex dashboard
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>CryptoTraderAI - Trading Platform</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
            .status { padding: 20px; background: #e8f5e9; border-radius: 8px; margin: 20px 0; }
            .btn { padding: 12px 24px; background: #4CAF50; color: white; border: none; border-radius: 6px; cursor: pointer; margin: 10px; }
            .btn:hover { background: #45a049; }
            .error { background: #ffebee; color: #c62828; }
            .success { background: #e8f5e9; color: #2e7d32; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🚀 CryptoTraderAI Trading Platform</h1>
            <div class="status success">
                <h3>✅ Trading Service Active</h3>
                <p>Welcome to the integrated CryptoTraderAI platform!</p>
                <p><strong>Status:</strong> Connected and ready for trading</p>
            </div>

            <div id="trading-controls">
                <button class="btn" onclick="initializeTrading()">🔄 Initialize Trading System</button>
                <button class="btn" onclick="checkStatus()">📊 Check Status</button>
                <button class="btn" onclick="window.parent.location.href='/logout'">🚪 Logout</button>
            </div>

            <div id="status-display"></div>

            <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                <h3>🔧 Trading Features</h3>
                <ul>
                    <li>✅ Autonomous Trading Agent</li>
                    <li>✅ Real-time Market Data</li>
                    <li>✅ Wallet Management</li>
                    <li>✅ Risk Management</li>
                    <li>✅ LLM-powered Analysis</li>
                </ul>
            </div>
        </div>

        <script>
            async function initializeTrading() {
                try {
                    const response = await fetch('/api/start', { method: 'POST' });
                    const data = await response.json();
                    document.getElementById('status-display').innerHTML =
                        '<div class="status success">Trading system initialized: ' + JSON.stringify(data) + '</div>';
                } catch (error) {
                    document.getElementById('status-display').innerHTML =
                        '<div class="status error">Error: ' + error.message + '</div>';
                }
            }

            async function checkStatus() {
                try {
                    const response = await fetch('/api/status');
                    const data = await response.json();
                    document.getElementById('status-display').innerHTML =
                        '<div class="status success">Status: ' + JSON.stringify(data, null, 2) + '</div>';
                } catch (error) {
                    document.getElementById('status-display').innerHTML =
                        '<div class="status error">Error: ' + error.message + '</div>';
                }
            }

            // Auto-check status on load
            checkStatus();
        </script>
    </body>
    </html>
    """

@app.route('/api/status')
def get_status():
    """Get trading system status"""
    if not is_initialized:
        return jsonify({
            'initialized': False,
            'message': 'Trading system not initialized'
        })
    
    try:
        # Get wallet balance
        wallet_balance = trading_agent.wallet_manager.get_balance() if trading_agent else 0
        
        # Get recent trades
        recent_trades = []
        if trading_agent and hasattr(trading_agent, 'get_recent_trades'):
            recent_trades = trading_agent.get_recent_trades(limit=5)
        
        # Get market data
        market_data = {}
        if trading_agent and trading_agent.market_data:
            try:
                market_data = trading_agent.market_data.get_current_prices(['BTC', 'ETH'])
            except:
                market_data = {}
        
        return jsonify({
            'initialized': True,
            'wallet_balance': wallet_balance,
            'recent_trades': recent_trades,
            'market_data': market_data,
            'agent_running': autonomous_agent.is_running if autonomous_agent else False,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error getting status: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/start', methods=['POST'])
def start_trading():
    """Start the trading system"""
    if not is_initialized:
        if not initialize_agent():
            return jsonify({'error': 'Failed to initialize trading system'}), 500
    
    try:
        if trading_agent and not trading_agent.is_running:
            trading_agent.start()
        
        if autonomous_agent and not autonomous_agent.is_running:
            autonomous_agent.start_agent()
        
        return jsonify({'success': True, 'message': 'Trading system started'})
        
    except Exception as e:
        logger.error(f"Error starting trading: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/stop', methods=['POST'])
def stop_trading():
    """Stop the trading system"""
    try:
        if trading_agent and trading_agent.is_running:
            trading_agent.stop()
        
        if autonomous_agent and autonomous_agent.is_running:
            autonomous_agent.stop_agent()
        
        return jsonify({'success': True, 'message': 'Trading system stopped'})
        
    except Exception as e:
        logger.error(f"Error stopping trading: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/wallet')
def get_wallet():
    """Get wallet information"""
    if not is_initialized:
        return jsonify({'error': 'Trading system not initialized'}), 400
    
    try:
        balance = trading_agent.wallet_manager.get_balance()
        holdings = trading_agent.wallet_manager.get_holdings()
        
        return jsonify({
            'balance': balance,
            'holdings': holdings,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error getting wallet info: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/trades')
def get_trades():
    """Get trading history"""
    if not is_initialized:
        return jsonify({'error': 'Trading system not initialized'}), 400
    
    try:
        limit = request.args.get('limit', 20, type=int)
        trades = []
        
        if trading_agent and hasattr(trading_agent, 'get_recent_trades'):
            trades = trading_agent.get_recent_trades(limit=limit)
        
        return jsonify({
            'trades': trades,
            'count': len(trades),
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error getting trades: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/market')
def get_market_data():
    """Get current market data"""
    if not is_initialized:
        return jsonify({'error': 'Trading system not initialized'}), 400
    
    try:
        symbols = request.args.get('symbols', 'BTC,ETH').split(',')
        market_data = {}
        
        if trading_agent and trading_agent.market_data:
            market_data = trading_agent.market_data.get_current_prices(symbols)
        
        return jsonify({
            'market_data': market_data,
            'symbols': symbols,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error getting market data: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Endpoint not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': 'Internal server error'}), 500

@app.before_request
def log_request():
    """Log all incoming requests for debugging"""
    logger.info(f"Request: {request.method} {request.path} from {request.remote_addr}")

@app.after_request
def log_response(response):
    """Log all responses for debugging"""
    logger.info(f"Response: {response.status_code} for {request.method} {request.path}")
    return response

if __name__ == '__main__':
    print("🚀 Starting CryptoTraderAI Bridge Server...")
    print("🔗 This service integrates with MultiLogin authentication")
    print("📊 Trading dashboard will be available at http://localhost:8080")
    print("🔧 Debug mode enabled for troubleshooting")
    print("🔄 Using port 8080 to avoid conflicts with system services")

    # Initialize on startup (but don't fail if it doesn't work)
    try:
        initialize_agent()
        print("✅ CryptoTraderAI components initialized successfully")
    except Exception as e:
        print(f"⚠️  CryptoTraderAI initialization failed: {e}")
        print("🔄 Service will still start - initialization can be done later")

    app.run(
        host='0.0.0.0',
        port=8080,
        debug=True,  # Enable debug mode for better error messages
        threaded=True,
        use_reloader=False  # Disable reloader to prevent issues with threading
    )
