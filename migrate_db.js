const sqlite3 = require('sqlite3').verbose();

// Simple database migration to add missing columns
const db = new sqlite3.Database('users.db');

console.log('Starting database migration...');

db.serialize(() => {
  // Check if name column exists
  db.all("PRAGMA table_info(users)", (err, columns) => {
    if (err) {
      console.error('Error checking table structure:', err);
      return;
    }
    
    const hasNameColumn = columns.some(col => col.name === 'name');
    
    if (!hasNameColumn) {
      console.log('Adding name column to users table...');
      db.run("ALTER TABLE users ADD COLUMN name TEXT", (err) => {
        if (err) {
          console.error('Error adding name column:', err);
        } else {
          console.log('Successfully added name column');
        }
        db.close();
      });
    } else {
      console.log('Name column already exists');
      db.close();
    }
  });
});
