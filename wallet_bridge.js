const { spawn } = require('child_process');
const path = require('path');

/**
 * Wallet Bridge - Connects Node.js trading platform with Python wallet manager
 * Uses the CryptoTraderAI wallet_manager.py with Coinbase AgentKit
 */

class WalletBridge {
  constructor() {
    this.cryptoTraderPath = path.join(__dirname, '..', 'CryptoTraderAI-1');
    this.isInitialized = false;
  }

  /**
   * Execute Python wallet command and return result
   */
  async executePythonCommand(command, args = []) {
    return new Promise((resolve, reject) => {
      const pythonScript = `
import sys
import os
sys.path.insert(0, '${this.cryptoTraderPath}')

try:
    from config import Config
    from wallet_manager import WalletManager
    from coinbase_agentkit import AgentKit
    import json
    
    # Initialize configuration
    config = Config()
    
    # Initialize AgentKit (will use environment variables for API keys)
    try:
        agent_kit = AgentKit()
    except:
        agent_kit = None  # Fall back to dry run mode
    
    # Initialize wallet manager
    wallet_manager = WalletManager(agent_kit, config)
    
    command = '${command}'
    args = ${JSON.stringify(args)}
    
    if command == 'get_balance':
        result = wallet_manager.get_balance()
    elif command == 'get_wallet_info':
        result = wallet_manager.get_wallet_info()
    elif command == 'get_transaction_history':
        limit = args[0] if args else 10
        result = wallet_manager.get_transaction_history(limit)
    elif command == 'buy_crypto':
        crypto, amount_fiat, fiat = args
        result = wallet_manager.buy_crypto(crypto, float(amount_fiat), fiat)
    elif command == 'sell_crypto':
        crypto, amount_crypto, fiat = args
        result = wallet_manager.sell_crypto(crypto, float(amount_crypto), fiat)
    elif command == 'get_wallet_address':
        result = wallet_manager.get_wallet_address()
    elif command == 'send_bitcoin':
        to_address, amount_usd = args
        result = wallet_manager.send_bitcoin(to_address, float(amount_usd))
    else:
        result = {'error': f'Unknown command: {command}'}
    
    print(json.dumps(result, default=str))
    
except Exception as e:
    print(json.dumps({'error': str(e), 'type': 'python_error'}))
`;

      const pythonProcess = spawn('python3', ['-c', pythonScript], {
        cwd: this.cryptoTraderPath,
        stdio: ['pipe', 'pipe', 'pipe'],
        env: {
          ...process.env,
          PYTHONPATH: this.cryptoTraderPath
        }
      });

      let stdout = '';
      let stderr = '';

      pythonProcess.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      pythonProcess.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      pythonProcess.on('close', (code) => {
        if (code === 0) {
          try {
            const result = JSON.parse(stdout.trim());
            resolve(result);
          } catch (error) {
            reject(new Error(`Failed to parse Python output: ${stdout}`));
          }
        } else {
          reject(new Error(`Python process failed with code ${code}: ${stderr}`));
        }
      });

      pythonProcess.on('error', (error) => {
        reject(new Error(`Failed to start Python process: ${error.message}`));
      });
    });
  }

  /**
   * Get wallet balance using real Coinbase data
   */
  async getBalance() {
    try {
      console.log('Fetching real wallet balance...');
      const result = await this.executePythonCommand('get_balance');
      
      if (result.error) {
        console.error('Wallet balance error:', result.error);
        return this.getFallbackBalance();
      }
      
      console.log('Real wallet balance retrieved:', result);
      return result;
    } catch (error) {
      console.error('Error getting wallet balance:', error.message);
      return this.getFallbackBalance();
    }
  }

  /**
   * Get comprehensive wallet information
   */
  async getWalletInfo() {
    try {
      console.log('Fetching wallet information...');
      const result = await this.executePythonCommand('get_wallet_info');
      
      if (result.error) {
        console.error('Wallet info error:', result.error);
        return this.getFallbackWalletInfo();
      }
      
      return result;
    } catch (error) {
      console.error('Error getting wallet info:', error.message);
      return this.getFallbackWalletInfo();
    }
  }

  /**
   * Get transaction history
   */
  async getTransactionHistory(limit = 10) {
    try {
      console.log(`Fetching ${limit} recent transactions...`);
      const result = await this.executePythonCommand('get_transaction_history', [limit]);
      
      if (result.error) {
        console.error('Transaction history error:', result.error);
        return [];
      }
      
      return Array.isArray(result) ? result : [];
    } catch (error) {
      console.error('Error getting transaction history:', error.message);
      return [];
    }
  }

  /**
   * Buy cryptocurrency
   */
  async buyCrypto(cryptoCurrency, amountFiat, fiatCurrency = 'USD') {
    try {
      console.log(`Buying ${cryptoCurrency} worth $${amountFiat} ${fiatCurrency}...`);
      const result = await this.executePythonCommand('buy_crypto', [cryptoCurrency, amountFiat, fiatCurrency]);
      
      if (result.error) {
        console.error('Buy crypto error:', result.error);
        return { success: false, error: result.error };
      }
      
      return { success: result === true, result };
    } catch (error) {
      console.error('Error buying crypto:', error.message);
      return { success: false, error: error.message };
    }
  }

  /**
   * Sell cryptocurrency
   */
  async sellCrypto(cryptoCurrency, amountCrypto, fiatCurrency = 'USD') {
    try {
      console.log(`Selling ${amountCrypto} ${cryptoCurrency} for ${fiatCurrency}...`);
      const result = await this.executePythonCommand('sell_crypto', [cryptoCurrency, amountCrypto, fiatCurrency]);
      
      if (result.error) {
        console.error('Sell crypto error:', result.error);
        return { success: false, error: result.error };
      }
      
      return { success: result === true, result };
    } catch (error) {
      console.error('Error selling crypto:', error.message);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get wallet address for receiving Bitcoin
   */
  async getWalletAddress() {
    try {
      console.log('Getting wallet address...');
      const result = await this.executePythonCommand('get_wallet_address');
      
      if (result.error) {
        console.error('Wallet address error:', result.error);
        return null;
      }
      
      return result;
    } catch (error) {
      console.error('Error getting wallet address:', error.message);
      return null;
    }
  }

  /**
   * Send Bitcoin to another address
   */
  async sendBitcoin(toAddress, amountUsd) {
    try {
      console.log(`Sending $${amountUsd} worth of Bitcoin to ${toAddress}...`);
      const result = await this.executePythonCommand('send_bitcoin', [toAddress, amountUsd]);
      
      if (result.error) {
        console.error('Send Bitcoin error:', result.error);
        return { success: false, error: result.error };
      }
      
      return { success: result === true, result };
    } catch (error) {
      console.error('Error sending Bitcoin:', error.message);
      return { success: false, error: error.message };
    }
  }

  /**
   * Fallback balance for when real wallet is unavailable
   */
  getFallbackBalance() {
    return {
      USD: 1000.0,
      BTC: 0.05,
      ETH: 2.3,
      ADA: 1000,
      SOL: 15,
      _fallback: true
    };
  }

  /**
   * Fallback wallet info
   */
  getFallbackWalletInfo() {
    return {
      balance: this.getFallbackBalance(),
      recent_transactions: [],
      wallet_address: 'bc1qfallback_demo_address_for_testing',
      wallet_status: 'demo_mode',
      dry_run: true,
      last_updated: new Date().toISOString(),
      _fallback: true
    };
  }

  /**
   * Check if wallet bridge is working
   */
  async healthCheck() {
    try {
      const balance = await this.getBalance();
      return {
        status: 'healthy',
        wallet_connected: !balance._fallback,
        balance_available: !!balance,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'error',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}

module.exports = WalletBridge;
