{"name": "multilogin", "version": "1.0.0", "description": "multimodal login", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@coinbase/coinbase-sdk": "^0.25.0", "@superfaceai/passport-twitter-oauth2": "^1.2.4", "axios": "^1.10.0", "bcrypt": "^6.0.0", "body-parser": "^2.2.0", "dotenv": "^16.5.0", "ejs": "^3.1.10", "express": "^5.1.0", "express-session": "^1.18.1", "jsonwebtoken": "^9.0.2", "nodemailer": "^7.0.3", "passport": "^0.7.0", "passport-apple": "^2.0.2", "passport-facebook": "^3.0.0", "passport-google-oauth20": "^2.0.0", "passport-local": "^1.0.0", "sqlite3": "^5.1.7", "uuid": "^11.1.0"}}