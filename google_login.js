const GoogleStrategy = require('passport-google-oauth20').Strategy;

/**
 * Google OAuth Login Module
 * Handles Google OAuth authentication strategy and routes
 */

class GoogleLogin {
  constructor(passport, db) {
    this.passport = passport;
    this.db = db;
    this.config = {
      clientID: process.env.GOOGLE_CLIENT_ID || 'GOOGLE_CLIENT_ID',
      clientSecret: process.env.GOOGLE_CLIENT_SECRET || 'GOOGLE_CLIENT_SECRET',
      callbackURL: process.env.GOOGLE_CALLBACK_URL || '/auth/google/callback'
    };
  }

  /**
   * Initialize Google OAuth strategy
   */
  initializeStrategy() {
    console.log('Initializing Google OAuth strategy...');
    
    this.passport.use('google', new GoogleStrategy({
      clientID: this.config.clientID,
      clientSecret: this.config.clientSecret,
      callbackURL: this.config.callbackURL,
      scope: ['profile', 'email']
    }, (accessToken, refreshToken, profile, done) => {
      console.log('Google OAuth callback triggered for user:', profile.id);
      this.handleOAuthCallback(accessToken, refreshToken, profile, done);
    }));
  }

  /**
   * Handle OAuth callback from Google
   */
  handleOAuthCallback(accessToken, refreshToken, profile, done) {
    const providerId = profile.id;
    const email = profile.emails && profile.emails[0] && profile.emails[0].value;
    const name = profile.displayName;

    console.log('Processing Google user:', { id: providerId, email, name });

    // Check if user already exists
    this.db.get(
      'SELECT * FROM users WHERE provider = ? AND provider_id = ?', 
      ['google', providerId], 
      (err, row) => {
        if (err) {
          console.error('Database error during Google user lookup:', err);
          return done(err);
        }

        if (row) {
          console.log('Existing Google user found:', row.id);
          return done(null, row);
        }

        // Check if user exists with same email but different provider
        this.db.get(
          'SELECT * FROM users WHERE email = ?',
          [email],
          (err, existingUser) => {
            if (err) {
              console.error('Database error during email lookup:', err);
              return done(err);
            }

            if (existingUser) {
              // Link Google account to existing user
              console.log('Linking Google account to existing user:', existingUser.id);
              this.db.run(
                'UPDATE users SET provider = ?, provider_id = ? WHERE id = ?',
                ['google', providerId, existingUser.id],
                (err) => {
                  if (err) {
                    console.error('Error linking Google account:', err);
                    return done(err);
                  }
                  
                  // Return updated user
                  this.db.get('SELECT * FROM users WHERE id = ?', existingUser.id, (e, updatedUser) => {
                    if (e) return done(e);
                    console.log('Successfully linked Google account');
                    done(null, updatedUser);
                  });
                }
              );
            } else {
              // Create new user
              console.log('Creating new Google user');
              this.createNewUser(email, providerId, name, done);
            }
          }
        );
      }
    );
  }

  /**
   * Create a new user from Google OAuth
   */
  createNewUser(email, providerId, name, done) {
    const db = this.db; // Capture db reference for use in callback
    db.run(
      'INSERT INTO users (email, provider, provider_id, name) VALUES (?, ?, ?, ?)',
      [email, 'google', providerId, name],
      function(err) {
        if (err) {
          console.error('Error creating Google user:', err);
          return done(err);
        }

        const newUserId = this.lastID;
        console.log('New Google user created with ID:', newUserId);

        // Fetch the newly created user
        db.get('SELECT * FROM users WHERE id = ?', newUserId, (e, newUser) => {
          if (e) {
            console.error('Error fetching newly created user:', e);
            return done(e);
          }
          if (!newUser) {
            console.error('Failed to create Google user - user not found after insertion');
            return done(null, false);
          }
          console.log('Successfully created and retrieved Google user');
          done(null, newUser);
        });
      }
    );
  }

  /**
   * Setup Google OAuth routes
   */
  setupRoutes(app) {
    console.log('Setting up Google OAuth routes...');

    // Initiate Google OAuth
    app.get('/auth/google', 
      this.passport.authenticate('google', { 
        scope: ['profile', 'email'] 
      })
    );

    // Google OAuth callback
    app.get('/auth/google/callback',
      this.passport.authenticate('google', {
        successRedirect: '/trading',
        failureRedirect: '/login',
        failureFlash: false
      })
    );

    console.log('Google OAuth routes configured');
  }

  /**
   * Get configuration status
   */
  getConfigStatus() {
    return {
      provider: 'google',
      configured: this.config.clientID !== 'GOOGLE_CLIENT_ID' && 
                  this.config.clientSecret !== 'GOOGLE_CLIENT_SECRET',
      clientID: this.config.clientID.substring(0, 10) + '...',
      callbackURL: this.config.callbackURL
    };
  }
}

module.exports = GoogleLogin;
