# Multilogin

This sample project demonstrates a multi-modal authentication system supporting:

- Email and password login with password reset
- Social logins via Google, Facebook, Apple and X/Twitter

The implementation uses Node.js with Express and Passport for handling authentication strategies.

## Setup

1. Install dependencies

```bash
npm install
```

2. Configure OAuth credentials by replacing the placeholders in `server.js` with your provider keys.

3. Start the server

```bash
npm start
```

The server will run on http://localhost:3000 and provides simple web pages for login and registration. The password reset flow sends a link via email using a nodemailer transport configured in `server.js`.

This code is meant as a starting point and may require additional security and production hardening.
