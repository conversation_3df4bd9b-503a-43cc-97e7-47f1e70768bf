# AI Cryptocurrency Trading Agent

An intelligent cryptocurrency trading agent built with Coinbase AgentKit that executes automated buy/sell operations based on technical analysis and market data.

## 🚀 Features

- **Automated Trading**: Execute buy/sell orders based on AI-driven market analysis
- **Multiple Strategies**: Trend following, RSI-based, and mean reversion strategies
- **Risk Management**: Configurable stop-loss, take-profit, and position sizing
- **Real-time Monitoring**: CLI interface for live monitoring and control
- **Safety Features**: Daily trade limits, dry-run mode, and comprehensive logging
- **Market Data**: Real-time price feeds from multiple sources
- **Wallet Management**: Balance tracking and transaction history

## 📋 Prerequisites

- Python 3.8 or higher
- Coinbase Developer Platform (CDP) API credentials
- Basic understanding of cryptocurrency trading

## 🛠️ Installation

1. **Clone or create the project directory**
```bash
mkdir crypto-trading-agent
cd crypto-trading-agent
