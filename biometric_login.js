const crypto = require('crypto');

/**
 * Biometric/Passkey Login Module
 * Handles WebAuthn/FIDO2 passkey authentication
 * Requires users to first authenticate with traditional credentials
 */

class BiometricLogin {
  constructor(passport, db) {
    this.passport = passport;
    this.db = db;
    this.rpName = process.env.RP_NAME || 'MultiLogin App';
    this.rpId = process.env.RP_ID || 'localhost';
    this.origin = process.env.ORIGIN || 'http://localhost:3000';
  }

  /**
   * Initialize biometric authentication routes
   */
  setupRoutes(app) {
    console.log('Setting up Biometric/Passkey authentication routes...');

    // Register a new passkey for authenticated user
    app.post('/auth/biometric/register/begin', this.requireAuth.bind(this), this.beginRegistration.bind(this));
    app.post('/auth/biometric/register/complete', this.requireAuth.bind(this), this.completeRegistration.bind(this));

    // Authenticate with passkey
    app.post('/auth/biometric/authenticate/begin', this.beginAuthentication.bind(this));
    app.post('/auth/biometric/authenticate/complete', this.completeAuthentication.bind(this));

    // Check if user has passkeys (authenticated users only)
    app.get('/auth/biometric/status', this.requireAuth.bind(this), this.getPasskeyStatus.bind(this));

    // Check if any passkeys exist for login page (public endpoint)
    app.get('/auth/biometric/available', this.checkPasskeysAvailable.bind(this));

    // Remove passkey
    app.delete('/auth/biometric/remove/:id', this.requireAuth.bind(this), this.removePasskey.bind(this));

    console.log('Biometric/Passkey authentication routes configured');
  }

  /**
   * Middleware to require authentication
   */
  requireAuth(req, res, next) {
    if (req.isAuthenticated()) {
      return next();
    }
    res.status(401).json({ error: 'Authentication required' });
  }

  /**
   * Begin passkey registration process
   */
  async beginRegistration(req, res) {
    try {
      const user = req.user;
      const challenge = crypto.randomBytes(32);
      
      // Store challenge in session
      req.session.registrationChallenge = challenge.toString('base64url');
      
      const publicKeyCredentialCreationOptions = {
        challenge: challenge.toString('base64url'),
        rp: {
          name: this.rpName,
          id: this.rpId,
        },
        user: {
          id: Buffer.from(user.id.toString()).toString('base64url'),
          name: user.email,
          displayName: user.name || user.email,
        },
        pubKeyCredParams: [
          { alg: -7, type: 'public-key' }, // ES256
          { alg: -257, type: 'public-key' }, // RS256
        ],
        authenticatorSelection: {
          authenticatorAttachment: 'platform',
          userVerification: 'required',
          residentKey: 'preferred',
        },
        timeout: 60000,
        attestation: 'direct',
      };

      console.log('Passkey registration initiated for user:', user.id);
      res.json(publicKeyCredentialCreationOptions);
    } catch (error) {
      console.error('Error beginning passkey registration:', error);
      res.status(500).json({ error: 'Failed to begin registration' });
    }
  }

  /**
   * Complete passkey registration
   */
  async completeRegistration(req, res) {
    try {
      const user = req.user;
      const { credential } = req.body;
      const storedChallenge = req.session.registrationChallenge;

      if (!storedChallenge) {
        return res.status(400).json({ error: 'No registration challenge found' });
      }

      // Basic validation (in production, use a proper WebAuthn library)
      if (!credential || !credential.id || !credential.response) {
        return res.status(400).json({ error: 'Invalid credential data' });
      }

      // Store the passkey in database
      const passkeyData = {
        credentialId: credential.id,
        publicKey: credential.response.publicKey,
        counter: 0,
        userId: user.id,
        createdAt: new Date().toISOString(),
        name: req.body.name || 'Biometric Key'
      };

      this.db.run(
        `INSERT INTO passkeys (credential_id, public_key, counter, user_id, created_at, name) 
         VALUES (?, ?, ?, ?, ?, ?)`,
        [
          passkeyData.credentialId,
          JSON.stringify(passkeyData.publicKey),
          passkeyData.counter,
          passkeyData.userId,
          passkeyData.createdAt,
          passkeyData.name
        ],
        function(err) {
          if (err) {
            console.error('Error storing passkey:', err);
            return res.status(500).json({ error: 'Failed to store passkey' });
          }

          console.log('Passkey registered successfully for user:', user.id);
          delete req.session.registrationChallenge;
          res.json({ success: true, id: this.lastID });
        }
      );
    } catch (error) {
      console.error('Error completing passkey registration:', error);
      res.status(500).json({ error: 'Failed to complete registration' });
    }
  }

  /**
   * Begin passkey authentication
   */
  async beginAuthentication(req, res) {
    try {
      const challenge = crypto.randomBytes(32);
      
      // Store challenge in session
      req.session.authenticationChallenge = challenge.toString('base64url');

      const publicKeyCredentialRequestOptions = {
        challenge: challenge.toString('base64url'),
        timeout: 60000,
        rpId: this.rpId,
        userVerification: 'required',
      };

      console.log('Passkey authentication initiated');
      res.json(publicKeyCredentialRequestOptions);
    } catch (error) {
      console.error('Error beginning passkey authentication:', error);
      res.status(500).json({ error: 'Failed to begin authentication' });
    }
  }

  /**
   * Complete passkey authentication
   */
  async completeAuthentication(req, res) {
    try {
      const { credential } = req.body;
      const storedChallenge = req.session.authenticationChallenge;

      if (!storedChallenge) {
        return res.status(400).json({ error: 'No authentication challenge found' });
      }

      if (!credential || !credential.id) {
        return res.status(400).json({ error: 'Invalid credential data' });
      }

      // Find the passkey in database
      this.db.get(
        'SELECT p.*, u.* FROM passkeys p JOIN users u ON p.user_id = u.id WHERE p.credential_id = ?',
        [credential.id],
        (err, row) => {
          if (err) {
            console.error('Database error during passkey lookup:', err);
            return res.status(500).json({ error: 'Database error' });
          }

          if (!row) {
            console.log('Passkey not found:', credential.id);
            return res.status(401).json({ error: 'Passkey not recognized. Please register a passkey first.' });
          }

          // In production, verify the signature here using a proper WebAuthn library
          // For now, we'll trust the client-side verification

          // Log the user in
          const user = {
            id: row.user_id, // Use user_id from passkeys table
            email: row.email,
            name: row.name,
            provider: row.provider,
            provider_id: row.provider_id
          };

          req.login(user, (err) => {
            if (err) {
              console.error('Error logging in user with passkey:', err);
              return res.status(500).json({ error: 'Login failed' });
            }

            console.log('User authenticated successfully with passkey:', user.id);
            delete req.session.authenticationChallenge;
            res.json({ success: true, user: { id: user.id, email: user.email, name: user.name } });
          });
        }
      );
    } catch (error) {
      console.error('Error completing passkey authentication:', error);
      res.status(500).json({ error: 'Failed to complete authentication' });
    }
  }

  /**
   * Get passkey status for current user
   */
  getPasskeyStatus(req, res) {
    const userId = req.user.id;

    this.db.all(
      'SELECT id, credential_id, name, created_at FROM passkeys WHERE user_id = ?',
      [userId],
      (err, rows) => {
        if (err) {
          console.error('Error fetching passkeys:', err);
          return res.status(500).json({ error: 'Database error' });
        }

        res.json({
          hasPasskeys: rows.length > 0,
          passkeys: rows.map(row => ({
            id: row.id,
            name: row.name,
            createdAt: row.created_at
          }))
        });
      }
    );
  }

  /**
   * Check if any passkeys are available (for login page)
   */
  checkPasskeysAvailable(req, res) {
    this.db.get(
      'SELECT COUNT(*) as count FROM passkeys',
      (err, row) => {
        if (err) {
          console.error('Error checking passkey availability:', err);
          return res.status(500).json({ error: 'Database error' });
        }

        res.json({
          available: row.count > 0,
          count: row.count
        });
      }
    );
  }

  /**
   * Remove a passkey
   */
  removePasskey(req, res) {
    const passkeyId = req.params.id;
    const userId = req.user.id;

    this.db.run(
      'DELETE FROM passkeys WHERE id = ? AND user_id = ?',
      [passkeyId, userId],
      function(err) {
        if (err) {
          console.error('Error removing passkey:', err);
          return res.status(500).json({ error: 'Failed to remove passkey' });
        }

        if (this.changes === 0) {
          return res.status(404).json({ error: 'Passkey not found' });
        }

        console.log('Passkey removed:', passkeyId);
        res.json({ success: true });
      }
    );
  }

  /**
   * Initialize passkeys table
   */
  initializeDatabase() {
    this.db.run(`
      CREATE TABLE IF NOT EXISTS passkeys (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        credential_id TEXT UNIQUE NOT NULL,
        public_key TEXT NOT NULL,
        counter INTEGER DEFAULT 0,
        user_id INTEGER NOT NULL,
        name TEXT,
        created_at TEXT NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `, (err) => {
      if (err) {
        console.error('Error creating passkeys table:', err);
      } else {
        console.log('Passkeys table initialized');
      }
    });
  }
}

module.exports = BiometricLogin;
