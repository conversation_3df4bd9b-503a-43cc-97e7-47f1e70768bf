"""
Autonomous AI Trading Agent for Bitcoin accumulation
"""
import asyncio
import json
import os
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import threading
from dataclasses import dataclass

from config import Config
from wallet_manager import WalletManager
from market_data import MarketDataProvider
from trading_strategy import TradingStrategy
from database import get_db_manager
from logger_config import setup_logger


@dataclass
class AgentPersonality:
    """Agent personality and characteristics"""
    name: str = "BitHunter"
    mode: str = "autonomous"  # "autonomous" or "interactive"
    avatar_path: Optional[str] = None
    risk_tolerance: float = 0.7  # 0.0 to 1.0
    aggression_level: float = 0.8  # How aggressive in accumulating BTC
    learning_rate: float = 0.1  # How quickly it adapts strategies
    confidence_threshold: float = 0.6  # Minimum confidence for trades


class AutonomousAgent:
    """Advanced AI Trading Agent for Bitcoin accumulation"""
    
    def __init__(self, config: Config, wallet_manager: WalletManager, 
                 market_data: MarketDataProvider, strategy: TradingStrategy):
        self.config = config
        self.wallet_manager = wallet_manager
        self.market_data = market_data
        self.strategy = strategy
        self.db_manager = get_db_manager()
        self.logger = setup_logger("AutonomousAgent")
        
        # Agent state
        self.is_running = False
        self.agent_thread = None
        self.personality = AgentPersonality()
        
        # Performance tracking
        self.session_start_time = None
        self.session_start_balance = 0.0
        self.total_trades_executed = 0
        self.successful_trades = 0
        self.btc_accumulated = 0.0
        self.profit_loss = 0.0
        
        # Advanced trading features
        self.market_analysis_interval = 15  # seconds
        self.price_alerts = []
        self.trading_opportunities = []
        self.last_analysis_time = None
        
        # Interactive mode features
        self.pending_trades = []  # Store trades awaiting approval
        
        # AI decision-making parameters
        self.decision_history = []
        self.market_sentiment = "neutral"
        self.volatility_threshold = 0.02  # 2% volatility trigger
        
    def set_personality(self, name: str, mode: str = "autonomous", avatar_path: Optional[str] = None, 
                       risk_tolerance: float = 0.7, aggression_level: float = 0.8):
        """Configure agent personality"""
        self.personality.name = name
        self.personality.mode = mode
        self.personality.avatar_path = avatar_path
        self.personality.risk_tolerance = max(0.0, min(1.0, risk_tolerance))
        self.personality.aggression_level = max(0.0, min(1.0, aggression_level))
        
        self.logger.info(f"Agent personality configured: {name} in {mode} mode (Risk: {risk_tolerance}, Aggression: {aggression_level})")
    
    def start_agent(self):
        """Start the autonomous trading agent"""
        if self.is_running:
            self.logger.warning("Agent is already running")
            return False
            
        self.is_running = True
        self.session_start_time = datetime.now()
        
        # Get starting balance
        balance = self.wallet_manager.get_balance()
        if balance:
            self.session_start_balance = balance.get('BTC', 0.0)
        
        # Start agent in separate thread
        self.agent_thread = threading.Thread(target=self._agent_loop, daemon=True)
        self.agent_thread.start()
        
        self.logger.info(f"Autonomous agent '{self.personality.name}' started")
        return True
    
    def stop_agent(self):
        """Stop the autonomous trading agent"""
        self.is_running = False
        if self.agent_thread:
            self.agent_thread.join(timeout=5)
        
        self.logger.info(f"Autonomous agent '{self.personality.name}' stopped")
        return True
    
    def _agent_loop(self):
        """Main agent loop for autonomous operation"""
        self.logger.info("Starting autonomous trading loop")
        
        while self.is_running:
            try:
                # Perform market analysis
                self._perform_market_analysis()
                
                # Make trading decisions
                self._make_trading_decisions()
                
                # Update performance metrics
                self._update_performance_metrics()
                
                # Adaptive learning
                self._adaptive_learning()
                
                # Wait before next iteration
                time.sleep(self.market_analysis_interval)
                
            except Exception as e:
                self.logger.error(f"Error in agent loop: {str(e)}")
                time.sleep(30)  # Wait longer on error
    
    def _perform_market_analysis(self):
        """Advanced market analysis for trading opportunities"""
        try:
            # Get current market data
            current_data = self.market_data.get_current_data(self.config.TRADING_PAIR)
            if not current_data:
                return
            
            # Analyze market trends
            historical_data = self.market_data.get_historical_data(self.config.TRADING_PAIR, days=7)
            if historical_data is not None and len(historical_data) > 0:
                # Calculate volatility
                price_changes = historical_data['close'].pct_change().dropna()
                volatility = price_changes.std()
                
                # Determine market sentiment
                recent_trend = historical_data['close'].tail(24).mean()
                current_price = current_data['price']
                
                if current_price > recent_trend * 1.02:
                    self.market_sentiment = "bullish"
                elif current_price < recent_trend * 0.98:
                    self.market_sentiment = "bearish"
                else:
                    self.market_sentiment = "neutral"
                
                # Identify opportunities based on volatility and sentiment
                if volatility > self.volatility_threshold:
                    opportunity = {
                        'timestamp': datetime.now(),
                        'type': 'high_volatility',
                        'sentiment': self.market_sentiment,
                        'volatility': volatility,
                        'price': current_price,
                        'confidence': min(1.0, volatility * 10)  # Higher volatility = more opportunity
                    }
                    self.trading_opportunities.append(opportunity)
                    
                    # Keep only recent opportunities
                    cutoff_time = datetime.now() - timedelta(hours=1)
                    self.trading_opportunities = [
                        opp for opp in self.trading_opportunities 
                        if opp['timestamp'] > cutoff_time
                    ]
            
            self.last_analysis_time = datetime.now()
            
        except Exception as e:
            self.logger.error(f"Error in market analysis: {str(e)}")
    
    def _make_trading_decisions(self):
        """AI-driven trading decision making"""
        try:
            # Get wallet balance
            balance = self.wallet_manager.get_balance()
            if not balance:
                return
            
            # Get current market data
            current_data = self.market_data.get_current_data(self.config.TRADING_PAIR)
            if not current_data:
                return
            
            # Generate trading signal using existing strategy
            signal = self.strategy.analyze_market(current_data)
            if not signal:
                return
            
            # Apply AI enhancements to signal
            enhanced_signal = self._enhance_signal_with_ai(signal, current_data, balance)
            
            if enhanced_signal and enhanced_signal['confidence'] >= self.personality.confidence_threshold:
                # Execute trade based on enhanced signal
                self._execute_autonomous_trade(enhanced_signal, balance, current_data)
                
        except Exception as e:
            self.logger.error(f"Error in trading decisions: {str(e)}")
    
    def _enhance_signal_with_ai(self, signal: Dict[str, Any], market_data: Dict[str, Any], 
                               balance: Dict[str, float]) -> Optional[Dict[str, Any]]:
        """Enhance trading signal with AI analysis"""
        try:
            enhanced_signal = signal.copy()
            
            # Factor in market sentiment
            sentiment_multiplier = 1.0
            if self.market_sentiment == "bullish" and signal['action'] == 'buy':
                sentiment_multiplier = 1.2
            elif self.market_sentiment == "bearish" and signal['action'] == 'sell':
                sentiment_multiplier = 1.2
            elif self.market_sentiment == "bullish" and signal['action'] == 'sell':
                sentiment_multiplier = 0.8
            elif self.market_sentiment == "bearish" and signal['action'] == 'buy':
                sentiment_multiplier = 0.8
            
            # Apply personality factors
            risk_adjustment = self.personality.risk_tolerance
            aggression_adjustment = self.personality.aggression_level
            
            # Calculate enhanced confidence
            base_confidence = signal['confidence']
            enhanced_confidence = base_confidence * sentiment_multiplier * risk_adjustment
            
            # Adjust trade size based on aggression level
            if signal['action'] == 'buy':
                # More aggressive = larger buy orders when confident
                size_multiplier = 1.0 + (aggression_adjustment - 0.5) * enhanced_confidence
            else:
                # More aggressive = larger sell orders to accumulate cash for better buys
                size_multiplier = 1.0 + (aggression_adjustment - 0.5) * 0.5
            
            enhanced_signal['confidence'] = min(1.0, enhanced_confidence)
            enhanced_signal['size_multiplier'] = max(0.1, min(2.0, size_multiplier))
            enhanced_signal['ai_reasoning'] = f"Sentiment: {self.market_sentiment}, Risk: {risk_adjustment}, Aggression: {aggression_adjustment}"
            
            return enhanced_signal
            
        except Exception as e:
            self.logger.error(f"Error enhancing signal: {str(e)}")
            return signal
    
    def _execute_autonomous_trade(self, signal: Dict[str, Any], balance: Dict[str, float], 
                                 market_data: Dict[str, Any]):
        """Execute trading decision based on mode (autonomous or interactive)"""
        try:
            action = signal['action']
            confidence = signal['confidence']
            size_multiplier = signal.get('size_multiplier', 1.0)
            
            # Calculate trade amount
            if action == 'buy':
                available_usd = balance.get('USD', 0)
                base_trade_amount = available_usd * 0.1  # 10% of available USD
                trade_amount = base_trade_amount * confidence * size_multiplier
                trade_amount = max(10, min(trade_amount, available_usd * 0.5))  # Min $10, max 50% of USD
            else:  # sell
                available_btc = balance.get('BTC', 0)
                trade_amount = available_btc * 0.1 * confidence * size_multiplier  # 10% of BTC holdings
                trade_amount = max(0.0001, min(trade_amount, available_btc * 0.5))  # Min 0.0001 BTC, max 50%
            
            # Create decision record
            decision_record = {
                'id': len(self.decision_history) + 1,
                'timestamp': datetime.utcnow().isoformat(),
                'action': action,
                'confidence': confidence,
                'amount_usd': trade_amount if action == 'buy' else trade_amount * market_data.get('price', 1),
                'amount_btc': trade_amount / market_data.get('price', 1) if action == 'buy' else trade_amount,
                'price': market_data.get('price', 0.0),
                'reasoning': signal.get('ai_reasoning', signal.get('reasoning', '')),
                'executed': False,
                'status': 'pending' if self.personality.mode == 'interactive' else 'executed'
            }
            
            if self.personality.mode == 'interactive':
                # Queue trade for approval in interactive mode
                self.pending_trades.append(decision_record)
                self.logger.info(f"Trade queued for approval: {action} ${decision_record['amount_usd']:.2f} (ID: {decision_record['id']})")
                decision_record['executed'] = False
            else:
                # Execute immediately in autonomous mode
                self.total_trades_executed += 1
                success = self._execute_trade_directly(action, trade_amount, balance, market_data)
                decision_record['executed'] = success
                decision_record['status'] = 'executed' if success else 'failed'
                
                if success:
                    self.successful_trades += 1
            
            self.decision_history.append(decision_record)
            
            # Keep only recent decisions
            if len(self.decision_history) > 100:
                self.decision_history = self.decision_history[-100:]
                
        except Exception as e:
            self.logger.error(f"Error handling trade decision: {e}")
    
    def _execute_trade_directly(self, action: str, trade_amount: float, balance: Dict[str, float], 
                               market_data: Dict[str, Any]) -> bool:
        """Execute a trade directly (used in autonomous mode)"""
        try:
            if action == 'buy':
                success = self.wallet_manager.buy_crypto('BTC', trade_amount, 'USD')
                if success:
                    btc_bought = trade_amount / market_data['price']
                    self.btc_accumulated += btc_bought
                    self.logger.info(f"Autonomous BUY: ${trade_amount:.2f} worth of BTC")
                return success
                
            elif action == 'sell':
                success = self.wallet_manager.sell_crypto('BTC', trade_amount, 'USD')
                if success:
                    self.logger.info(f"Autonomous SELL: {trade_amount:.8f} BTC")
                return success
                
            return False
            
        except Exception as e:
            self.logger.error(f"Error executing trade directly: {e}")
            return False
    
    def _update_performance_metrics(self):
        """Update agent performance metrics"""
        try:
            current_balance = self.wallet_manager.get_balance()
            if current_balance and self.session_start_time:
                current_btc = current_balance.get('BTC', 0.0)
                self.btc_accumulated = current_btc - self.session_start_balance
                
                # Calculate profit/loss in USD
                current_data = self.market_data.get_current_data(self.config.TRADING_PAIR)
                if current_data:
                    current_portfolio_value = (
                        current_balance.get('USD', 0) + 
                        current_btc * current_data['price']
                    )
                    
                    start_portfolio_value = (
                        current_balance.get('USD', 0) + 
                        self.session_start_balance * current_data['price']
                    )
                    
                    self.profit_loss = current_portfolio_value - start_portfolio_value
                    
        except Exception as e:
            self.logger.error(f"Error updating performance metrics: {str(e)}")
    
    def _adaptive_learning(self):
        """Adaptive learning based on trading performance"""
        try:
            if len(self.decision_history) >= 10:
                recent_decisions = self.decision_history[-10:]
                success_rate = sum(1 for d in recent_decisions if d['success']) / len(recent_decisions)
                
                # Adjust confidence threshold based on success rate
                if success_rate > 0.8:
                    # High success rate - can be more aggressive
                    self.personality.confidence_threshold = max(0.4, self.personality.confidence_threshold - 0.1)
                elif success_rate < 0.6:
                    # Low success rate - be more conservative
                    self.personality.confidence_threshold = min(0.9, self.personality.confidence_threshold + 0.1)
                
                # Adjust market analysis interval based on volatility
                if len(self.trading_opportunities) > 5:
                    # High volatility - analyze more frequently
                    self.market_analysis_interval = max(5, self.market_analysis_interval - 2)
                else:
                    # Low volatility - analyze less frequently
                    self.market_analysis_interval = min(60, self.market_analysis_interval + 2)
                    
        except Exception as e:
            self.logger.error(f"Error in adaptive learning: {str(e)}")
    
    def get_agent_status(self) -> Dict[str, Any]:
        """Get comprehensive agent status"""
        try:
            runtime = None
            if self.session_start_time:
                runtime = str(datetime.now() - self.session_start_time).split('.')[0]
            
            success_rate = 0.0
            if self.total_trades_executed > 0:
                success_rate = (self.successful_trades / self.total_trades_executed) * 100
            
            return {
                'name': self.personality.name,
                'avatar_path': self.personality.avatar_path,
                'is_running': self.is_running,
                'runtime': runtime,
                'session_start': self.session_start_time.isoformat() if self.session_start_time else None,
                'total_trades': self.total_trades_executed,
                'successful_trades': self.successful_trades,
                'success_rate': round(success_rate, 2),
                'btc_accumulated': round(self.btc_accumulated, 8),
                'profit_loss_usd': round(self.profit_loss, 2),
                'market_sentiment': self.market_sentiment,
                'confidence_threshold': round(self.personality.confidence_threshold, 2),
                'risk_tolerance': round(self.personality.risk_tolerance, 2),
                'aggression_level': round(self.personality.aggression_level, 2),
                'trading_opportunities': len(self.trading_opportunities),
                'last_analysis': self.last_analysis_time.isoformat() if self.last_analysis_time else None,
                'analysis_interval': self.market_analysis_interval
            }
            
        except Exception as e:
            self.logger.error(f"Error getting agent status: {str(e)}")
            return {'error': str(e)}
    
    def get_recent_decisions(self, limit: int = 20) -> List[Dict[str, Any]]:
        """Get recent trading decisions"""
        try:
            recent = self.decision_history[-limit:] if self.decision_history else []
            return [
                {
                    **decision,
                    'timestamp': decision['timestamp'].isoformat()
                } for decision in recent
            ]
        except Exception as e:
            self.logger.error(f"Error getting recent decisions: {str(e)}")
            return []
    
    def force_market_analysis(self):
        """Force immediate market analysis"""
        try:
            self._perform_market_analysis()
            return True
        except Exception as e:
            self.logger.error(f"Error in forced market analysis: {str(e)}")
            return False
    
    def update_personality_settings(self, risk_tolerance: float = None, 
                                  aggression_level: float = None):
        """Update personality settings while running"""
        try:
            if risk_tolerance is not None:
                self.personality.risk_tolerance = max(0.0, min(1.0, risk_tolerance))
            
            if aggression_level is not None:
                self.personality.aggression_level = max(0.0, min(1.0, aggression_level))
            
            self.logger.info(f"Updated personality: Risk={self.personality.risk_tolerance}, Aggression={self.personality.aggression_level}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating personality: {str(e)}")
            return False
    
    def get_pending_trades(self) -> List[Dict[str, Any]]:
        """Get trades awaiting approval"""
        return [trade for trade in self.pending_trades if trade.get('status') == 'pending']
    
    def approve_trade(self, trade_id: int) -> bool:
        """Approve and execute a pending trade"""
        try:
            trade = next((t for t in self.pending_trades if t.get('id') == trade_id), None)
            if not trade or trade.get('status') != 'pending':
                return False
            
            action = trade.get('action')
            amount_usd = trade.get('amount_usd', 0.0)
            
            if action == 'buy':
                success = self.trading_agent.force_trade_signal('buy', amount_usd)
            elif action == 'sell':
                success = self.trading_agent.force_trade_signal('sell', amount_usd)
            else:
                return False
            
            # Update trade status
            trade['executed'] = success
            trade['status'] = 'executed' if success else 'failed'
            trade['executed_at'] = datetime.utcnow().isoformat()
            
            if success:
                self.total_trades_executed += 1
                self.successful_trades += 1
                self.logger.info(f"Approved and executed {action} trade for ${amount_usd:.2f}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Error approving trade: {e}")
            return False
    
    def reject_trade(self, trade_id: int) -> bool:
        """Reject a pending trade"""
        try:
            trade = next((t for t in self.pending_trades if t.get('id') == trade_id), None)
            if not trade or trade.get('status') != 'pending':
                return False
            
            trade['status'] = 'rejected'
            trade['rejected_at'] = datetime.utcnow().isoformat()
            self.logger.info(f"Rejected trade ID {trade_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error rejecting trade: {e}")
            return False


# Global agent instance
_autonomous_agent_instance = None

def get_autonomous_agent(config: Config = None, wallet_manager: WalletManager = None,
                        market_data: MarketDataProvider = None, strategy: TradingStrategy = None) -> AutonomousAgent:
    """Get or create the global autonomous agent instance"""
    global _autonomous_agent_instance
    
    if _autonomous_agent_instance is None:
        if not all([config, wallet_manager, market_data, strategy]):
            raise ValueError("All parameters required for first initialization")
        _autonomous_agent_instance = AutonomousAgent(config, wallet_manager, market_data, strategy)
    
    return _autonomous_agent_instance