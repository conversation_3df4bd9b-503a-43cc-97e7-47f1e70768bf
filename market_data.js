/**
 * Market data provider for cryptocurrency prices and analysis
 * JavaScript implementation for Node.js integration
 */

const axios = require('axios');

class MarketDataProvider {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 60000; // Cache data for 1 minute (60 seconds)

    // API endpoints
    this.coinbaseProApi = 'https://api.exchange.coinbase.com';
    this.coingeckoApi = 'https://api.coingecko.com/api/v3';
    this.coinApiUrl = 'https://rest.coinapi.io/v1';
    this.coinApiKey = process.env.COIN_API_KEY;

    // Symbol mapping for CoinGecko
    this.symbolMap = {
      'btc': 'bitcoin',
      'eth': 'ethereum',
      'ada': 'cardano',
      'dot': 'polkadot',
      'sol': 'solana',
      'matic': 'polygon',
      'avax': 'avalanche-2',
      'link': 'chainlink',
      'uni': 'uniswap',
      'ltc': 'litecoin'
    };

    // Supported currencies with their symbols and names
    this.supportedCurrencies = {
      'USD': { name: 'US Dollar', symbol: '$', flag: '🇺🇸' },
      'EUR': { name: 'Euro', symbol: '€', flag: '🇪🇺' },
      'GBP': { name: 'British Pound', symbol: '£', flag: '🇬🇧' },
      'AUD': { name: 'Australian Dollar', symbol: 'A$', flag: '🇦🇺' },
      'CAD': { name: 'Canadian Dollar', symbol: 'C$', flag: '🇨🇦' },
      'JPY': { name: 'Japanese Yen', symbol: '¥', flag: '🇯🇵' },
      'CHF': { name: 'Swiss Franc', symbol: 'CHF', flag: '🇨🇭' },
      'CNY': { name: 'Chinese Yuan', symbol: '¥', flag: '🇨🇳' },
      'INR': { name: 'Indian Rupee', symbol: '₹', flag: '🇮🇳' },
      'KRW': { name: 'South Korean Won', symbol: '₩', flag: '🇰🇷' }
    };

    console.log('Market data provider initialized');
    console.log(`CoinAPI.io integration: ${this.coinApiKey ? '✅ Enabled' : '❌ Disabled (no API key)'}`);
    console.log(`Supported currencies: ${Object.keys(this.supportedCurrencies).join(', ')}`);
  }

  /**
   * Get current market data for a trading pair
   */
  async getCurrentData(tradingPair) {
    try {
      // Check cache first
      const cacheKey = `current_${tradingPair}`;
      if (this.isCacheValid(cacheKey)) {
        console.log(`Returning cached data for ${tradingPair}`);
        return this.cache.get(cacheKey).data;
      }

      // Fetch fresh data
      console.log(`Fetching fresh market data for ${tradingPair}`);
      const data = await this.fetchCurrentData(tradingPair);
      
      if (data) {
        // Cache the data
        this.cache.set(cacheKey, {
          data: data,
          timestamp: Date.now()
        });
        return data;
      }

      return null;
    } catch (error) {
      console.error(`Error getting current market data: ${error.message}`);
      return null;
    }
  }

  /**
   * Get current prices for multiple cryptocurrencies
   */
  async getCurrentPrices(symbols = ['BTC', 'ETH', 'ADA', 'SOL']) {
    try {
      const prices = {};

      // Try to get all prices in parallel
      const promises = symbols.map(async (symbol) => {
        try {
          const tradingPair = `${symbol}-USD`;
          const data = await this.getCurrentData(tradingPair);
          if (data && data.price) {
            prices[symbol] = data.price;
          }
        } catch (error) {
          console.error(`Error getting price for ${symbol}: ${error.message}`);
        }
      });

      await Promise.all(promises);

      // If we didn't get any real prices, return fallback data
      if (Object.keys(prices).length === 0) {
        console.log('No real prices available, using fallback data');
        return this.getFallbackPrices(symbols);
      }

      console.log(`Successfully fetched prices for: ${Object.keys(prices).join(', ')}`);
      return prices;
    } catch (error) {
      console.error(`Error getting current prices: ${error.message}`);
      return this.getFallbackPrices(symbols);
    }
  }

  /**
   * Get current prices in multiple currencies using CoinAPI.io
   */
  async getCurrentPricesMultiCurrency(symbols = ['BTC', 'ETH', 'ADA', 'SOL'], currencies = ['USD', 'EUR', 'GBP']) {
    try {
      console.log(`Fetching prices for ${symbols.join(', ')} in ${currencies.join(', ')}`);

      if (!this.coinApiKey) {
        console.log('CoinAPI.io key not available, falling back to single currency');
        const usdPrices = await this.getCurrentPrices(symbols);
        return this.convertToMultiCurrency(usdPrices, currencies);
      }

      const results = {};

      // Initialize results structure
      symbols.forEach(symbol => {
        results[symbol] = {};
        currencies.forEach(currency => {
          results[symbol][currency] = null;
        });
      });

      // Fetch prices for each symbol-currency pair
      const promises = [];

      symbols.forEach(symbol => {
        currencies.forEach(currency => {
          promises.push(
            this.fetchCoinApiPrice(symbol, currency)
              .then(price => {
                if (price !== null) {
                  results[symbol][currency] = price;
                }
              })
              .catch(error => {
                console.error(`Error fetching ${symbol}-${currency}: ${error.message}`);
              })
          );
        });
      });

      await Promise.all(promises);

      // Fill in missing prices with conversions if we have USD prices
      await this.fillMissingPrices(results, symbols, currencies);

      console.log(`Multi-currency prices fetched successfully`);
      return results;

    } catch (error) {
      console.error(`Error getting multi-currency prices: ${error.message}`);
      // Fallback to single currency conversion
      const usdPrices = await this.getCurrentPrices(symbols);
      return this.convertToMultiCurrency(usdPrices, currencies);
    }
  }

  /**
   * Fetch price from CoinAPI.io
   */
  async fetchCoinApiPrice(symbol, currency) {
    try {
      const cacheKey = `coinapi_${symbol}_${currency}`;

      // Check cache first
      if (this.isCacheValid(cacheKey)) {
        const cached = this.cache.get(cacheKey);
        return cached.data.price;
      }

      const url = `${this.coinApiUrl}/exchangerate/${symbol}/${currency}`;
      const headers = {
        'X-CoinAPI-Key': this.coinApiKey,
        'Accept': 'application/json'
      };

      const response = await axios.get(url, {
        headers,
        timeout: 10000
      });

      if (response.data && response.data.rate) {
        const price = parseFloat(response.data.rate);

        // Cache the result
        this.cache.set(cacheKey, {
          data: { price: price, source: 'coinapi' },
          timestamp: Date.now()
        });

        return price;
      }

      return null;
    } catch (error) {
      console.error(`CoinAPI.io error for ${symbol}-${currency}: ${error.message}`);
      return null;
    }
  }

  /**
   * Fill missing prices using currency conversion
   */
  async fillMissingPrices(results, symbols, currencies) {
    try {
      // Get USD exchange rates for conversion
      const usdRates = {};

      for (const currency of currencies) {
        if (currency !== 'USD') {
          try {
            const rate = await this.fetchCoinApiPrice('USD', currency);
            if (rate) {
              usdRates[currency] = rate;
            }
          } catch (error) {
            console.error(`Error getting USD-${currency} rate: ${error.message}`);
          }
        }
      }

      // Fill missing prices
      symbols.forEach(symbol => {
        currencies.forEach(currency => {
          if (results[symbol][currency] === null) {
            // Try to convert from USD price
            if (currency !== 'USD' && results[symbol]['USD'] && usdRates[currency]) {
              results[symbol][currency] = results[symbol]['USD'] * usdRates[currency];
              console.log(`Converted ${symbol} price to ${currency} using USD rate`);
            }
          }
        });
      });
    } catch (error) {
      console.error(`Error filling missing prices: ${error.message}`);
    }
  }

  /**
   * Convert USD prices to multiple currencies (fallback method)
   */
  async convertToMultiCurrency(usdPrices, currencies) {
    const results = {};

    // Initialize structure
    Object.keys(usdPrices).forEach(symbol => {
      results[symbol] = {};
      currencies.forEach(currency => {
        results[symbol][currency] = null;
      });
    });

    // Set USD prices
    Object.keys(usdPrices).forEach(symbol => {
      if (currencies.includes('USD')) {
        results[symbol]['USD'] = usdPrices[symbol];
      }
    });

    // Get conversion rates and convert
    try {
      for (const currency of currencies) {
        if (currency !== 'USD') {
          const rate = await this.fetchCoinApiPrice('USD', currency);
          if (rate) {
            Object.keys(usdPrices).forEach(symbol => {
              results[symbol][currency] = usdPrices[symbol] * rate;
            });
          }
        }
      }
    } catch (error) {
      console.error(`Error in currency conversion: ${error.message}`);
    }

    return results;
  }

  /**
   * Get list of supported currencies
   */
  getSupportedCurrencies() {
    return this.supportedCurrencies;
  }

  /**
   * Fetch current market data from API
   */
  async fetchCurrentData(tradingPair) {
    try {
      // Try Coinbase Pro API first
      try {
        const url = `${this.coinbaseProApi}/products/${tradingPair}/ticker`;
        const response = await axios.get(url, { timeout: 10000 });
        
        const data = response.data;
        return {
          price: parseFloat(data.price),
          bid: parseFloat(data.bid),
          ask: parseFloat(data.ask),
          volume: parseFloat(data.volume),
          timestamp: Date.now(),
          source: 'coinbase_pro'
        };
      } catch (error) {
        console.log(`Coinbase Pro API failed for ${tradingPair}: ${error.message}, trying CoinGecko`);
        
        // Fallback to CoinGecko API
        return await this.fetchFromCoinGecko(tradingPair);
      }
    } catch (error) {
      console.error(`Error fetching current data: ${error.message}`);
      return null;
    }
  }

  /**
   * Fetch data from CoinGecko API as fallback
   */
  async fetchFromCoinGecko(tradingPair) {
    try {
      // Convert trading pair format (BTC-USD -> bitcoin, usd)
      const [cryptoSymbol, fiatSymbol] = tradingPair.split('-');
      const crypto = cryptoSymbol.toLowerCase();
      const fiat = fiatSymbol.toLowerCase();
      
      const coinId = this.symbolMap[crypto] || crypto;
      
      const url = `${this.coingeckoApi}/simple/price`;
      const params = {
        ids: coinId,
        vs_currencies: fiat,
        include_24hr_vol: 'true',
        include_24hr_change: 'true'
      };

      const headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json'
      };

      const response = await axios.get(url, { 
        params, 
        headers, 
        timeout: 10000 
      });

      const data = response.data;
      if (!data[coinId]) {
        throw new Error(`Coin ${coinId} not found in CoinGecko response`);
      }

      const coinData = data[coinId];
      const price = coinData[fiat];

      return {
        price: parseFloat(price),
        bid: parseFloat(price * 0.999), // Approximate bid/ask spread
        ask: parseFloat(price * 1.001),
        volume: parseFloat(coinData[`${fiat}_24h_vol`] || 0),
        change_24h: parseFloat(coinData[`${fiat}_24h_change`] || 0),
        timestamp: Date.now(),
        source: 'coingecko'
      };
    } catch (error) {
      console.error(`Error fetching from CoinGecko: ${error.message}`);
      return null;
    }
  }

  /**
   * Get fallback prices when APIs are unavailable
   */
  getFallbackPrices(symbols) {
    const basePrices = {
      BTC: 45000,
      ETH: 3000,
      ADA: 0.5,
      SOL: 100,
      DOT: 25,
      MATIC: 1.2,
      AVAX: 35,
      LINK: 15,
      UNI: 8,
      LTC: 150
    };

    const prices = {};
    symbols.forEach(symbol => {
      const basePrice = basePrices[symbol] || 1;
      // Add some realistic variation (±5%)
      const variation = (Math.random() - 0.5) * 0.1;
      prices[symbol] = basePrice * (1 + variation);
    });

    return prices;
  }

  /**
   * Check if cached data is still valid
   */
  isCacheValid(cacheKey) {
    if (!this.cache.has(cacheKey)) {
      return false;
    }

    const cached = this.cache.get(cacheKey);
    const age = Date.now() - cached.timestamp;
    return age < this.cacheTimeout;
  }

  /**
   * Clear all cached data
   */
  clearCache() {
    this.cache.clear();
    console.log('Market data cache cleared');
  }

  /**
   * Get cache information
   */
  getCacheInfo() {
    const cacheInfo = {};
    const currentTime = Date.now();

    for (const [key, value] of this.cache.entries()) {
      const age = currentTime - value.timestamp;
      cacheInfo[key] = {
        age_seconds: Math.floor(age / 1000),
        is_valid: age < this.cacheTimeout,
        data_type: typeof value.data,
        source: value.data?.source || 'unknown'
      };
    }

    return cacheInfo;
  }

  /**
   * Get market summary for multiple coins
   */
  async getMarketSummary(symbols = ['BTC', 'ETH', 'ADA', 'SOL']) {
    try {
      const prices = await this.getCurrentPrices(symbols);
      const summary = {
        prices: prices,
        timestamp: new Date().toISOString(),
        total_symbols: symbols.length,
        successful_fetches: Object.keys(prices).length,
        cache_info: this.getCacheInfo()
      };

      return summary;
    } catch (error) {
      console.error(`Error getting market summary: ${error.message}`);
      return {
        prices: this.getFallbackPrices(symbols),
        timestamp: new Date().toISOString(),
        error: error.message,
        fallback: true
      };
    }
  }

  /**
   * Health check for the market data service
   */
  async healthCheck() {
    try {
      // Try to fetch BTC price as a health check
      const btcData = await this.getCurrentData('BTC-USD');
      
      return {
        status: 'healthy',
        btc_price_available: !!btcData,
        cache_entries: this.cache.size,
        last_check: new Date().toISOString(),
        apis_tested: ['coinbase_pro', 'coingecko']
      };
    } catch (error) {
      return {
        status: 'error',
        error: error.message,
        last_check: new Date().toISOString()
      };
    }
  }
}

module.exports = MarketDataProvider;
