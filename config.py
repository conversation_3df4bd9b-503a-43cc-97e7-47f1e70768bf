"""
Configuration management for the trading agent
"""

import os
from typing import Dict, Any
from dotenv import load_dotenv

load_dotenv()

class Config:
    """Configuration class for trading agent settings"""
    
    def __init__(self):
        """Initialize configuration with environment variables and defaults"""
        
        # Coinbase API Configuration (updated for AgentKit requirements)
        self.CDP_API_KEY_ID = os.getenv("CDP_API_KEY_ID", "")
        self.CDP_API_KEY_SECRET = os.getenv("CDP_API_KEY_SECRET", "")
        self.CDP_WALLET_SECRET = os.getenv("CDP_WALLET_SECRET", "")
        # Legacy environment variables for backward compatibility
        self.CDP_API_KEY_NAME = os.getenv("CDP_API_KEY_NAME", "")
        self.CDP_API_KEY_PRIVATE_KEY = os.getenv("CDP_API_KEY_PRIVATE_KEY", "")
        self.COINBASE_API_KEY = os.getenv("COINBASE_API_KEY", "")
        self.COINBASE_API_SECRET = os.getenv("COINBASE_API_SECRET", "")
        
        # Trading Configuration
        self.DEFAULT_BASE_CURRENCY = os.getenv("DEFAULT_BASE_CURRENCY", "USD")
        self.DEFAULT_QUOTE_CURRENCY = os.getenv("DEFAULT_QUOTE_CURRENCY", "BTC")
        self.TRADING_PAIR = f"{self.DEFAULT_QUOTE_CURRENCY}-{self.DEFAULT_BASE_CURRENCY}"
        
        # Risk Management
        self.MAX_TRADE_AMOUNT = float(os.getenv("MAX_TRADE_AMOUNT", "100.0"))
        self.MIN_TRADE_AMOUNT = float(os.getenv("MIN_TRADE_AMOUNT", "10.0"))
        self.MAX_DAILY_TRADES = int(os.getenv("MAX_DAILY_TRADES", "10"))
        self.STOP_LOSS_PERCENTAGE = float(os.getenv("STOP_LOSS_PERCENTAGE", "5.0"))
        self.TAKE_PROFIT_PERCENTAGE = float(os.getenv("TAKE_PROFIT_PERCENTAGE", "10.0"))
        
        # Trading Strategy Parameters
        self.STRATEGY_TYPE = os.getenv("STRATEGY_TYPE", "trend_following")
        self.RSI_PERIOD = int(os.getenv("RSI_PERIOD", "14"))
        self.RSI_OVERSOLD = float(os.getenv("RSI_OVERSOLD", "30.0"))
        self.RSI_OVERBOUGHT = float(os.getenv("RSI_OVERBOUGHT", "70.0"))
        self.MOVING_AVERAGE_PERIOD = int(os.getenv("MOVING_AVERAGE_PERIOD", "20"))
        
        # Execution Settings
        self.TRADING_INTERVAL = int(os.getenv("TRADING_INTERVAL", "300"))  # 5 minutes
        self.STATUS_UPDATE_INTERVAL = int(os.getenv("STATUS_UPDATE_INTERVAL", "30"))  # 30 seconds
        self.MARKET_DATA_REFRESH_INTERVAL = int(os.getenv("MARKET_DATA_REFRESH_INTERVAL", "60"))  # 1 minute
        
        # Logging Configuration
        self.LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
        self.LOG_FILE = os.getenv("LOG_FILE", "trading_agent.log")
        self.LOG_MAX_SIZE = int(os.getenv("LOG_MAX_SIZE", "10485760"))  # 10MB
        self.LOG_BACKUP_COUNT = int(os.getenv("LOG_BACKUP_COUNT", "5"))
        
        # Development Settings
        self.DRY_RUN = os.getenv("DRY_RUN", "True").lower() == "true"
        self.DEBUG_MODE = os.getenv("DEBUG_MODE", "False").lower() == "true"
        
        # Validate configuration
        self._validate_config()
    
    def _validate_config(self):
        """Validate configuration parameters"""
        if not self.CDP_API_KEY_ID and not self.DRY_RUN:
            raise ValueError("CDP_API_KEY_ID is required for live trading")
        
        if not self.CDP_API_KEY_SECRET and not self.DRY_RUN:
            raise ValueError("CDP_API_KEY_SECRET is required for live trading")
        
        if not self.CDP_WALLET_SECRET and not self.DRY_RUN:
            raise ValueError("CDP_WALLET_SECRET is required for live trading")
        
        if self.MAX_TRADE_AMOUNT <= 0:
            raise ValueError("MAX_TRADE_AMOUNT must be positive")
        
        if self.MIN_TRADE_AMOUNT <= 0:
            raise ValueError("MIN_TRADE_AMOUNT must be positive")
        
        if self.MIN_TRADE_AMOUNT >= self.MAX_TRADE_AMOUNT:
            raise ValueError("MIN_TRADE_AMOUNT must be less than MAX_TRADE_AMOUNT")
        
        if self.TRADING_INTERVAL < 60:
            raise ValueError("TRADING_INTERVAL must be at least 60 seconds")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary (excluding sensitive data)"""
        config_dict = {}
        for key, value in self.__dict__.items():
            if 'key' not in key.lower() and 'secret' not in key.lower():
                config_dict[key] = value
        return config_dict
    
    def get_agentkit_config(self) -> Dict[str, Any]:
        """Get configuration dictionary for AgentKit initialization"""
        return {
            "cdp_api_key_id": self.CDP_API_KEY_ID,
            "cdp_api_key_secret": self.CDP_API_KEY_SECRET,
            "cdp_wallet_secret": self.CDP_WALLET_SECRET,
        }
