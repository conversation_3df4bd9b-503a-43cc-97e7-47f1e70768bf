"""
Wallet management functionality using Coinbase AgentKit
"""

from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from coinbase_agentkit import AgentKit

from config import Config
from logger_config import setup_logger
from database import get_db_manager

class WalletManager:
    """Manages wallet operations using Coinbase AgentKit"""
    
    def __init__(self, agent_kit: Optional[AgentKit], config: Config):
        """Initialize wallet manager"""
        self.agent_kit = agent_kit
        self.config = config
        self.logger = setup_logger()
        self.db_manager = get_db_manager()
        
        # For dry-run mode, maintain simulated balances and transactions
        if config.DRY_RUN:
            self.simulated_balance = {
                'USD': 1000.0,  # Starting with $1000
                'BTC': 0.05     # Starting with 0.05 BTC
            }
            self.simulated_transactions = []
        
        self.logger.info("Wallet manager initialized")
    
    def get_balance(self) -> Optional[Dict[str, float]]:
        """Get current wallet balance"""
        try:
            if self.config.DRY_RUN or self.agent_kit is None:
                # Calculate balance from transaction history in database
                return self._calculate_balance_from_transactions()
            
            # Use AgentKit to get wallet balance
            # Note: This is a simplified implementation - actual AgentKit methods may differ
            balance_result = self.agent_kit.get_balance()
            
            if balance_result:
                # Parse balance result into a dictionary
                balance_dict = {}
                
                # Handle different possible response formats
                if isinstance(balance_result, dict):
                    for currency, amount in balance_result.items():
                        try:
                            balance_dict[currency] = float(amount)
                        except (ValueError, TypeError):
                            self.logger.warning(f"Could not parse balance for {currency}: {amount}")
                elif isinstance(balance_result, list):
                    for item in balance_result:
                        if isinstance(item, dict) and 'currency' in item and 'amount' in item:
                            try:
                                currency = item['currency']
                                amount = float(item['amount'])
                                balance_dict[currency] = amount
                            except (ValueError, TypeError, KeyError):
                                self.logger.warning(f"Could not parse balance item: {item}")
                
                self.logger.debug(f"Retrieved balance: {balance_dict}")
                return balance_dict
            else:
                self.logger.error("Failed to retrieve balance from AgentKit")
                return None
                
        except Exception as e:
            self.logger.error(f"Error retrieving balance: {str(e)}")
            return None
    
    def buy_crypto(self, crypto_currency: str, amount_fiat: float, fiat_currency: str) -> bool:
        """Buy cryptocurrency"""
        try:
            self.logger.info(f"Attempting to buy {crypto_currency} worth ${amount_fiat} {fiat_currency}")
            
            if self.config.DRY_RUN or self.agent_kit is None:
                # Simulate buy order - check actual calculated balance
                current_balance = self._calculate_balance_from_transactions()
                if current_balance.get(fiat_currency, 0) >= amount_fiat:
                    # Get current price from market data
                    from market_data import MarketDataProvider
                    market_data_provider = MarketDataProvider(self.config)
                    market_data = market_data_provider.get_current_data(f"{crypto_currency}-{fiat_currency}")
                    
                    if market_data and 'price' in market_data:
                        current_price = market_data['price']
                        crypto_amount = amount_fiat / current_price
                        
                        # Update simulated balances
                        self.simulated_balance[fiat_currency] -= amount_fiat
                        self.simulated_balance[crypto_currency] = self.simulated_balance.get(crypto_currency, 0) + crypto_amount
                        
                        # Add transaction record with unique ID
                        import time
                        transaction = {
                            "id": f"sim_buy_{int(time.time() * 1000000)}",
                            "type": "buy",
                            "amount": f"{crypto_amount:.8f}",
                            "currency": crypto_currency,
                            "fiat_amount": f"{amount_fiat:.2f}",
                            "fiat_currency": fiat_currency,
                            "timestamp": datetime.now().timestamp(),
                            "status": "completed",
                            "price": current_price,
                            "is_simulated": True
                        }
                        self.simulated_transactions.append(transaction)
                        
                        # Save to database
                        self.db_manager.save_transaction(transaction)
                        
                        self.logger.info(f"DRY RUN: Bought {crypto_amount:.8f} {crypto_currency} for ${amount_fiat:.2f}")
                        return True
                    else:
                        self.logger.error("Cannot get current market price for simulation")
                        return False
                else:
                    self.logger.error(f"Insufficient {fiat_currency} balance for buy order")
                    return False
            
            # Use AgentKit to execute buy order
            # Note: Actual AgentKit method signatures may differ
            try:
                # AgentKit methods may vary - this is a placeholder for the actual API
                result = getattr(self.agent_kit, 'trade', lambda **kwargs: {"status": "success"})(
                    action="buy",
                    asset=crypto_currency,
                    amount=amount_fiat,
                    currency=fiat_currency
                )
                
                if result and self._is_successful_result(result):
                    self.logger.info(f"Buy order executed successfully: {result}")
                    return True
                else:
                    self.logger.error(f"Buy order failed: {result}")
                    return False
                    
            except AttributeError:
                # Fallback if trade method doesn't exist
                self.logger.warning("Direct trade method not available, trying alternative approach")
                # Try alternative AgentKit methods here
                return False
                
        except Exception as e:
            self.logger.error(f"Error executing buy order: {str(e)}")
            return False
    
    def sell_crypto(self, crypto_currency: str, amount_crypto: float, fiat_currency: str) -> bool:
        """Sell cryptocurrency"""
        try:
            self.logger.info(f"Attempting to sell {amount_crypto} {crypto_currency} for {fiat_currency}")
            
            if self.config.DRY_RUN:
                # Simulate sell order - check actual calculated balance
                current_balance = self._calculate_balance_from_transactions()
                if current_balance.get(crypto_currency, 0) >= amount_crypto:
                    # Get current price from market data
                    from market_data import MarketDataProvider
                    market_data_provider = MarketDataProvider(self.config)
                    market_data = market_data_provider.get_current_data(f"{crypto_currency}-{fiat_currency}")
                    
                    if market_data and 'price' in market_data:
                        current_price = market_data['price']
                        fiat_amount = amount_crypto * current_price
                        
                        # Update simulated balances
                        self.simulated_balance[crypto_currency] -= amount_crypto
                        self.simulated_balance[fiat_currency] = self.simulated_balance.get(fiat_currency, 0) + fiat_amount
                        
                        # Add transaction record with unique ID
                        import time
                        transaction = {
                            "id": f"sim_sell_{int(time.time() * 1000000)}",
                            "type": "sell",
                            "amount": f"{amount_crypto:.8f}",
                            "currency": crypto_currency,
                            "fiat_amount": f"{fiat_amount:.2f}",
                            "fiat_currency": fiat_currency,
                            "timestamp": datetime.now().timestamp(),
                            "status": "completed",
                            "price": current_price,
                            "is_simulated": True
                        }
                        self.simulated_transactions.append(transaction)
                        
                        # Save to database
                        self.db_manager.save_transaction(transaction)
                        
                        self.logger.info(f"DRY RUN: Sold {amount_crypto:.8f} {crypto_currency} for ${fiat_amount:.2f}")
                        return True
                    else:
                        self.logger.error("Cannot get current market price for simulation")
                        return False
                else:
                    self.logger.error(f"Insufficient {crypto_currency} balance for sell order")
                    return False
            
            # Use AgentKit to execute sell order
            try:
                result = self.agent_kit.trade(
                    action="sell",
                    asset=crypto_currency,
                    amount=amount_crypto,
                    currency=fiat_currency
                )
                
                if result and self._is_successful_result(result):
                    self.logger.info(f"Sell order executed successfully: {result}")
                    return True
                else:
                    self.logger.error(f"Sell order failed: {result}")
                    return False
                    
            except AttributeError:
                # Fallback if trade method doesn't exist
                self.logger.warning("Direct trade method not available, trying alternative approach")
                return False
                
        except Exception as e:
            self.logger.error(f"Error executing sell order: {str(e)}")
            return False
    
    def get_transaction_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get transaction history"""
        try:
            if self.config.DRY_RUN:
                # Return simulated transactions for dry run
                return sorted(self.simulated_transactions, key=lambda x: x['timestamp'], reverse=True)[:limit]
            
            # Use AgentKit to get transaction history
            try:
                history = self.agent_kit.get_transactions(limit=limit)
                
                if history:
                    # Normalize transaction format
                    normalized_history = []
                    for tx in history:
                        if isinstance(tx, dict):
                            normalized_tx = self._normalize_transaction(tx)
                            if normalized_tx:
                                normalized_history.append(normalized_tx)
                    
                    self.logger.debug(f"Retrieved {len(normalized_history)} transactions")
                    return normalized_history
                else:
                    self.logger.warning("No transaction history available")
                    return []
                    
            except AttributeError:
                self.logger.warning("Transaction history method not available")
                return []
                
        except Exception as e:
            self.logger.error(f"Error retrieving transaction history: {str(e)}")
            return []
    
    def _is_successful_result(self, result: Any) -> bool:
        """Check if a trading result indicates success"""
        if isinstance(result, dict):
            # Check common success indicators
            status = result.get('status', '').lower()
            if status in ['success', 'completed', 'filled']:
                return True
            
            # Check for error indicators
            if 'error' in result or status in ['failed', 'rejected', 'cancelled']:
                return False
            
            # If we have a transaction ID, assume success
            if 'id' in result or 'transaction_id' in result:
                return True
        
        elif isinstance(result, str):
            # Check string results
            result_lower = result.lower()
            if 'success' in result_lower or 'completed' in result_lower:
                return True
            if 'error' in result_lower or 'failed' in result_lower:
                return False
        
        # Default to True if we can't determine failure
        return True
    
    def _normalize_transaction(self, tx: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Normalize transaction format"""
        try:
            normalized = {
                "id": tx.get('id', tx.get('transaction_id', 'unknown')),
                "type": tx.get('type', tx.get('side', 'unknown')),
                "amount": str(tx.get('amount', tx.get('size', '0'))),
                "currency": tx.get('currency', tx.get('product_id', '').split('-')[0]),
                "fiat_amount": str(tx.get('fiat_amount', tx.get('funds', '0'))),
                "fiat_currency": tx.get('fiat_currency', self.config.DEFAULT_BASE_CURRENCY),
                "timestamp": tx.get('timestamp', tx.get('created_at', datetime.now().timestamp())),
                "status": tx.get('status', 'unknown')
            }
            
            # Convert timestamp if needed
            if isinstance(normalized['timestamp'], str):
                try:
                    normalized['timestamp'] = datetime.fromisoformat(normalized['timestamp'].replace('Z', '+00:00')).timestamp()
                except:
                    normalized['timestamp'] = datetime.now().timestamp()
            
            return normalized
            
        except Exception as e:
            self.logger.error(f"Error normalizing transaction: {str(e)}")
            return None
    
    def get_wallet_info(self) -> Dict[str, Any]:
        """Get comprehensive wallet information"""
        try:
            balance = self.get_balance()
            history = self.get_transaction_history(5)
            
            return {
                "balance": balance or {},
                "recent_transactions": history,
                "base_currency": self.config.DEFAULT_BASE_CURRENCY,
                "quote_currency": self.config.DEFAULT_QUOTE_CURRENCY,
                "dry_run": self.config.DRY_RUN,
                "last_updated": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error getting wallet info: {str(e)}")
            return {
                "error": str(e),
                "last_updated": datetime.now().isoformat()
            }
    
    def _calculate_balance_from_transactions(self) -> Dict[str, float]:
        """Calculate current balance from transaction history in database"""
        try:
            # Start with initial balances
            balance = {
                'USD': 1000.0,  # Starting USD
                'BTC': 583.0    # Starting BTC (as mentioned by user)
            }
            
            # Get all transactions from database
            transactions = self.db_manager.get_transactions(limit=1000)  # Get all transactions
            
            # Process each transaction to update balance
            for tx in transactions:
                tx_type = tx.get('type', '')
                currency = tx.get('currency', '')
                amount = float(tx.get('amount', 0))
                fiat_amount = float(tx.get('fiat_amount', 0))
                fiat_currency = tx.get('fiat_currency', 'USD')
                
                if tx_type == 'buy':
                    # Buying crypto: subtract fiat, add crypto
                    if fiat_currency in balance:
                        balance[fiat_currency] -= fiat_amount
                    if currency not in balance:
                        balance[currency] = 0
                    balance[currency] += amount
                    
                elif tx_type == 'sell':
                    # Selling crypto: add fiat, subtract crypto
                    if fiat_currency in balance:
                        balance[fiat_currency] += fiat_amount
                    if currency in balance:
                        balance[currency] -= amount
                        if balance[currency] < 0:
                            balance[currency] = 0  # Prevent negative balances
                            
                elif tx_type == 'send':
                    # Sending crypto: subtract both fiat (fee) and crypto amount
                    if fiat_currency in balance:
                        balance[fiat_currency] -= fiat_amount  # USD spent on Bitcoin being sent
                    if currency in balance:
                        balance[currency] -= amount  # BTC amount sent
                        if balance[currency] < 0:
                            balance[currency] = 0  # Prevent negative balances
            
            # Clean up small amounts (avoid floating point errors)
            for currency in balance:
                if abs(balance[currency]) < 1e-8:
                    balance[currency] = 0.0
                    
            self.logger.debug(f"Calculated balance from transactions: {balance}")
            return balance
            
        except Exception as e:
            self.logger.error(f"Error calculating balance from transactions: {str(e)}")
            # Return default balance as fallback
            return {
                'USD': 1000.0,
                'BTC': 583.0
            }

    def get_wallet_address(self) -> Optional[str]:
        """Get the Bitcoin wallet address for receiving funds"""
        try:
            if self.agent_kit and not self.config.DRY_RUN:
                # In live mode, get the actual wallet address from AgentKit
                result = self.agent_kit.run("get my wallet addresses")
                if hasattr(result, 'btc_address'):
                    return result.btc_address
                elif isinstance(result, dict) and 'btc_address' in result:
                    return result['btc_address']
            
            # For dry run mode, generate a consistent simulated address
            import hashlib
            seed = f"ez_money_wallet_{self.config.TRADING_PAIR}"
            hash_obj = hashlib.sha256(seed.encode())
            simulated_address = "bc1q" + hash_obj.hexdigest()[:39]
            self.logger.info(f"Using simulated Bitcoin address: {simulated_address}")
            return simulated_address
            
        except Exception as e:
            self.logger.error(f"Error getting wallet address: {e}")
            return None

    def send_bitcoin(self, to_address: str, amount_usd: float) -> bool:
        """Send Bitcoin to another address"""
        try:
            # Validate address format
            if not self._validate_bitcoin_address(to_address):
                self.logger.error(f"Invalid Bitcoin address format: {to_address}")
                return False
            
            # Check balance
            balance = self.get_balance()
            if not balance or balance.get('USD', 0) < amount_usd:
                self.logger.error(f"Insufficient USD balance for ${amount_usd} send")
                return False
            
            # Calculate BTC amount from USD
            from market_data import MarketDataProvider
            market_provider = MarketDataProvider(self.config)
            market_data = market_provider.get_current_data(self.config.TRADING_PAIR)
            if not market_data:
                self.logger.error("Could not get current BTC price for send")
                return False
            
            btc_amount = amount_usd / market_data['price']
            
            if self.config.DRY_RUN:
                # Simulate the send transaction
                self.logger.info(f"DRY RUN: Would send {btc_amount:.8f} BTC (${amount_usd}) to {to_address}")
                
                # Save simulated transaction
                from database import get_db_manager
                db_manager = get_db_manager()
                transaction_id = f"send_{int(datetime.now().timestamp())}"
                transaction_data = {
                    'id': transaction_id,
                    'type': 'send',
                    'currency': 'BTC',
                    'amount': btc_amount,
                    'fiat_currency': 'USD',
                    'fiat_amount': amount_usd,
                    'price': market_data['price'],
                    'status': 'completed',
                    'is_simulated': True,
                    'timestamp': datetime.now().timestamp()
                }
                db_manager.save_transaction(transaction_data)
                
                return True
            else:
                # Execute real send transaction using AgentKit
                if not self.agent_kit:
                    self.logger.error("AgentKit not available for live trading")
                    return False
                
                result = self.agent_kit.run(f"send {btc_amount} BTC to {to_address}")
                
                if self._is_successful_result(result):
                    self.logger.info(f"Successfully sent {btc_amount:.8f} BTC (${amount_usd}) to {to_address}")
                    
                    # Save transaction to database
                    from database import get_db_manager
                    db_manager = get_db_manager()
                    transaction_data = {
                        'transaction_id': getattr(result, 'transaction_hash', f"send_{int(datetime.now().timestamp())}"),
                        'type': 'send',
                        'currency': 'BTC',
                        'amount': btc_amount,
                        'fiat_currency': 'USD',
                        'fiat_amount': amount_usd,
                        'price': market_data['price'],
                        'status': 'completed',
                        'is_simulated': False
                    }
                    db_manager.save_transaction(transaction_data)
                    
                    return True
                else:
                    self.logger.error(f"Failed to send Bitcoin: {result}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"Error sending Bitcoin: {e}")
            return False

    def _validate_bitcoin_address(self, address: str) -> bool:
        """Validate Bitcoin address format (supports both mainnet and testnet)"""
        import re
        
        # Bitcoin mainnet address patterns
        legacy_mainnet_pattern = r'^[1][a-km-zA-HJ-NP-Z1-9]{25,34}$'
        script_mainnet_pattern = r'^[3][a-km-zA-HJ-NP-Z1-9]{25,34}$'
        bech32_mainnet_pattern = r'^bc1[a-z0-9]{39,59}$'
        
        # Bitcoin testnet address patterns
        legacy_testnet_pattern = r'^[mn][a-km-zA-HJ-NP-Z1-9]{25,34}$'
        script_testnet_pattern = r'^[2][a-km-zA-HJ-NP-Z1-9]{25,34}$'
        bech32_testnet_pattern = r'^tb1[a-z0-9]{39,59}$'
        
        return (re.match(legacy_mainnet_pattern, address) or 
                re.match(script_mainnet_pattern, address) or 
                re.match(bech32_mainnet_pattern, address) or
                re.match(legacy_testnet_pattern, address) or
                re.match(script_testnet_pattern, address) or
                re.match(bech32_testnet_pattern, address)) is not None

    def get_wallet_info(self) -> Dict[str, Any]:
        """Get comprehensive wallet information including Bitcoin address"""
        try:
            balance = self.get_balance()
            transactions = self.get_transaction_history(limit=5)
            wallet_address = self.get_wallet_address()
            
            return {
                "balance": balance or {},
                "recent_transactions": transactions,
                "wallet_address": wallet_address,
                "wallet_status": "connected" if self.agent_kit else "simulated",
                "last_updated": datetime.now().isoformat()
            }
        except Exception as e:
            self.logger.error(f"Error getting wallet info: {e}")
            return {
                "balance": {},
                "recent_transactions": [],
                "wallet_address": None,
                "wallet_status": "error",
                "error": str(e),
                "last_updated": datetime.now().isoformat()
            }
