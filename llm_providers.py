"""
LLM providers for enhanced trading decisions
"""
import json
import os
from typing import Dict, Any, Optional, List
from abc import ABC, abstractmethod
import requests
from logger_config import setup_logger


class LLMProvider(ABC):
    """Abstract base class for LLM providers"""
    
    def __init__(self, name: str):
        self.name = name
        self.logger = setup_logger(f"LLM_{name}")
    
    @abstractmethod
    def analyze_market(self, market_data: Dict[str, Any], technical_signals: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze market data and provide trading insights"""
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """Check if the provider is properly configured and available"""
        pass


class OpenAIProvider(LLMProvider):
    """OpenAI GPT provider for trading analysis"""
    
    def __init__(self):
        super().__init__("OpenAI")
        self.api_key = os.environ.get("OPENAI_API_KEY")
        self.base_url = "https://api.openai.com/v1"
        self.model = "gpt-4o"  # Latest GPT-4 model
    
    def analyze_market(self, market_data: Dict[str, Any], technical_signals: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze market using OpenAI GPT"""
        try:
            if not self.is_available():
                raise ValueError("OpenAI API key not configured")
            
            prompt = self._create_analysis_prompt(market_data, technical_signals)
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": self.model,
                "messages": [
                    {
                        "role": "system",
                        "content": "You are a professional cryptocurrency trading analyst. Analyze the provided market data and technical signals, then provide trading insights in JSON format."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "response_format": {"type": "json_object"},
                "max_tokens": 1000,
                "temperature": 0.3
            }
            
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result["choices"][0]["message"]["content"]
                analysis = json.loads(content)
                
                self.logger.info(f"OpenAI analysis completed: {analysis.get('recommendation', 'N/A')}")
                return analysis
            else:
                self.logger.error(f"OpenAI API error: {response.status_code} - {response.text}")
                return {"error": f"API error: {response.status_code}"}
                
        except Exception as e:
            self.logger.error(f"Error in OpenAI analysis: {e}")
            return {"error": str(e)}
    
    def is_available(self) -> bool:
        """Check if OpenAI is configured"""
        return bool(self.api_key)
    
    def _create_analysis_prompt(self, market_data: Dict[str, Any], technical_signals: Dict[str, Any]) -> str:
        """Create analysis prompt for OpenAI"""
        return f"""
Analyze this Bitcoin market data and technical signals:

MARKET DATA:
- Current Price: ${market_data.get('price', 0):,.2f}
- 24h Change: {market_data.get('price_change_24h', 0):.2f}%
- Volume 24h: ${market_data.get('volume_24h', 0):,.0f}
- Market Cap: ${market_data.get('market_cap', 0):,.0f}

TECHNICAL SIGNALS:
- Action: {technical_signals.get('action', 'hold')}
- Confidence: {technical_signals.get('confidence', 0):.2f}
- Reasoning: {technical_signals.get('reasoning', 'N/A')}

Provide analysis in this JSON format:
{{
    "sentiment": "bullish|bearish|neutral",
    "confidence_adjustment": 0.8-1.2,
    "recommendation": "strong_buy|buy|hold|sell|strong_sell",
    "reasoning": "detailed explanation",
    "risk_factors": ["factor1", "factor2"],
    "price_target_24h": price_estimate,
    "market_outlook": "short analysis"
}}
"""


class DeepSeekProvider(LLMProvider):
    """DeepSeek R1 provider for trading analysis"""
    
    def __init__(self):
        super().__init__("DeepSeek")
        self.api_key = os.environ.get("DEEPSEEK_API_KEY")
        self.base_url = "https://api.deepseek.com/v1"
        self.model = "deepseek-reasoner"
    
    def analyze_market(self, market_data: Dict[str, Any], technical_signals: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze market using DeepSeek R1"""
        try:
            if not self.is_available():
                raise ValueError("DeepSeek API key not configured")
            
            prompt = self._create_analysis_prompt(market_data, technical_signals)
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": self.model,
                "messages": [
                    {
                        "role": "system",
                        "content": "You are an expert cryptocurrency analyst with deep reasoning capabilities. Provide thorough market analysis."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "max_tokens": 1000,
                "temperature": 0.2
            }
            
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result["choices"][0]["message"]["content"]
                
                # Parse JSON from response
                try:
                    analysis = json.loads(content)
                except json.JSONDecodeError:
                    # If not JSON, create structured response
                    analysis = {
                        "sentiment": "neutral",
                        "confidence_adjustment": 1.0,
                        "recommendation": "hold",
                        "reasoning": content,
                        "risk_factors": ["API response parsing"],
                        "price_target_24h": market_data.get('price', 0),
                        "market_outlook": "Analysis available in reasoning field"
                    }
                
                self.logger.info(f"DeepSeek analysis completed: {analysis.get('recommendation', 'N/A')}")
                return analysis
            else:
                self.logger.error(f"DeepSeek API error: {response.status_code} - {response.text}")
                return {"error": f"API error: {response.status_code}"}
                
        except Exception as e:
            self.logger.error(f"Error in DeepSeek analysis: {e}")
            return {"error": str(e)}
    
    def is_available(self) -> bool:
        """Check if DeepSeek is configured"""
        return bool(self.api_key)
    
    def _create_analysis_prompt(self, market_data: Dict[str, Any], technical_signals: Dict[str, Any]) -> str:
        """Create analysis prompt for DeepSeek"""
        return f"""
As an expert crypto analyst, analyze this Bitcoin market situation:

Current Market State:
- Price: ${market_data.get('price', 0):,.2f}
- 24h Change: {market_data.get('price_change_24h', 0):.2f}%
- Volume: ${market_data.get('volume_24h', 0):,.0f}
- Market Cap: ${market_data.get('market_cap', 0):,.0f}

Technical Analysis Says:
- Signal: {technical_signals.get('action', 'hold')}
- Confidence: {technical_signals.get('confidence', 0):.2f}
- Technical Reasoning: {technical_signals.get('reasoning', 'N/A')}

Provide deep reasoning analysis in JSON format:
{{
    "sentiment": "bullish|bearish|neutral",
    "confidence_adjustment": 0.8-1.2,
    "recommendation": "strong_buy|buy|hold|sell|strong_sell",
    "reasoning": "your detailed analysis with reasoning steps",
    "risk_factors": ["list", "of", "risks"],
    "price_target_24h": estimated_price,
    "market_outlook": "market analysis summary"
}}
"""


class FinGPTProvider(LLMProvider):
    """FinGPT provider for financial trading analysis"""
    
    def __init__(self):
        super().__init__("FinGPT")
        self.api_key = os.environ.get("FINGPT_API_KEY")
        self.base_url = os.environ.get("FINGPT_API_URL", "https://api.fingpt.ai/v1")
        self.model = "fingpt-v3"
    
    def analyze_market(self, market_data: Dict[str, Any], technical_signals: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze market using FinGPT"""
        try:
            if not self.is_available():
                raise ValueError("FinGPT API key not configured")
            
            prompt = self._create_analysis_prompt(market_data, technical_signals)
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": self.model,
                "prompt": prompt,
                "max_tokens": 800,
                "temperature": 0.1,
                "format": "json"
            }
            
            response = requests.post(
                f"{self.base_url}/completions",
                headers=headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get("choices", [{}])[0].get("text", "{}")
                
                try:
                    analysis = json.loads(content)
                except json.JSONDecodeError:
                    analysis = {
                        "sentiment": "neutral",
                        "confidence_adjustment": 1.0,
                        "recommendation": "hold",
                        "reasoning": content,
                        "risk_factors": ["Response parsing error"],
                        "price_target_24h": market_data.get('price', 0),
                        "market_outlook": "See reasoning field"
                    }
                
                self.logger.info(f"FinGPT analysis completed: {analysis.get('recommendation', 'N/A')}")
                return analysis
            else:
                self.logger.error(f"FinGPT API error: {response.status_code} - {response.text}")
                return {"error": f"API error: {response.status_code}"}
                
        except Exception as e:
            self.logger.error(f"Error in FinGPT analysis: {e}")
            return {"error": str(e)}
    
    def is_available(self) -> bool:
        """Check if FinGPT is configured"""
        return bool(self.api_key)
    
    def _create_analysis_prompt(self, market_data: Dict[str, Any], technical_signals: Dict[str, Any]) -> str:
        """Create analysis prompt for FinGPT"""
        return f"""
Financial Analysis Request for Bitcoin:

Market Data:
Price: ${market_data.get('price', 0):,.2f}
24h Change: {market_data.get('price_change_24h', 0):.2f}%
Volume: ${market_data.get('volume_24h', 0):,.0f}
Market Cap: ${market_data.get('market_cap', 0):,.0f}

Technical Signals:
Action: {technical_signals.get('action', 'hold')}
Confidence: {technical_signals.get('confidence', 0):.2f}
Reasoning: {technical_signals.get('reasoning', 'N/A')}

Return JSON analysis with sentiment, confidence_adjustment (0.8-1.2), recommendation, reasoning, risk_factors array, price_target_24h, and market_outlook.
"""


class BloombergGPTProvider(LLMProvider):
    """Bloomberg GPT provider for institutional-grade analysis"""
    
    def __init__(self):
        super().__init__("BloombergGPT")
        self.api_key = os.environ.get("BLOOMBERG_API_KEY")
        self.base_url = os.environ.get("BLOOMBERG_API_URL", "https://api.bloomberg.com/v1")
        self.model = "bloomberg-gpt"
    
    def analyze_market(self, market_data: Dict[str, Any], technical_signals: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze market using Bloomberg GPT"""
        try:
            if not self.is_available():
                raise ValueError("Bloomberg GPT API key not configured")
            
            prompt = self._create_analysis_prompt(market_data, technical_signals)
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": self.model,
                "messages": [
                    {
                        "role": "system",
                        "content": "You are Bloomberg's institutional cryptocurrency analyst. Provide professional-grade market analysis."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "max_tokens": 1200,
                "temperature": 0.1
            }
            
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result["choices"][0]["message"]["content"]
                
                try:
                    analysis = json.loads(content)
                except json.JSONDecodeError:
                    analysis = {
                        "sentiment": "neutral",
                        "confidence_adjustment": 1.0,
                        "recommendation": "hold",
                        "reasoning": content,
                        "risk_factors": ["Response format error"],
                        "price_target_24h": market_data.get('price', 0),
                        "market_outlook": "See reasoning field"
                    }
                
                self.logger.info(f"Bloomberg GPT analysis completed: {analysis.get('recommendation', 'N/A')}")
                return analysis
            else:
                self.logger.error(f"Bloomberg GPT API error: {response.status_code} - {response.text}")
                return {"error": f"API error: {response.status_code}"}
                
        except Exception as e:
            self.logger.error(f"Error in Bloomberg GPT analysis: {e}")
            return {"error": str(e)}
    
    def is_available(self) -> bool:
        """Check if Bloomberg GPT is configured"""
        return bool(self.api_key)
    
    def _create_analysis_prompt(self, market_data: Dict[str, Any], technical_signals: Dict[str, Any]) -> str:
        """Create analysis prompt for Bloomberg GPT"""
        return f"""
Institutional Analysis for Bitcoin Trading:

Market Snapshot:
- Current Price: ${market_data.get('price', 0):,.2f}
- 24-Hour Change: {market_data.get('price_change_24h', 0):.2f}%
- Trading Volume: ${market_data.get('volume_24h', 0):,.0f}
- Market Capitalization: ${market_data.get('market_cap', 0):,.0f}

Technical Indicators:
- Signal: {technical_signals.get('action', 'hold')}
- Confidence Level: {technical_signals.get('confidence', 0):.2f}
- Technical Rationale: {technical_signals.get('reasoning', 'N/A')}

Please provide institutional-grade analysis in JSON format:
{{
    "sentiment": "bullish|bearish|neutral",
    "confidence_adjustment": 0.8-1.2,
    "recommendation": "strong_buy|buy|hold|sell|strong_sell",
    "reasoning": "institutional analysis with market context",
    "risk_factors": ["institutional risk considerations"],
    "price_target_24h": price_projection,
    "market_outlook": "institutional market perspective"
}}
"""


class CustomLLMProvider(LLMProvider):
    """Custom LLM provider for user-defined endpoints"""
    
    def __init__(self):
        super().__init__("Custom")
        self.api_key = os.environ.get("CUSTOM_LLM_API_KEY")
        self.base_url = os.environ.get("CUSTOM_LLM_API_URL")
        self.model = os.environ.get("CUSTOM_LLM_MODEL", "custom-model")
    
    def analyze_market(self, market_data: Dict[str, Any], technical_signals: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze market using custom LLM"""
        try:
            if not self.is_available():
                raise ValueError("Custom LLM not properly configured")
            
            prompt = self._create_analysis_prompt(market_data, technical_signals)
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": self.model,
                "messages": [
                    {
                        "role": "system",
                        "content": "You are a cryptocurrency trading analyst. Analyze market data and provide trading insights in JSON format."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "max_tokens": 1000,
                "temperature": 0.3
            }
            
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result["choices"][0]["message"]["content"]
                
                try:
                    analysis = json.loads(content)
                except json.JSONDecodeError:
                    analysis = {
                        "sentiment": "neutral",
                        "confidence_adjustment": 1.0,
                        "recommendation": "hold",
                        "reasoning": content,
                        "risk_factors": ["JSON parsing error"],
                        "price_target_24h": market_data.get('price', 0),
                        "market_outlook": "See reasoning field"
                    }
                
                self.logger.info(f"Custom LLM analysis completed: {analysis.get('recommendation', 'N/A')}")
                return analysis
            else:
                self.logger.error(f"Custom LLM API error: {response.status_code} - {response.text}")
                return {"error": f"API error: {response.status_code}"}
                
        except Exception as e:
            self.logger.error(f"Error in Custom LLM analysis: {e}")
            return {"error": str(e)}
    
    def is_available(self) -> bool:
        """Check if Custom LLM is configured"""
        return bool(self.api_key and self.base_url)
    
    def _create_analysis_prompt(self, market_data: Dict[str, Any], technical_signals: Dict[str, Any]) -> str:
        """Create analysis prompt for custom LLM"""
        return f"""
Cryptocurrency Market Analysis Request:

Market Data:
- Price: ${market_data.get('price', 0):,.2f}
- 24h Change: {market_data.get('price_change_24h', 0):.2f}%
- Volume: ${market_data.get('volume_24h', 0):,.0f}
- Market Cap: ${market_data.get('market_cap', 0):,.0f}

Technical Analysis:
- Signal: {technical_signals.get('action', 'hold')}
- Confidence: {technical_signals.get('confidence', 0):.2f}
- Reasoning: {technical_signals.get('reasoning', 'N/A')}

Provide analysis in JSON format:
{{
    "sentiment": "bullish|bearish|neutral",
    "confidence_adjustment": 0.8-1.2,
    "recommendation": "strong_buy|buy|hold|sell|strong_sell",
    "reasoning": "analysis explanation",
    "risk_factors": ["risk1", "risk2"],
    "price_target_24h": estimated_price,
    "market_outlook": "market summary"
}}
"""


class LLMManager:
    """Manages multiple LLM providers for trading analysis"""
    
    def __init__(self):
        self.logger = setup_logger("LLMManager")
        self.providers = {
            "openai": OpenAIProvider(),
            "deepseek": DeepSeekProvider(),
            "fingpt": FinGPTProvider(),
            "bloomberg": BloombergGPTProvider(),
            "custom": CustomLLMProvider()
        }
        self.active_provider = None
    
    def get_available_providers(self) -> List[Dict[str, Any]]:
        """Get list of available LLM providers"""
        providers = []
        for key, provider in self.providers.items():
            providers.append({
                "key": key,
                "name": provider.name,
                "available": provider.is_available(),
                "description": self._get_provider_description(key)
            })
        return providers
    
    def set_active_provider(self, provider_key: str) -> bool:
        """Set the active LLM provider"""
        if provider_key in self.providers:
            provider = self.providers[provider_key]
            if provider.is_available():
                self.active_provider = provider_key
                self.logger.info(f"Active LLM provider set to: {provider.name}")
                return True
            else:
                self.logger.error(f"Provider {provider.name} is not available (missing API key)")
                return False
        else:
            self.logger.error(f"Unknown provider: {provider_key}")
            return False
    
    def analyze_market(self, market_data: Dict[str, Any], technical_signals: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze market using the active LLM provider"""
        if not self.active_provider:
            return {"error": "No active LLM provider selected"}
        
        provider = self.providers[self.active_provider]
        return provider.analyze_market(market_data, technical_signals)
    
    def _get_provider_description(self, key: str) -> str:
        """Get description for each provider"""
        descriptions = {
            "openai": "GPT-4o - Advanced general-purpose AI with strong reasoning",
            "deepseek": "DeepSeek R1 - Specialized reasoning model with deep analytical capabilities",
            "fingpt": "FinGPT - Financial domain-specific model for trading analysis",
            "bloomberg": "Bloomberg GPT - Institutional-grade financial analysis",
            "custom": "Custom LLM - User-defined model endpoint"
        }
        return descriptions.get(key, "Unknown provider")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current LLM manager status"""
        return {
            "active_provider": self.active_provider,
            "active_provider_name": self.providers[self.active_provider].name if self.active_provider else None,
            "available_providers": self.get_available_providers()
        }


# Global LLM manager instance
_llm_manager_instance = None

def get_llm_manager() -> LLMManager:
    """Get or create the global LLM manager instance"""
    global _llm_manager_instance
    if _llm_manager_instance is None:
        _llm_manager_instance = LLMManager()
    return _llm_manager_instance