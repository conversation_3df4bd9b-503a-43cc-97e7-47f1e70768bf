"""
Authentication Models - Separate database for user management and auth
Designed for scalability to millions/billions of users
"""

import os
from datetime import datetime, timedelta
from sqlalchemy import create_engine, Column, Integer, String, Boolean, DateTime, Text, ForeignKey, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.sql import func
import secrets
import pyotp
import qrcode
from io import BytesIO
import base64
from flask_bcrypt import Bcrypt

# Separate authentication database URL
AUTH_DATABASE_URL = os.environ.get("AUTH_DATABASE_URL") or os.environ.get("DATABASE_URL")

Base = declarative_base()
bcrypt = Bcrypt()

class User(Base):
    """User account model - core user information"""
    __tablename__ = 'users'
    
    id = Column(String(36), primary_key=True, default=lambda: secrets.token_hex(18))
    email = Column(String(255), unique=True, nullable=False, index=True)
    username = Column(String(50), unique=True, nullable=True, index=True)
    password_hash = Column(String(255), nullable=True)  # Nullable for OAuth-only users
    
    # Profile information
    first_name = Column(String(100), nullable=True)
    last_name = Column(String(100), nullable=True)
    profile_picture_url = Column(Text, nullable=True)
    
    # Account status
    is_active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    is_premium = Column(Boolean, default=False, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    last_login = Column(DateTime, nullable=True)
    email_verified_at = Column(DateTime, nullable=True)
    
    # Security settings
    mfa_enabled = Column(Boolean, default=False, nullable=False)
    mfa_secret = Column(String(32), nullable=True)
    backup_codes = Column(Text, nullable=True)  # JSON array of backup codes
    
    # Biometric settings
    webauthn_enabled = Column(Boolean, default=False, nullable=False)
    
    # Relationships
    oauth_accounts = relationship("OAuthAccount", back_populates="user", cascade="all, delete-orphan")
    sessions = relationship("UserSession", back_populates="user", cascade="all, delete-orphan")
    security_logs = relationship("SecurityLog", back_populates="user", cascade="all, delete-orphan")
    webauthn_credentials = relationship("WebAuthnCredential", back_populates="user", cascade="all, delete-orphan")
    trading_accounts = relationship("TradingAccount", back_populates="user", cascade="all, delete-orphan")
    
    # Indexes for performance at scale
    __table_args__ = (
        Index('idx_user_email_active', email, is_active),
        Index('idx_user_created', created_at),
        Index('idx_user_last_login', last_login),
    )
    
    def set_password(self, password):
        """Set hashed password"""
        self.password_hash = bcrypt.generate_password_hash(password).decode('utf-8')
    
    def check_password(self, password):
        """Check password against hash"""
        if not self.password_hash:
            return False
        return bcrypt.check_password_hash(self.password_hash, password)
    
    def enable_mfa(self):
        """Enable MFA and generate secret"""
        self.mfa_secret = pyotp.random_base32()
        self.mfa_enabled = True
        return self.mfa_secret
    
    def verify_mfa_token(self, token):
        """Verify MFA token"""
        if not self.mfa_enabled or not self.mfa_secret:
            return False
        totp = pyotp.TOTP(self.mfa_secret)
        return totp.verify(token)
    
    def get_mfa_qr_code(self, app_name="EZ Money Trading"):
        """Generate QR code for MFA setup"""
        if not self.mfa_secret:
            return None
        
        totp_uri = pyotp.totp.TOTP(self.mfa_secret).provisioning_uri(
            name=self.email,
            issuer_name=app_name
        )
        
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(totp_uri)
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        buffer = BytesIO()
        img.save(buffer, format='PNG')
        buffer.seek(0)
        
        return base64.b64encode(buffer.getvalue()).decode()
    
    def to_dict(self):
        """Convert user to dictionary for API responses"""
        return {
            'id': self.id,
            'email': self.email,
            'username': self.username,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'profile_picture_url': self.profile_picture_url,
            'is_active': self.is_active,
            'is_verified': self.is_verified,
            'is_premium': self.is_premium,
            'mfa_enabled': self.mfa_enabled,
            'webauthn_enabled': self.webauthn_enabled,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }

class OAuthAccount(Base):
    """OAuth provider accounts linked to users"""
    __tablename__ = 'oauth_accounts'
    
    id = Column(String(36), primary_key=True, default=lambda: secrets.token_hex(18))
    user_id = Column(String(36), ForeignKey('users.id'), nullable=False)
    provider = Column(String(50), nullable=False)  # google, facebook, apple, github
    provider_id = Column(String(255), nullable=False)  # Provider's user ID
    provider_email = Column(String(255), nullable=True)
    provider_name = Column(String(255), nullable=True)
    provider_picture = Column(Text, nullable=True)
    
    # OAuth tokens (encrypted in production)
    access_token = Column(Text, nullable=True)
    refresh_token = Column(Text, nullable=True)
    token_expires_at = Column(DateTime, nullable=True)
    
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    user = relationship("User", back_populates="oauth_accounts")
    
    __table_args__ = (
        Index('idx_oauth_provider_id', provider, provider_id),
        Index('idx_oauth_user', user_id),
    )

class UserSession(Base):
    """User session management for security and tracking"""
    __tablename__ = 'user_sessions'
    
    id = Column(String(36), primary_key=True, default=lambda: secrets.token_hex(18))
    user_id = Column(String(36), ForeignKey('users.id'), nullable=False)
    session_token = Column(String(255), unique=True, nullable=False)
    
    # Device and location info
    device_fingerprint = Column(String(255), nullable=True)
    ip_address = Column(String(45), nullable=True)  # IPv6 compatible
    user_agent = Column(Text, nullable=True)
    location = Column(String(255), nullable=True)  # City, Country
    
    # Session status
    is_active = Column(Boolean, default=True, nullable=False)
    expires_at = Column(DateTime, nullable=False)
    last_activity = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    user = relationship("User", back_populates="sessions")
    
    __table_args__ = (
        Index('idx_session_token', session_token),
        Index('idx_session_user_active', user_id, is_active),
        Index('idx_session_expires', expires_at),
    )
    
    @classmethod
    def create_session(cls, user_id, device_info=None, expires_hours=24):
        """Create new user session"""
        session_token = secrets.token_urlsafe(32)
        expires_at = datetime.utcnow() + timedelta(hours=expires_hours)
        
        return cls(
            user_id=user_id,
            session_token=session_token,
            expires_at=expires_at,
            device_fingerprint=device_info.get('fingerprint') if device_info else None,
            ip_address=device_info.get('ip') if device_info else None,
            user_agent=device_info.get('user_agent') if device_info else None,
            location=device_info.get('location') if device_info else None
        )

class SecurityLog(Base):
    """Security event logging for audit and monitoring"""
    __tablename__ = 'security_logs'
    
    id = Column(String(36), primary_key=True, default=lambda: secrets.token_hex(18))
    user_id = Column(String(36), ForeignKey('users.id'), nullable=True)  # Nullable for failed login attempts
    
    event_type = Column(String(50), nullable=False)  # login, logout, failed_login, mfa_enabled, etc.
    event_description = Column(Text, nullable=True)
    
    # Context information
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)
    location = Column(String(255), nullable=True)
    
    # Risk assessment
    risk_level = Column(String(20), default='low', nullable=False)  # low, medium, high, critical
    
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    user = relationship("User", back_populates="security_logs")
    
    __table_args__ = (
        Index('idx_security_user_event', user_id, event_type),
        Index('idx_security_created', created_at),
        Index('idx_security_risk', risk_level),
    )

class WebAuthnCredential(Base):
    """WebAuthn credentials for biometric authentication"""
    __tablename__ = 'webauthn_credentials'
    
    id = Column(String(36), primary_key=True, default=lambda: secrets.token_hex(18))
    user_id = Column(String(36), ForeignKey('users.id'), nullable=False)
    
    credential_id = Column(Text, nullable=False, unique=True)
    public_key = Column(Text, nullable=False)
    
    # Credential metadata
    name = Column(String(255), nullable=True)  # User-friendly name
    authenticator_type = Column(String(50), nullable=True)  # platform, cross-platform
    
    # Usage tracking
    sign_count = Column(Integer, default=0, nullable=False)
    last_used = Column(DateTime, nullable=True)
    
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    user = relationship("User", back_populates="webauthn_credentials")
    
    __table_args__ = (
        Index('idx_webauthn_user', user_id),
        Index('idx_webauthn_credential', credential_id),
    )

class TradingAccount(Base):
    """Trading account association with users"""
    __tablename__ = 'trading_accounts'
    
    id = Column(String(36), primary_key=True, default=lambda: secrets.token_hex(18))
    user_id = Column(String(36), ForeignKey('users.id'), nullable=False)
    
    # Account details
    account_name = Column(String(255), nullable=False)
    account_type = Column(String(50), default='demo', nullable=False)  # demo, live
    
    # Trading settings
    is_active = Column(Boolean, default=True, nullable=False)
    risk_level = Column(String(20), default='medium', nullable=False)
    
    # API credentials (encrypted)
    coinbase_api_key = Column(Text, nullable=True)
    coinbase_api_secret = Column(Text, nullable=True)
    
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    user = relationship("User", back_populates="trading_accounts")
    
    __table_args__ = (
        Index('idx_trading_user_active', user_id, is_active),
    )

class AuthDatabase:
    """Authentication database manager"""
    
    def __init__(self):
        self.engine = create_engine(
            AUTH_DATABASE_URL,
            pool_size=20,
            max_overflow=30,
            pool_pre_ping=True,
            pool_recycle=3600
        )
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
    
    def create_tables(self):
        """Create all authentication tables"""
        Base.metadata.create_all(bind=self.engine)
    
    def get_session(self):
        """Get database session"""
        return self.SessionLocal()
    
    def get_user_by_email(self, email):
        """Get user by email"""
        with self.get_session() as session:
            return session.query(User).filter(User.email == email).first()
    
    def get_user_by_id(self, user_id):
        """Get user by ID"""
        with self.get_session() as session:
            return session.query(User).filter(User.id == user_id).first()
    
    def create_user(self, email, password=None, **kwargs):
        """Create new user"""
        with self.get_session() as session:
            user = User(email=email, **kwargs)
            if password:
                user.set_password(password)
            session.add(user)
            session.commit()
            session.refresh(user)
            return user
    
    def log_security_event(self, user_id, event_type, description=None, ip_address=None, user_agent=None, risk_level='low'):
        """Log security event"""
        with self.get_session() as session:
            log = SecurityLog(
                user_id=user_id,
                event_type=event_type,
                event_description=description,
                ip_address=ip_address,
                user_agent=user_agent,
                risk_level=risk_level
            )
            session.add(log)
            session.commit()

# Global auth database instance
auth_db = AuthDatabase()