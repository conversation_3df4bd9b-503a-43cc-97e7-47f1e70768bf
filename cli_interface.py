"""
Command Line Interface for monitoring and controlling the trading agent
"""

import sys
import select
import json
from datetime import datetime
from typing import Optional, Dict, Any
import time

from trading_agent import TradingAgent
from logger_config import setup_logger

class CLIInterface:
    """Command Line Interface for the trading agent"""
    
    def __init__(self, agent: TradingAgent):
        """Initialize CLI interface"""
        self.agent = agent
        self.logger = setup_logger("CLI")
        self.last_status_display = 0
        self.display_interval = 30  # Display status every 30 seconds
        
        # Available commands
        self.commands = {
            'status': 'Show current agent status',
            'balance': 'Show wallet balance',
            'history': 'Show transaction history',
            'strategy': 'Show strategy information',
            'buy': 'Force buy order (testing)',
            'sell': 'Force sell order (testing)',
            'help': 'Show available commands',
            'quit': 'Exit the application',
            'q': 'Exit the application (shortcut)',
            'exit': 'Exit the application'
        }
        
        self.logger.info("CLI interface initialized")
    
    def display_status(self):
        """Display current agent status"""
        current_time = time.time()
        
        # Only display status at intervals to avoid spam
        if current_time - self.last_status_display < self.display_interval:
            return
        
        self.last_status_display = current_time
        
        try:
            # Clear screen (simple version)
            print("\n" + "="*80)
            print(f"EZ Money Trading Agent - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print("="*80)
            
            # Get agent status
            status = self.agent.get_status()
            
            if 'error' in status:
                print(f"❌ Error: {status['error']}")
                return
            
            # Display basic status
            print(f"🤖 Agent Status: {'🟢 RUNNING' if status['is_running'] else '🔴 STOPPED'}")
            print(f"📊 Trading Pair: {status['trading_pair']}")
            print(f"🎯 Mode: {'DRY RUN' if status['dry_run'] else 'LIVE TRADING'}")
            print(f"📈 Daily Trades: {status['daily_trades']}/{status['max_daily_trades']}")
            
            # Display current price
            if status['current_price']:
                print(f"💰 Current Price: ${status['current_price']:.2f}")
            
            # Display wallet balance
            if status['balance']:
                print("\n💼 Wallet Balance:")
                for currency, amount in status['balance'].items():
                    if currency == 'USD':
                        print(f"   💵 {currency}: ${amount:.2f}")
                    else:
                        print(f"   🪙 {currency}: {amount:.8f}")
            
            # Display recent activity
            try:
                history = self.agent.get_transaction_history(3)
                print("\n📋 Recent Transactions:")
                if history:
                    for tx in history[:3]:
                        tx_time = datetime.fromtimestamp(float(tx['timestamp'])).strftime('%H:%M:%S')
                        tx_type = "🟢 BUY" if tx['type'].lower() == 'buy' else "🔴 SELL"
                        print(f"   {tx_time} | {tx_type} | {tx['amount']} {tx['currency']} | ${tx['fiat_amount']}")
                else:
                    if status.get('dry_run', False):
                        print("   📝 No transactions yet (DRY RUN mode)")
                        print("   💡 Connect to Coinbase API for real trading")
                    else:
                        print("   📝 No recent transactions")
            except Exception as e:
                print(f"   ❌ Could not load transaction history: {str(e)}")
            
            print("\n" + "-"*80)
            print("💡 Commands: 'help' for options, 'quit' to exit")
            print("➤ ", end="", flush=True)
            
        except Exception as e:
            self.logger.error(f"Error displaying status: {str(e)}")
            print(f"❌ Error displaying status: {str(e)}")
    
    def get_user_input(self) -> Optional[str]:
        """Get user input in a non-blocking way"""
        try:
            # Check if input is available (Unix/Linux only)
            if hasattr(select, 'select'):
                if select.select([sys.stdin], [], [], 0) == ([sys.stdin], [], []):
                    return input().strip()
            else:
                # Fallback for Windows - this will block
                # In a real implementation, you might want to use threading
                pass
            
            return None
            
        except (EOFError, KeyboardInterrupt):
            return 'quit'
        except Exception as e:
            self.logger.error(f"Error getting user input: {str(e)}")
            return None
    
    def handle_command(self, command: str):
        """Handle user commands"""
        try:
            command = command.lower().strip()
            
            if command in ['quit', 'q', 'exit']:
                return False
            
            elif command == 'help':
                self._show_help()
            
            elif command == 'status':
                self._show_detailed_status()
            
            elif command == 'balance':
                self._show_balance()
            
            elif command == 'history':
                self._show_transaction_history()
            
            elif command == 'strategy':
                self._show_strategy_info()
            
            elif command == 'buy':
                self._force_buy_order()
            
            elif command == 'sell':
                self._force_sell_order()
            
            else:
                print(f"❓ Unknown command: '{command}'. Type 'help' for available commands.")
            
            print("\n➤ ", end="", flush=True)
            return True
            
        except Exception as e:
            self.logger.error(f"Error handling command '{command}': {str(e)}")
            print(f"❌ Error executing command: {str(e)}")
            return True
    
    def _show_help(self):
        """Show available commands"""
        print("\n📖 Available Commands:")
        print("-" * 50)
        for cmd, description in self.commands.items():
            print(f"  {cmd:<10} - {description}")
        print("-" * 50)
    
    def _show_detailed_status(self):
        """Show detailed agent status"""
        try:
            status = self.agent.get_status()
            print("\n📊 Detailed Agent Status:")
            print("-" * 50)
            print(json.dumps(status, indent=2, default=str))
            print("-" * 50)
            
        except Exception as e:
            print(f"❌ Error showing status: {str(e)}")
    
    def _show_balance(self):
        """Show wallet balance"""
        try:
            status = self.agent.get_status()
            balance = status.get('balance', {})
            
            print("\n💼 Current Wallet Balance:")
            print("-" * 30)
            
            if balance:
                total_usd_value = 0
                current_price = status.get('current_price', 0)
                
                for currency, amount in balance.items():
                    if currency == 'USD':
                        print(f"💵 {currency}: ${amount:.2f}")
                        total_usd_value += amount
                    else:
                        usd_value = amount * current_price if current_price else 0
                        print(f"🪙 {currency}: {amount:.8f} (≈${usd_value:.2f})")
                        total_usd_value += usd_value
                
                print("-" * 30)
                print(f"💰 Total Portfolio Value: ≈${total_usd_value:.2f}")
            else:
                print("❌ No balance information available")
            
            print("-" * 30)
            
        except Exception as e:
            print(f"❌ Error showing balance: {str(e)}")
    
    def _show_transaction_history(self):
        """Show transaction history"""
        try:
            history = self.agent.get_transaction_history(10)
            
            print("\n📋 Transaction History (Last 10):")
            print("-" * 80)
            
            if history:
                print(f"{'Time':<12} {'Type':<6} {'Amount':<15} {'Currency':<8} {'Value':<12} {'Status':<10}")
                print("-" * 80)
                
                for tx in history:
                    tx_time = datetime.fromtimestamp(float(tx['timestamp'])).strftime('%H:%M:%S')
                    tx_type = tx['type'].upper()
                    amount = f"{float(tx['amount']):.8f}"[:14]
                    currency = tx['currency']
                    value = f"${float(tx['fiat_amount']):.2f}"
                    status = tx['status']
                    
                    print(f"{tx_time:<12} {tx_type:<6} {amount:<15} {currency:<8} {value:<12} {status:<10}")
            else:
                print("❌ No transaction history available")
            
            print("-" * 80)
            
        except Exception as e:
            print(f"❌ Error showing transaction history: {str(e)}")
    
    def _show_strategy_info(self):
        """Show trading strategy information"""
        try:
            strategy_status = self.agent.trading_strategy.get_strategy_status()
            
            print("\n🎯 Trading Strategy Information:")
            print("-" * 50)
            print(json.dumps(strategy_status, indent=2, default=str))
            print("-" * 50)
            
        except Exception as e:
            print(f"❌ Error showing strategy info: {str(e)}")
    
    def _force_buy_order(self):
        """Force a buy order (for testing)"""
        try:
            print("\n⚠️  WARNING: This will execute a real buy order!")
            print("Are you sure you want to continue? (y/N): ", end="", flush=True)
            
            confirmation = input().strip().lower()
            if confirmation != 'y':
                print("❌ Buy order cancelled")
                return
            
            print("🔄 Executing forced buy order...")
            success = self.agent.force_trade_signal('buy')
            
            if success:
                print("✅ Buy order executed successfully")
            else:
                print("❌ Buy order failed")
                
        except Exception as e:
            print(f"❌ Error executing buy order: {str(e)}")
    
    def _force_sell_order(self):
        """Force a sell order (for testing)"""
        try:
            print("\n⚠️  WARNING: This will execute a real sell order!")
            print("Are you sure you want to continue? (y/N): ", end="", flush=True)
            
            confirmation = input().strip().lower()
            if confirmation != 'y':
                print("❌ Sell order cancelled")
                return
            
            print("🔄 Executing forced sell order...")
            success = self.agent.force_trade_signal('sell')
            
            if success:
                print("✅ Sell order executed successfully")
            else:
                print("❌ Sell order failed")
                
        except Exception as e:
            print(f"❌ Error executing sell order: {str(e)}")
    
    def show_welcome_message(self):
        """Show welcome message"""
        print("\n" + "="*80)
        print("🤖 AI Cryptocurrency Trading Agent")
        print("🚀 Powered by Coinbase AgentKit")
        print("="*80)
        print("💡 Type 'help' to see available commands")
        print("⚠️  Remember: This is real money! Trade responsibly.")
        print("🛡️  All trades are logged for your review")
        print("-"*80)
