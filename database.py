"""
Database models and connection management for the trading agent
"""

import os
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional, List
from sqlalchemy import create_engine, Column, Integer, String, Float, DateTime, Boolean, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.sql import func
from logger_config import setup_logger

Base = declarative_base()
logger = setup_logger("Database")

class Portfolio(Base):
    """Portfolio balance tracking"""
    __tablename__ = 'portfolios'
    
    id = Column(Integer, primary_key=True)
    currency = Column(String(10), nullable=False)
    amount = Column(Float, nullable=False)
    value_usd = Column(Float, nullable=False)
    percentage = Column(Float, nullable=False)
    total_portfolio_value = Column(Float, nullable=False)
    timestamp = Column(DateTime, default=datetime.utcnow)
    
class Transaction(Base):
    """Trading transaction records"""
    __tablename__ = 'transactions'
    
    id = Column(Integer, primary_key=True)
    transaction_id = Column(String(100), unique=True, nullable=False)
    type = Column(String(10), nullable=False)  # 'buy' or 'sell'
    currency = Column(String(10), nullable=False)
    amount = Column(Float, nullable=False)
    fiat_currency = Column(String(10), nullable=False) 
    fiat_amount = Column(Float, nullable=False)
    price = Column(Float, nullable=False)
    status = Column(String(20), nullable=False)
    is_simulated = Column(Boolean, default=False)
    timestamp = Column(DateTime, default=datetime.utcnow)
    
class MarketData(Base):
    """Historical market data"""
    __tablename__ = 'market_data'
    
    id = Column(Integer, primary_key=True)
    trading_pair = Column(String(20), nullable=False)
    price = Column(Float, nullable=False)
    volume_24h = Column(Float)
    price_change_24h = Column(Float)
    market_cap = Column(Float)
    source = Column(String(50), nullable=False)
    timestamp = Column(DateTime, default=datetime.utcnow)

class TradingStrategy(Base):
    """Trading strategy performance and metrics"""
    __tablename__ = 'trading_strategies'
    
    id = Column(Integer, primary_key=True)
    strategy_name = Column(String(100), nullable=False)
    signal_type = Column(String(10), nullable=False)  # 'buy', 'sell', 'hold'
    confidence = Column(Float, nullable=False)
    reason = Column(Text)
    executed = Column(Boolean, default=False)
    result = Column(String(20))  # 'success', 'failed', 'pending'
    profit_loss = Column(Float)
    timestamp = Column(DateTime, default=datetime.utcnow)

class AgentConfig(Base):
    """Agent configuration settings"""
    __tablename__ = 'agent_configs'
    
    id = Column(Integer, primary_key=True)
    config_key = Column(String(50), unique=True, nullable=False)
    config_value = Column(Text, nullable=False)
    last_updated = Column(DateTime, default=datetime.utcnow)

class DatabaseManager:
    """Manages database connections and operations"""
    
    def __init__(self):
        self.database_url = os.getenv('DATABASE_URL')
        if not self.database_url:
            raise ValueError("DATABASE_URL environment variable not found")
        
        self.engine = create_engine(self.database_url)
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        
        # Create tables
        Base.metadata.create_all(bind=self.engine)
        logger.info("Database initialized and tables created")
    
    def get_session(self) -> Session:
        """Get a database session"""
        return self.SessionLocal()
    
    def save_portfolio_snapshot(self, portfolio_data: dict):
        """Save current portfolio state"""
        session = self.get_session()
        try:
            timestamp = datetime.utcnow()
            total_value = portfolio_data.get('total_value_usd', 0)
            
            for currency, data in portfolio_data.get('portfolio', {}).items():
                portfolio_record = Portfolio(
                    currency=currency,
                    amount=data['amount'],
                    value_usd=data['value_usd'],
                    percentage=data['percentage'],
                    total_portfolio_value=total_value,
                    timestamp=timestamp
                )
                session.add(portfolio_record)
            
            session.commit()
            logger.debug(f"Portfolio snapshot saved: {len(portfolio_data.get('portfolio', {}))} currencies")
            
        except Exception as e:
            session.rollback()
            logger.error(f"Error saving portfolio snapshot: {str(e)}")
        finally:
            session.close()
    
    def save_transaction(self, transaction_data: dict):
        """Save a transaction record"""
        session = self.get_session()
        try:
            transaction = Transaction(
                transaction_id=transaction_data['id'],
                type=transaction_data['type'],
                currency=transaction_data['currency'],
                amount=float(transaction_data['amount']),
                fiat_currency=transaction_data.get('fiat_currency', 'USD'),
                fiat_amount=float(transaction_data['fiat_amount']),
                price=transaction_data.get('price', 0),
                status=transaction_data['status'],
                is_simulated=transaction_data.get('is_simulated', True),
                timestamp=datetime.fromtimestamp(transaction_data['timestamp'])
            )
            session.add(transaction)
            session.commit()
            logger.info(f"Transaction saved: {transaction_data['type']} {transaction_data['amount']} {transaction_data['currency']}")
            
        except Exception as e:
            session.rollback()
            logger.error(f"Error saving transaction: {str(e)}")
        finally:
            session.close()
    
    def get_transactions(self, limit: int = 50) -> List[dict]:
        """Get recent transactions"""
        session = self.get_session()
        try:
            transactions = session.query(Transaction)\
                .order_by(Transaction.timestamp.desc())\
                .limit(limit)\
                .all()
            
            result = []
            for tx in transactions:
                result.append({
                    'id': tx.transaction_id,
                    'type': tx.type,
                    'currency': tx.currency,
                    'amount': str(tx.amount),
                    'fiat_currency': tx.fiat_currency,
                    'fiat_amount': str(tx.fiat_amount),
                    'price': tx.price,
                    'status': tx.status,
                    'is_simulated': tx.is_simulated,
                    'timestamp': tx.timestamp.timestamp()
                })
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting transactions: {str(e)}")
            return []
        finally:
            session.close()
    
    def save_market_data(self, trading_pair: str, market_data: dict, source: str = "coingecko"):
        """Save market data snapshot"""
        session = self.get_session()
        try:
            market_record = MarketData(
                trading_pair=trading_pair,
                price=market_data.get('price', 0),
                volume_24h=market_data.get('volume_24h'),
                price_change_24h=market_data.get('price_change_24h'),
                market_cap=market_data.get('market_cap'),
                source=source
            )
            session.add(market_record)
            session.commit()
            logger.debug(f"Market data saved for {trading_pair}")
            
        except Exception as e:
            session.rollback()
            logger.error(f"Error saving market data: {str(e)}")
        finally:
            session.close()
    
    def save_strategy_signal(self, strategy_name: str, signal_data: dict):
        """Save trading strategy signal"""
        session = self.get_session()
        try:
            strategy_record = TradingStrategy(
                strategy_name=strategy_name,
                signal_type=signal_data.get('action', 'hold'),
                confidence=signal_data.get('confidence', 0),
                reason=signal_data.get('reason', ''),
                executed=signal_data.get('executed', False),
                result=signal_data.get('result'),
                profit_loss=signal_data.get('profit_loss')
            )
            session.add(strategy_record)
            session.commit()
            logger.debug(f"Strategy signal saved: {strategy_name}")
            
        except Exception as e:
            session.rollback()
            logger.error(f"Error saving strategy signal: {str(e)}")
        finally:
            session.close()
    
    def get_portfolio_history(self, currency: str = None, days: int = 30) -> List[dict]:
        """Get portfolio value history"""
        session = self.get_session()
        try:
            query = session.query(Portfolio)
            
            if currency:
                query = query.filter(Portfolio.currency == currency)
            
            # Get data from last N days
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            query = query.filter(Portfolio.timestamp >= cutoff_date)
            
            portfolios = query.order_by(Portfolio.timestamp.desc()).all()
            
            result = []
            for p in portfolios:
                result.append({
                    'currency': p.currency,
                    'amount': p.amount,
                    'value_usd': p.value_usd,
                    'percentage': p.percentage,
                    'total_portfolio_value': p.total_portfolio_value,
                    'timestamp': p.timestamp.timestamp()
                })
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting portfolio history: {str(e)}")
            return []
        finally:
            session.close()
    
    def get_trading_performance(self, days: int = 30) -> dict:
        """Get trading performance metrics"""
        session = self.get_session()
        try:
            from datetime import timedelta
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            
            # Total trades
            total_trades = session.query(Transaction)\
                .filter(Transaction.timestamp >= cutoff_date)\
                .count()
            
            # Buy vs Sell counts
            buy_count = session.query(Transaction)\
                .filter(Transaction.timestamp >= cutoff_date)\
                .filter(Transaction.type == 'buy')\
                .count()
            
            sell_count = session.query(Transaction)\
                .filter(Transaction.timestamp >= cutoff_date)\
                .filter(Transaction.type == 'sell')\
                .count()
            
            # Total volume traded
            total_volume = session.query(func.sum(Transaction.fiat_amount))\
                .filter(Transaction.timestamp >= cutoff_date)\
                .scalar() or 0
            
            return {
                'total_trades': total_trades,
                'buy_count': buy_count,
                'sell_count': sell_count,
                'total_volume_usd': float(total_volume),
                'period_days': days
            }
            
        except Exception as e:
            logger.error(f"Error getting trading performance: {str(e)}")
            return {}
        finally:
            session.close()
    
    def save_agent_config(self, key: str, value: str):
        """Save agent configuration setting"""
        session = self.get_session()
        try:
            # Check if config exists
            existing = session.query(AgentConfig).filter(AgentConfig.config_key == key).first()
            
            if existing:
                existing.config_value = value
                existing.last_updated = datetime.utcnow()
            else:
                config = AgentConfig(config_key=key, config_value=value)
                session.add(config)
            
            session.commit()
            logger.debug(f"Agent config saved: {key}")
            
        except Exception as e:
            session.rollback()
            logger.error(f"Error saving agent config: {str(e)}")
        finally:
            session.close()
    
    def get_agent_config(self, key: str, default: str = None) -> str:
        """Get agent configuration setting"""
        session = self.get_session()
        try:
            config = session.query(AgentConfig).filter(AgentConfig.config_key == key).first()
            return config.config_value if config else default
            
        except Exception as e:
            logger.error(f"Error getting agent config: {str(e)}")
            return default
        finally:
            session.close()

# Global database manager instance
db_manager = None

def get_db_manager() -> DatabaseManager:
    """Get the global database manager instance"""
    global db_manager
    if db_manager is None:
        db_manager = DatabaseManager()
    return db_manager