const { spawn } = require('child_process');
const path = require('path');
const axios = require('axios');
const MarketDataService = require('./market_data_service');
const { bitcoinWalletManager, Coinbase } = require('./bitcoin_wallet');

/**
 * Trading Integration Module
 * Handles communication between MultiLogin and CryptoTraderAI
 */

class TradingIntegration {
  constructor() {
    this.cryptoTraderPath = path.join(__dirname, '..', 'CryptoTraderAI-1');
    this.pythonProcess = null;
    this.isRunning = false; // Start as stopped
    this.tradingFeatures = {
      walletBalance: 10000, // Demo balance
      activeStrategies: ['DCA', 'Momentum', 'Mean Reversion'],
      supportedCoins: ['BTC', 'ETH', 'ADA', 'SOL'],
      riskLevel: 'Medium'
    };

    // Initialize market data service (microservice for Kubernetes)
    this.marketDataService = new MarketDataService({
      cacheTimeout: 60000, // 1 minute cache
      retryAttempts: 3,
      retryDelay: 1000
    });

    // Initialize Bitcoin wallet manager
    this.bitcoinWalletManager = bitcoinWalletManager;

    console.log('Trading integration initialized - session stopped by default');
  }

  /**
   * Initialize trading session (simplified - no external process)
   */
  initializeTradingSession(user) {
    console.log(`Initializing trading session for user: ${user.email}`);
    this.isRunning = true;
    return {
      success: true,
      message: 'Trading session initialized',
      user: user.email,
      features: this.tradingFeatures
    };
  }

  /**
   * Stop trading session
   */
  stopTradingSession(user) {
    console.log(`Stopping trading session for user: ${user.email}`);
    this.isRunning = false;
    return {
      success: true,
      message: 'Trading session stopped',
      user: user.email
    };
  }

  /**
   * Get current trading session status
   */
  getSessionStatus(user) {
    return {
      isRunning: this.isRunning,
      user: user ? user.email : 'anonymous',
      features: this.tradingFeatures,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Get wallet data using Bitcoin wallet manager
   */
  async getWalletData(userId) {
    try {
      console.log(`Getting wallet data for user: ${userId}`);

      // Try to get existing wallet
      let walletData = await this.bitcoinWalletManager.getUserWallet(userId);

      if (!walletData) {
        console.log(`No wallet found for user ${userId}, creating new wallet...`);
        // Create new wallet if none exists
        walletData = await this.bitcoinWalletManager.createWalletForUser(userId, `user_${userId}@multilogin.app`);
      }

      // Calculate total value in USD using ETH price
      const ethPrice = 2000; // Approximate ETH price in USD
      const totalValue = (walletData.balance?.ETH || 0) * ethPrice + (walletData.balance?.USD || 0);

      return {
        holdings: walletData.balance || { ETH: 0, USD: 0 },
        totalValue: totalValue,
        isReal: walletData.isActive || false,
        walletAddress: walletData.address,
        status: walletData.isActive ? 'active' : 'inactive',
        network: walletData.network === 'base-sepolia' ? 'Base Sepolia' : walletData.network,
        createdAt: walletData.createdAt
      };

    } catch (error) {
      console.error('Error getting wallet data:', error.message);
      return {
        isReal: false,
        error: error.message,
        holdings: { ETH: 0, USD: 0 },
        totalValue: 0
      };
    }
  }

  /**
   * Get real market data using JavaScript market data provider
   */
  async getMarketData() {
    try {
      console.log('Fetching real market data...');
      const symbols = ['BTC', 'ETH', 'ADA', 'SOL'];
      const prices = await this.marketDataService.getPrices(symbols);

      return {
        prices: prices,
        isReal: true,
        timestamp: new Date().toISOString(),
        source: 'live_api'
      };
    } catch (error) {
      console.error('Error getting market data:', error.message);
      // Return fallback data
      return {
        prices: {
          BTC: 45000,
          ETH: 3000,
          ADA: 0.5,
          SOL: 100
        },
        isReal: false,
        error: error.message,
        source: 'fallback'
      };
    }
  }

  /**
   * Setup trading routes for the Express app
   */
  setupRoutes(app) {
    console.log('Setting up trading integration routes...');

    // Middleware to ensure user is authenticated
    const requireAuth = (req, res, next) => {
      if (!req.isAuthenticated()) {
        return res.redirect('/login');
      }
      next();
    };

    // Trading dashboard - serve directly from Node.js
    app.get('/trading', requireAuth, (req, res) => {
      res.render('trading_dashboard', {
        user: req.user,
        tradingFeatures: this.tradingFeatures,
        isIntegrated: true
      });
    });

    // Trading API endpoints
    app.get('/api/trading/wallet', requireAuth, async (req, res) => {
      try {
        // Get real ETH wallet data from Base Sepolia
        const walletData = await this.getWalletData(req.user.id);
        res.json({
          balance: walletData.totalValue || 0,
          currency: 'USD',
          holdings: walletData.holdings || { BTC: 0, USD: 0 },
          totalValue: walletData.totalValue || 0,
          user: req.user.email,
          isReal: walletData.isReal || false,
          walletAddress: walletData.walletAddress,
          network: walletData.network,
          status: walletData.status,
          lastUpdated: new Date().toISOString()
        });
      } catch (error) {
        console.error('Error getting wallet data:', error);
        // Fallback to demo data
        res.json({
          balance: 0,
          currency: 'USD',
          holdings: { BTC: 0, USD: 0 },
          totalValue: 0,
          user: req.user.email,
          isReal: false,
          error: 'Wallet service unavailable: ' + error.message
        });
      }
    });

    app.get('/api/trading/strategies', requireAuth, (req, res) => {
      res.json({
        active: this.tradingFeatures.activeStrategies,
        available: ['DCA', 'Momentum', 'Mean Reversion', 'Grid Trading', 'Arbitrage'],
        riskLevel: this.tradingFeatures.riskLevel
      });
    });

    app.get('/api/trading/market', requireAuth, async (req, res) => {
      try {
        // Try to get real market data
        const marketData = await this.getMarketData();
        res.json({
          prices: marketData.prices,
          timestamp: new Date().toISOString(),
          source: marketData.isReal ? 'Live Market Data' : 'Demo Data',
          isReal: marketData.isReal
        });
      } catch (error) {
        console.error('Error getting market data:', error);
        // Fallback to simulated data
        const mockPrices = {
          BTC: 45000 + Math.random() * 1000,
          ETH: 3000 + Math.random() * 200,
          ADA: 0.5 + Math.random() * 0.1,
          SOL: 100 + Math.random() * 10
        };

        res.json({
          prices: mockPrices,
          timestamp: new Date().toISOString(),
          source: 'Demo Data (Fallback)',
          isReal: false,
          error: 'Market data service unavailable'
        });
      }
    });

    // Trading status endpoint - simplified
    app.get('/trading/status', requireAuth, (req, res) => {
      const status = this.isRunning ? 'running' : 'stopped';
      const message = this.isRunning
        ? 'Trading session is active'
        : 'Trading session is stopped';

      res.json({
        status: status,
        message: message,
        isRunning: this.isRunning,
        features: this.tradingFeatures,
        user: {
          id: req.user.id,
          email: req.user.email,
          name: req.user.name
        },
        timestamp: new Date().toISOString()
      });
    });

    // Start trading session
    app.post('/trading/start', requireAuth, (req, res) => {
      this.isRunning = true;
      console.log(`Trading session started for user: ${req.user.email}`);
      res.json({
        success: true,
        message: 'Trading session activated',
        user: req.user.email,
        timestamp: new Date().toISOString()
      });
    });

    // Stop trading session
    app.post('/trading/stop', requireAuth, (req, res) => {
      this.isRunning = false;
      console.log(`Trading session stopped for user: ${req.user.email}`);
      res.json({
        success: true,
        message: 'Trading session deactivated',
        user: req.user.email,
        timestamp: new Date().toISOString()
      });
    });

    // Market data service health check (for Kubernetes readiness/liveness probes)
    app.get('/api/trading/market/health', requireAuth, async (req, res) => {
      try {
        const healthCheck = await this.marketDataService.healthCheck();
        res.json(healthCheck);
      } catch (error) {
        res.status(500).json({
          status: 'error',
          service: 'MarketDataService',
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    });

    // Market data service cache statistics
    app.get('/api/trading/market/cache', requireAuth, (req, res) => {
      try {
        const cacheStats = this.marketDataService.getCacheStats();
        res.json({
          status: 'success',
          service: 'MarketDataService',
          cache: cacheStats,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        res.status(500).json({
          status: 'error',
          service: 'MarketDataService',
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    });

    // Clear market data cache (for testing/debugging)
    app.post('/api/trading/market/cache/clear', requireAuth, (req, res) => {
      try {
        this.marketDataService.clearCache();
        res.json({
          status: 'success',
          service: 'MarketDataService',
          message: 'Cache cleared successfully',
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        res.status(500).json({
          status: 'error',
          service: 'MarketDataService',
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    });

    // Bitcoin wallet specific endpoints
    app.get('/api/trading/wallet/address', requireAuth, async (req, res) => {
      try {
        const walletData = await this.getWalletData(req.user.id);
        res.json({
          address: walletData.walletAddress,
          network: walletData.network,
          user: req.user.email,
          walletId: walletData.walletId,
          isReal: walletData.isReal || false
        });
      } catch (error) {
        res.status(500).json({
          error: 'Failed to get wallet address: ' + error.message
        });
      }
    });

    // Get detailed wallet information for current user
    app.get('/api/trading/wallet/my-wallet', requireAuth, async (req, res) => {
      try {
        const userId = req.user.id;
        console.log(`🔍 Getting detailed wallet info for user ${userId}...`);

        // Get user's wallet from the wallet manager
        const userWallet = await this.bitcoinWalletManager.getUserWallet(userId);

        if (!userWallet) {
          return res.json({
            hasWallet: false,
            message: 'No wallet found for user',
            userId: userId
          });
        }

        // Get the full wallet details from Coinbase
        const fullWalletData = await this.bitcoinWalletManager.getFullWalletData(userWallet.id);

        res.json({
          hasWallet: true,
          walletId: userWallet.id,
          address: userWallet.address,
          network: userWallet.network,
          createdAt: userWallet.createdAt,
          userEmail: userWallet.userEmail,
          balance: fullWalletData.balance || { ETH: 0, USD: 0 },
          isActive: fullWalletData.isActive || false,
          userId: userId
        });

      } catch (error) {
        console.error('Error getting user wallet details:', error);
        res.status(500).json({
          error: 'Failed to get wallet details: ' + error.message,
          hasWallet: false
        });
      }
    });

    app.post('/api/trading/wallet/send', requireAuth, async (req, res) => {
      try {
        const { toAddress, amount } = req.body;

        if (!toAddress || !amount) {
          return res.status(400).json({
            error: 'Missing required fields: toAddress, amount'
          });
        }

        const result = await this.bitcoinWalletManager.sendBitcoin(
          req.user.id,
          toAddress,
          parseFloat(amount)
        );

        res.json(result);
      } catch (error) {
        res.status(500).json({
          error: 'Failed to send Bitcoin: ' + error.message
        });
      }
    });

    app.get('/api/trading/wallet/health', requireAuth, async (req, res) => {
      try {
        const healthCheck = await this.bitcoinWalletManager.healthCheck();
        res.json(healthCheck);
      } catch (error) {
        res.status(500).json({
          status: 'error',
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    });

    // Test Coinbase API connection
    app.get('/api/trading/wallet/test-api', requireAuth, async (req, res) => {
      try {
        const testResult = await this.bitcoinWalletManager.testApiConnection();
        res.json({
          ...testResult,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: 'API test failed: ' + error.message,
          timestamp: new Date().toISOString()
        });
      }
    });

    // List all Coinbase wallets (mainnet and testnet)
    app.get('/api/trading/wallet/list-all', requireAuth, async (req, res) => {
      try {
        const walletsData = await this.bitcoinWalletManager.listAllCoinbaseWallets();
        res.json(walletsData);
      } catch (error) {
        res.status(500).json({
          success: false,
          error: 'Failed to list wallets: ' + error.message,
          timestamp: new Date().toISOString()
        });
      }
    });

    // Create new wallet
    app.post('/api/trading/wallet/create', requireAuth, async (req, res) => {
      try {
        const { network = 'testnet' } = req.body;
        const result = await this.bitcoinWalletManager.createNewWallet(network);
        res.json(result);
      } catch (error) {
        res.status(500).json({
          success: false,
          error: 'Failed to create wallet: ' + error.message,
          timestamp: new Date().toISOString()
        });
      }
    });

    // Request faucet funds
    app.post('/api/trading/wallet/faucet', requireAuth, async (req, res) => {
      try {
        const { walletId } = req.body;

        if (!walletId) {
          return res.status(400).json({
            success: false,
            error: 'Wallet ID is required'
          });
        }

        const result = await this.bitcoinWalletManager.requestFaucet(walletId);
        res.json(result);
      } catch (error) {
        res.status(500).json({
          success: false,
          error: 'Failed to request faucet: ' + error.message,
          timestamp: new Date().toISOString()
        });
      }
    });

    // Fund wallet with test BTC
    app.post('/api/trading/wallet/fund-test', requireAuth, async (req, res) => {
      try {
        const { walletId, amount = 500 } = req.body;

        if (!walletId) {
          return res.status(400).json({
            success: false,
            error: 'Wallet ID is required'
          });
        }

        const result = await this.bitcoinWalletManager.fundWalletWithTestBTC(walletId, amount);
        res.json(result);
      } catch (error) {
        res.status(500).json({
          success: false,
          error: 'Failed to fund wallet: ' + error.message,
          timestamp: new Date().toISOString()
        });
      }
    });

    // Fund default wallet for current user
    app.post('/api/trading/wallet/fund-default', requireAuth, async (req, res) => {
      try {
        const { amount = 500 } = req.body;
        const userId = req.user.id;

        const result = await this.bitcoinWalletManager.fundDefaultWalletWithTestBTC(userId, amount);
        res.json(result);
      } catch (error) {
        res.status(500).json({
          success: false,
          error: 'Failed to fund default wallet: ' + error.message,
          timestamp: new Date().toISOString()
        });
      }
    });

    // Request maximum funding for a wallet
    app.post('/api/trading/wallet/fund-maximum', requireAuth, async (req, res) => {
      try {
        const { walletId } = req.body;

        if (!walletId) {
          return res.status(400).json({
            success: false,
            error: 'Wallet ID is required'
          });
        }

        const result = await this.bitcoinWalletManager.requestMaximumFunding(walletId);
        res.json(result);
      } catch (error) {
        res.status(500).json({
          success: false,
          error: 'Failed to request maximum funding: ' + error.message,
          timestamp: new Date().toISOString()
        });
      }
    });

    console.log('Trading integration routes configured');
  }

  /**
   * Create a proxy middleware for trading API calls
   */
  createTradingProxy() {
    return async (req, res) => {
      try {
        if (!this.isRunning) {
          return res.status(503).json({ error: 'Trading service is not available' });
        }

        const targetUrl = `${this.apiBaseUrl}/api${req.path}`;
        const config = {
          method: req.method,
          url: targetUrl,
          headers: {
            ...req.headers,
            'X-User-ID': req.user.id,
            'X-User-Email': req.user.email
          }
        };

        if (req.body && Object.keys(req.body).length > 0) {
          config.data = req.body;
        }

        const response = await axios(config);
        res.status(response.status).json(response.data);

      } catch (error) {
        console.error('Trading proxy error:', error.message);
        if (error.response) {
          res.status(error.response.status).json(error.response.data);
        } else {
          res.status(500).json({ error: 'Trading service error' });
        }
      }
    };
  }

  /**
   * Get trading service status
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      processId: this.pythonProcess ? this.pythonProcess.pid : null,
      apiUrl: this.apiBaseUrl
    };
  }

  /**
   * Initialize the trading integration
   */
  async initialize() {
    try {
      // Test Bitcoin wallet manager connection
      const healthCheck = await this.bitcoinWalletManager.testApiConnection();
      if (healthCheck.success) {
        console.log('✅ Bitcoin wallet manager API connection successful');
      } else {
        console.log('⚠️ Bitcoin wallet manager API connection failed:', healthCheck.error);
      }

      // Test market data service
      const marketHealthCheck = await this.marketDataService.healthCheck();
      if (marketHealthCheck.status === 'healthy') {
        console.log('✅ Market data service initialized successfully');
      } else {
        console.log('⚠️ Market data service health check failed:', marketHealthCheck.error);
      }

      console.log('✅ Trading integration initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize trading integration:', error);
      console.log('⚠️  Continuing without full functionality');
    }
  }

  /**
   * Graceful shutdown for Kubernetes
   */
  async shutdown() {
    console.log('🛑 Trading integration shutting down...');

    try {
      // Shutdown market data service
      await this.marketDataService.shutdown();
      console.log('✅ Market data service shutdown complete');
    } catch (error) {
      console.error('❌ Error during market data service shutdown:', error);
    }

    console.log('✅ Trading integration shutdown complete');
  }

  /**
   * Check if required dependencies are available
   */
  async checkDependencies() {
    return new Promise((resolve, reject) => {
      const pythonCheck = spawn('python3', ['--version'], { stdio: 'pipe' });
      
      pythonCheck.on('close', (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error('Python3 is not available'));
        }
      });

      pythonCheck.on('error', (error) => {
        reject(new Error(`Python3 check failed: ${error.message}`));
      });
    });
  }
}

module.exports = TradingIntegration;
