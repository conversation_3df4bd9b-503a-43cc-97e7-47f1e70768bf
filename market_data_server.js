const express = require('express');
const MarketDataService = require('./market_data_service');

/**
 * Standalone Market Data Service Server
 * For running as a separate microservice in Kubernetes
 */

const app = express();
const port = process.env.PORT || 3001;

// Initialize market data service
const marketDataService = new MarketDataService({
  cacheTimeout: parseInt(process.env.CACHE_TIMEOUT) || 60000,
  retryAttempts: parseInt(process.env.RETRY_ATTEMPTS) || 3,
  retryDelay: parseInt(process.env.RETRY_DELAY) || 1000
});

// Middleware
app.use(express.json());

// CORS for cross-service communication
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  next();
});

// Health check endpoint for Kubernetes
app.get('/health', async (req, res) => {
  try {
    const healthCheck = await marketDataService.healthCheck();
    res.status(healthCheck.status === 'healthy' ? 200 : 503).json(healthCheck);
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      service: 'MarketDataService',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Readiness probe for Kubernetes
app.get('/ready', async (req, res) => {
  try {
    // Test with a simple price fetch
    await marketDataService.getPrice('BTC');
    res.json({
      status: 'ready',
      service: 'MarketDataService',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(503).json({
      status: 'not_ready',
      service: 'MarketDataService',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Get single price
app.get('/api/price/:symbol', async (req, res) => {
  try {
    const { symbol } = req.params;
    const price = await marketDataService.getPrice(symbol);
    res.json({
      symbol: symbol.toUpperCase(),
      price: price,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      error: error.message,
      symbol: req.params.symbol,
      timestamp: new Date().toISOString()
    });
  }
});

// Get multiple prices
app.post('/api/prices', async (req, res) => {
  try {
    const { symbols } = req.body;
    if (!Array.isArray(symbols)) {
      return res.status(400).json({
        error: 'symbols must be an array',
        timestamp: new Date().toISOString()
      });
    }
    
    const prices = await marketDataService.getPrices(symbols);
    res.json({
      prices: prices,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Cache statistics
app.get('/api/cache/stats', (req, res) => {
  try {
    const stats = marketDataService.getCacheStats();
    res.json({
      cache: stats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Clear cache
app.post('/api/cache/clear', (req, res) => {
  try {
    marketDataService.clearCache();
    res.json({
      message: 'Cache cleared successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('🛑 SIGTERM received, shutting down gracefully...');
  await marketDataService.shutdown();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('🛑 SIGINT received, shutting down gracefully...');
  await marketDataService.shutdown();
  process.exit(0);
});

// Start server
app.listen(port, () => {
  console.log(`📊 Market Data Service running on port ${port}`);
  console.log(`🔍 Health check: http://localhost:${port}/health`);
  console.log(`🚀 Ready check: http://localhost:${port}/ready`);
});

module.exports = app;
