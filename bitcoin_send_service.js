const { Coinbase, Wallet } = require("@coinbase/coinbase-sdk");

/**
 * Bitcoin Send Service - Handles all Bitcoin transfer operations
 * Designed to be scalable for Kubernetes deployment
 */
class BitcoinSendService {
  constructor() {
    this.sdkConfigured = false;
    this.initializeSDK();
  }

  /**
   * Initialize Coinbase SDK with proper configuration
   */
  initializeSDK() {
    try {
      console.log('🔄 Initializing Bitcoin Send Service...');
      
      // Check environment variables
      if (process.env.CDP_API_KEY_ID && process.env.CDP_API_KEY_SECRET) {
        console.log('📝 Configuring Coinbase SDK for send service...');

        // Clean up the credentials (remove quotes and extra whitespace)
        const apiKeyName = process.env.CDP_API_KEY_ID.replace(/"/g, '').trim();
        const privateKey = process.env.CDP_API_KEY_SECRET.replace(/"/g, '').trim();

        console.log(`   Send Service API Key ID: ${apiKeyName.substring(0, 8)}...`);
        console.log(`   Send Service Private Key length: ${privateKey.length} characters`);

        Coinbase.configure({
          apiKeyName: apiKeyName,
          privateKey: privateKey,
          useServerSigner: false  // Set to false since project doesn't have registered server signer
        });
        
        this.sdkConfigured = true;
        console.log('✅ Bitcoin Send Service SDK configured successfully');
      } else {
        console.error('❌ Missing CDP API credentials for send service');
      }
    } catch (error) {
      console.error('❌ Failed to initialize Bitcoin Send Service:', error);
    }
  }

  /**
   * Send Bitcoin (ETH on Base Sepolia) from one wallet to another
   */
  async sendBitcoin(fromWalletId, amount, options = {}) {
    try {
      if (!this.sdkConfigured) {
        throw new Error('Send service not properly configured');
      }

      console.log(`🔄 Send Service: Transferring ${amount} BTC from wallet ${fromWalletId}...`);

      // Fetch the source wallet
      const sourceWallet = await Wallet.fetch(fromWalletId);
      console.log(`📝 Source wallet fetched: ${sourceWallet.getId()}`);
      console.log(`📝 Source wallet network: ${sourceWallet.getNetworkId()}`);

      // Ensure we're on Base Sepolia testnet
      if (sourceWallet.getNetworkId() !== Coinbase.networks.BaseSepolia) {
        throw new Error('Send service only supports Base Sepolia testnet for testing');
      }

      // Create destination wallet or use provided address
      let destinationWallet;
      let destinationAddress;

      if (options.destinationWalletId) {
        // Use existing wallet
        destinationWallet = await Wallet.fetch(options.destinationWalletId);
        destinationAddress = await destinationWallet.getDefaultAddress();
      } else {
        // Create new destination wallet (as per Coinbase example)
        console.log('🔄 Creating destination wallet...');
        destinationWallet = await Wallet.create({
          networkId: Coinbase.networks.BaseSepolia
        });
        destinationAddress = await destinationWallet.getDefaultAddress();
        console.log(`📝 Destination wallet created: ${destinationWallet.getId()}`);
      }

      // Import wallet seed to load private key (required for transfers)
      if (process.env.WALLET_SECRET) {
        try {
          console.log('🔐 Importing wallet seed to load private key...');
          await sourceWallet.import(process.env.WALLET_SECRET);
          console.log('✅ Wallet seed imported successfully');
        } catch (importError) {
          console.log('⚠️ Wallet seed import failed, trying without:', importError.message);
        }
      } else {
        console.log('⚠️ No WALLET_SECRET found in environment variables');
      }

      // Create the transfer (ETH on Base network, but we call it BTC for UI)
      console.log(`📝 Creating transfer: ${amount} BTC (ETH) to ${destinationAddress.getId()}`);

      try {
        let transfer = await sourceWallet.createTransfer({
          amount: amount,
          assetId: Coinbase.assets.Eth, // Base network uses ETH
          destination: destinationWallet
        });

        console.log('🔄 Waiting for transfer to complete...');
        transfer = await transfer.wait();

        const transactionHash = transfer.getTransactionHash();
        console.log(`✅ Transfer completed: ${transactionHash}`);

        return {
          success: true,
          transactionHash: transactionHash,
          amount: amount,
          fromWallet: fromWalletId,
          toWallet: destinationWallet.getId(),
          toAddress: destinationAddress.getId(),
          network: 'Base Sepolia testnet',
          asset: 'BTC (ETH on Base)',
          timestamp: new Date().toISOString()
        };

      } catch (transferError) {
        if (transferError.message.includes('private key')) {
          console.error('❌ Private key not loaded. Make sure WALLET_SECRET is set correctly.');
          return {
            success: false,
            error: 'Private key not loaded. Check WALLET_SECRET configuration.',
            details: transferError.message,
            timestamp: new Date().toISOString()
          };
        } else {
          throw transferError; // Re-throw other errors
        }
      }



    } catch (error) {
      console.error('❌ Send Service Error:', error);
      return {
        success: false,
        error: error.message,
        details: error.stack,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Get service health status
   */
  getHealthStatus() {
    return {
      service: 'BitcoinSendService',
      status: this.sdkConfigured ? 'healthy' : 'unhealthy',
      sdkConfigured: this.sdkConfigured,
      timestamp: new Date().toISOString()
    };
  }
}

module.exports = BitcoinSendService;
