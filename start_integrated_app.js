#!/usr/bin/env node
/**
 * Startup script for the integrated MultiLogin + CryptoTraderAI application
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting Integrated MultiLogin + CryptoTraderAI Application');
console.log('=' .repeat(60));

// Check if required files exist
const requiredFiles = [
    'server.js',
    'crypto_bridge.py',
    path.join('..', 'CryptoTraderAI-1', 'config.py')
];

console.log('📋 Checking required files...');
for (const file of requiredFiles) {
    try {
        require('fs').accessSync(file);
        console.log(`✅ ${file} - Found`);
    } catch (error) {
        console.log(`❌ ${file} - Missing`);
        console.error(`Error: Required file ${file} not found`);
        process.exit(1);
    }
}

console.log('\n🔧 Environment Check...');

// Check Node.js version
const nodeVersion = process.version;
console.log(`Node.js version: ${nodeVersion}`);

// Check if Python3 is available
const pythonCheck = spawn('python3', ['--version'], { stdio: 'pipe' });
pythonCheck.on('close', (code) => {
    if (code === 0) {
        console.log('✅ Python3 - Available');
    } else {
        console.log('❌ Python3 - Not available');
        console.error('Error: Python3 is required for CryptoTraderAI');
        process.exit(1);
    }
});

pythonCheck.stdout.on('data', (data) => {
    console.log(`Python version: ${data.toString().trim()}`);
});

// Start the main application
setTimeout(() => {
    console.log('\n🌟 Starting MultiLogin Server...');
    console.log('📊 Trading platform will be available after login');
    console.log('🔗 Access the application at: http://localhost:3000');
    console.log('🔧 Trading service will run on port 8080 (avoiding port 5000 conflicts)');
    console.log('\n💡 User Flow:');
    console.log('   1. Login with any method (email/password, OAuth, biometric)');
    console.log('   2. Get automatically redirected to trading platform');
    console.log('   3. Trading service starts automatically');
    console.log('   4. Begin trading with CryptoTraderAI');
    console.log('   5. Logout to return to login page');
    console.log('\n' + '=' .repeat(60));
    
    // Start the Node.js server
    const serverProcess = spawn('node', ['server.js'], {
        stdio: 'inherit',
        cwd: __dirname
    });

    serverProcess.on('close', (code) => {
        console.log(`\n🛑 Application stopped with code ${code}`);
    });

    // Handle graceful shutdown
    process.on('SIGINT', () => {
        console.log('\n🛑 Shutting down gracefully...');
        serverProcess.kill('SIGTERM');
        process.exit(0);
    });

}, 2000);
