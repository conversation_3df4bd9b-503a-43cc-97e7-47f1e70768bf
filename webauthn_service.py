"""
WebAuthn/Passkey Service for Biometric Authentication
Supports face recognition, fingerprint, and hardware security keys
"""
import json
import secrets
import base64
from typing import Dict, Any, Optional, List
from datetime import datetime

from webauthn import generate_registration_options, verify_registration_response
from webauthn import generate_authentication_options, verify_authentication_response
from webauthn.helpers.structs import (
    AuthenticatorSelectionCriteria,
    UserVerificationRequirement,
    AttestationConveyancePreference,
    AuthenticatorAttachment,
    ResidentKeyRequirement,
    PublicKeyCredentialDescriptor,
    AuthenticationCredential,
    RegistrationCredential
)
from webauthn.helpers.cose import COSEAlgorithmIdentifier

from auth_models import User, WebAuthnCredential, auth_db
from config import Config

class WebAuthnService:
    """WebAuthn service for biometric authentication"""
    
    def __init__(self):
        import os
        self.rp_id = os.environ.get('REPLIT_DEV_DOMAIN', 'localhost')
        self.rp_name = "EZ Money Trading"
        self.origin = f"https://{self.rp_id}"
        
        # For development, use localhost
        if 'localhost' in self.rp_id or '127.0.0.1' in self.rp_id:
            self.origin = f"http://{self.rp_id}:5000"
    
    def start_registration(self, user_id: str, authenticator_type: str = "platform") -> Dict[str, Any]:
        """
        Start WebAuthn registration process
        authenticator_type: 'platform' for built-in (Touch ID, Face ID), 'cross-platform' for external keys
        """
        try:
            user = auth_db.get_user_by_id(user_id)
            if not user:
                return {"error": "User not found", "code": 404}
            
            # Get existing credentials to exclude
            with auth_db.get_session() as session:
                existing_creds = session.query(WebAuthnCredential).filter(
                    WebAuthnCredential.user_id == user_id
                ).all()
                
                exclude_credentials = [
                    PublicKeyCredentialDescriptor(id=cred.credential_id.encode())
                    for cred in existing_creds
                ]
            
            # Configure authenticator selection
            authenticator_selection = AuthenticatorSelectionCriteria(
                authenticator_attachment=AuthenticatorAttachment.PLATFORM if authenticator_type == "platform" else AuthenticatorAttachment.CROSS_PLATFORM,
                resident_key=ResidentKeyRequirement.PREFERRED,
                user_verification=UserVerificationRequirement.PREFERRED
            )
            
            # Generate registration options
            options = generate_registration_options(
                rp_id=self.rp_id,
                rp_name=self.rp_name,
                user_id=user.id.encode(),
                user_name=user.email,
                user_display_name=f"{user.first_name} {user.last_name}".strip() or user.email,
                exclude_credentials=exclude_credentials,
                authenticator_selection=authenticator_selection,
                attestation=AttestationConveyancePreference.NONE,
                supported_pub_key_algs=[
                    COSEAlgorithmIdentifier.ECDSA_SHA_256,
                    COSEAlgorithmIdentifier.RSASSA_PKCS1_v1_5_SHA_256,
                ]
            )
            
            # Store challenge for verification
            challenge_key = f"webauthn_challenge_{user_id}"
            # In production, store this in Redis or secure session storage
            # For now, we'll store it temporarily in user model
            
            return {
                "success": True,
                "options": {
                    "publicKey": {
                        "challenge": base64.b64encode(options.challenge).decode('utf-8'),
                        "rp": {"id": options.rp.id, "name": options.rp.name},
                        "user": {
                            "id": base64.b64encode(options.user.id).decode('utf-8'),
                            "name": options.user.name,
                            "displayName": options.user.display_name
                        },
                        "pubKeyCredParams": [{"alg": alg.value, "type": "public-key"} for alg in options.pub_key_cred_params],
                        "excludeCredentials": [
                            {"id": base64.b64encode(cred.id).decode('utf-8'), "type": cred.type}
                            for cred in options.exclude_credentials
                        ],
                        "authenticatorSelection": {
                            "authenticatorAttachment": options.authenticator_selection.authenticator_attachment.value,
                            "residentKey": options.authenticator_selection.resident_key.value,
                            "userVerification": options.authenticator_selection.user_verification.value
                        },
                        "attestation": options.attestation.value,
                        "timeout": 60000
                    }
                },
                "challenge": base64.b64encode(options.challenge).decode('utf-8')
            }
            
        except Exception as e:
            return {"error": f"Registration start failed: {str(e)}", "code": 500}
    
    def complete_registration(self, user_id: str, credential_data: Dict[str, Any], 
                            challenge: str, credential_name: str = "Biometric Key") -> Dict[str, Any]:
        """Complete WebAuthn registration"""
        try:
            user = auth_db.get_user_by_id(user_id)
            if not user:
                return {"error": "User not found", "code": 404}
            
            # Create RegistrationCredential object
            credential = RegistrationCredential(
                id=credential_data["id"],
                raw_id=credential_data["rawId"].encode('latin-1'),
                response={
                    "client_data_json": credential_data["response"]["clientDataJSON"].encode('latin-1'),
                    "attestation_object": credential_data["response"]["attestationObject"].encode('latin-1')
                },
                type=credential_data["type"]
            )
            
            # Verify the registration
            verification = verify_registration_response(
                credential=credential,
                expected_challenge=challenge.encode('latin-1'),
                expected_origin=self.origin,
                expected_rp_id=self.rp_id
            )
            
            if not verification.verified:
                return {"error": "Registration verification failed", "code": 400}
            
            # Save credential to database
            with auth_db.get_session() as session:
                webauthn_cred = WebAuthnCredential(
                    user_id=user_id,
                    credential_id=verification.credential_id.decode('latin-1'),
                    public_key=verification.credential_public_key.hex(),
                    name=credential_name,
                    authenticator_type="platform" if "platform" in credential_name.lower() else "cross-platform",
                    sign_count=verification.sign_count
                )
                session.add(webauthn_cred)
                
                # Enable WebAuthn for user
                user_obj = session.query(User).filter(User.id == user_id).first()
                user_obj.webauthn_enabled = True
                
                session.commit()
            
            return {
                "success": True,
                "message": "Biometric authentication registered successfully",
                "credential_id": verification.credential_id.decode('latin-1')
            }
            
        except Exception as e:
            return {"error": f"Registration completion failed: {str(e)}", "code": 500}
    
    def start_authentication(self, user_id: Optional[str] = None) -> Dict[str, Any]:
        """Start WebAuthn authentication"""
        try:
            allow_credentials = []
            
            if user_id:
                # User-specific authentication
                with auth_db.get_session() as session:
                    credentials = session.query(WebAuthnCredential).filter(
                        WebAuthnCredential.user_id == user_id
                    ).all()
                    
                    allow_credentials = [
                        PublicKeyCredentialDescriptor(id=cred.credential_id.encode('latin-1'))
                        for cred in credentials
                    ]
            
            # Generate authentication options
            options = generate_authentication_options(
                rp_id=self.rp_id,
                allow_credentials=allow_credentials,
                user_verification=UserVerificationRequirement.PREFERRED
            )
            
            return {
                "success": True,
                "options": {
                    "publicKey": {
                        "challenge": base64.b64encode(options.challenge).decode('utf-8'),
                        "rpId": options.rp_id,
                        "allowCredentials": [
                            {"id": base64.b64encode(cred.id).decode('utf-8'), "type": cred.type}
                            for cred in options.allow_credentials
                        ],
                        "userVerification": options.user_verification.value,
                        "timeout": 60000
                    }
                },
                "challenge": base64.b64encode(options.challenge).decode('utf-8')
            }
            
        except Exception as e:
            return {"error": f"Authentication start failed: {str(e)}", "code": 500}
    
    def complete_authentication(self, credential_data: Dict[str, Any], challenge: str) -> Dict[str, Any]:
        """Complete WebAuthn authentication"""
        try:
            # Find credential in database
            credential_id = credential_data["id"]
            
            with auth_db.get_session() as session:
                webauthn_cred = session.query(WebAuthnCredential).filter(
                    WebAuthnCredential.credential_id == credential_id
                ).first()
                
                if not webauthn_cred:
                    return {"error": "Credential not found", "code": 404}
                
                user = session.query(User).filter(User.id == webauthn_cred.user_id).first()
                if not user or not user.is_active:
                    return {"error": "User not found or inactive", "code": 404}
                
                # Create AuthenticationCredential object
                credential = AuthenticationCredential(
                    id=credential_data["id"],
                    raw_id=credential_data["rawId"].encode('latin-1'),
                    response={
                        "client_data_json": credential_data["response"]["clientDataJSON"].encode('latin-1'),
                        "authenticator_data": credential_data["response"]["authenticatorData"].encode('latin-1'),
                        "signature": credential_data["response"]["signature"].encode('latin-1'),
                        "user_handle": credential_data["response"].get("userHandle", "").encode('latin-1') if credential_data["response"].get("userHandle") else None
                    },
                    type=credential_data["type"]
                )
                
                # Verify authentication
                verification = verify_authentication_response(
                    credential=credential,
                    expected_challenge=challenge.encode('latin-1'),
                    expected_origin=self.origin,
                    expected_rp_id=self.rp_id,
                    credential_public_key=bytes.fromhex(webauthn_cred.public_key),
                    credential_current_sign_count=webauthn_cred.sign_count
                )
                
                if not verification.verified:
                    return {"error": "Authentication verification failed", "code": 401}
                
                # Update credential sign count and last used
                webauthn_cred.sign_count = verification.new_sign_count
                webauthn_cred.last_used = datetime.utcnow()
                
                # Update user last login
                user.last_login = datetime.utcnow()
                
                session.commit()
                
                return {
                    "success": True,
                    "user": user.to_dict(),
                    "message": "Biometric authentication successful"
                }
                
        except Exception as e:
            return {"error": f"Authentication failed: {str(e)}", "code": 500}
    
    def get_user_credentials(self, user_id: str) -> List[Dict[str, Any]]:
        """Get user's WebAuthn credentials"""
        try:
            with auth_db.get_session() as session:
                credentials = session.query(WebAuthnCredential).filter(
                    WebAuthnCredential.user_id == user_id
                ).all()
                
                return [
                    {
                        "id": cred.id,
                        "name": cred.name,
                        "authenticator_type": cred.authenticator_type,
                        "last_used": cred.last_used.isoformat() if cred.last_used else None,
                        "created_at": cred.created_at.isoformat()
                    }
                    for cred in credentials
                ]
                
        except Exception as e:
            return []
    
    def remove_credential(self, user_id: str, credential_id: str) -> Dict[str, Any]:
        """Remove a WebAuthn credential"""
        try:
            with auth_db.get_session() as session:
                credential = session.query(WebAuthnCredential).filter(
                    WebAuthnCredential.user_id == user_id,
                    WebAuthnCredential.id == credential_id
                ).first()
                
                if not credential:
                    return {"error": "Credential not found", "code": 404}
                
                session.delete(credential)
                
                # Check if this was the last credential
                remaining_creds = session.query(WebAuthnCredential).filter(
                    WebAuthnCredential.user_id == user_id
                ).count()
                
                if remaining_creds == 0:
                    # Disable WebAuthn for user
                    user = session.query(User).filter(User.id == user_id).first()
                    if user:
                        user.webauthn_enabled = False
                
                session.commit()
                
                return {"success": True, "message": "Credential removed successfully"}
                
        except Exception as e:
            return {"error": f"Failed to remove credential: {str(e)}", "code": 500}

# Global WebAuthn service instance
webauthn_service = WebAuthnService()