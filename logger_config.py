"""
Logging configuration for the trading agent
"""

import logging
import logging.handlers
import os
from datetime import datetime

def setup_logger(name: str = "trading_agent", log_file: str = None, log_level: str = "INFO") -> logging.Logger:
    """Setup and configure logger for the trading agent"""
    
    # Get log configuration from environment
    if log_file is None:
        log_file = os.getenv("LOG_FILE", "trading_agent.log")
    
    if log_level is None:
        log_level = os.getenv("LOG_LEVEL", "INFO")
    
    log_max_size = int(os.getenv("LOG_MAX_SIZE", "10485760"))  # 10MB
    log_backup_count = int(os.getenv("LOG_BACKUP_COUNT", "5"))
    
    # Create logger
    logger = logging.getLogger(name)
    
    # Avoid duplicate handlers
    if logger.hasHandlers():
        return logger
    
    # Set log level
    numeric_level = getattr(logging, log_level.upper(), logging.INFO)
    logger.setLevel(numeric_level)
    
    # Create formatters
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    simple_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S'
    )
    
    # File handler with rotation
    try:
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=log_max_size,
            backupCount=log_backup_count
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(detailed_formatter)
        logger.addHandler(file_handler)
    except Exception as e:
        print(f"Warning: Could not create file handler: {e}")
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(simple_formatter)
    logger.addHandler(console_handler)
    
    # Add a filter to prevent excessive logging
    class RateLimitFilter(logging.Filter):
        def __init__(self):
            super().__init__()
            self.last_log_time = {}
            self.rate_limit_seconds = 10
        
        def filter(self, record):
            # Allow all ERROR and above messages
            if record.levelno >= logging.ERROR:
                return True
            
            # Rate limit other messages
            current_time = datetime.now().timestamp()
            key = f"{record.filename}:{record.lineno}:{record.msg}"
            
            if key in self.last_log_time:
                if current_time - self.last_log_time[key] < self.rate_limit_seconds:
                    return False
            
            self.last_log_time[key] = current_time
            return True
    
    # Add rate limiting filter to file handler only
    if logger.handlers:
        for handler in logger.handlers:
            if isinstance(handler, logging.handlers.RotatingFileHandler):
                handler.addFilter(RateLimitFilter())
    
    logger.info(f"Logger '{name}' initialized with level {log_level}")
    return logger

def log_trade_decision(logger: logging.Logger, action: str, amount: float, price: float, 
                      reason: str, confidence: float):
    """Log trading decisions with structured format"""
    logger.info(
        f"TRADE_DECISION | Action: {action.upper()} | Amount: ${amount:.2f} | "
        f"Price: ${price:.2f} | Confidence: {confidence:.2f} | Reason: {reason}"
    )

def log_market_data(logger: logging.Logger, trading_pair: str, price: float, 
                   volume: float, source: str):
    """Log market data updates"""
    logger.debug(
        f"MARKET_DATA | Pair: {trading_pair} | Price: ${price:.2f} | "
        f"Volume: {volume:.2f} | Source: {source}"
    )

def log_wallet_balance(logger: logging.Logger, balance: dict):
    """Log wallet balance updates"""
    balance_str = " | ".join([f"{k}: {v:.8f}" for k, v in balance.items()])
    logger.info(f"WALLET_BALANCE | {balance_str}")

def log_error_with_context(logger: logging.Logger, error: Exception, context: str):
    """Log errors with additional context"""
    logger.error(f"ERROR_CONTEXT | {context} | Error: {str(error)} | Type: {type(error).__name__}")

def log_performance_metric(logger: logging.Logger, metric_name: str, value: float, unit: str = ""):
    """Log performance metrics"""
    logger.info(f"PERFORMANCE | {metric_name}: {value:.4f} {unit}")

# Custom logging context manager
class LoggingContext:
    """Context manager for adding context to log messages"""
    
    def __init__(self, logger: logging.Logger, context: str):
        self.logger = logger
        self.context = context
        self.old_factory = None
    
    def __enter__(self):
        self.old_factory = logging.getLogRecordFactory()
        
        def record_factory(*args, **kwargs):
            record = self.old_factory(*args, **kwargs)
            record.context = self.context
            return record
        
        logging.setLogRecordFactory(record_factory)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        logging.setLogRecordFactory(self.old_factory)

# Usage example:
# with LoggingContext(logger, "MarketAnalysis"):
#     logger.info("Analyzing market trends")
