"""
Autonomous AI Trading Agent for Bitcoin accumulation
"""
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import logging

from config import Config
from wallet_manager import WalletManager
from market_data import MarketDataProvider
from trading_strategy import TradingStrategy
from trading_agent import TradingAgent
from logger_config import setup_logger
from llm_providers import get_llm_manager


@dataclass
class AgentPersonality:
    """Agent personality and characteristics"""
    name: str = "BitHunter"
    mode: str = "autonomous"  # "autonomous" or "interactive"
    avatar_path: Optional[str] = None
    risk_tolerance: float = 0.7  # 0.0 to 1.0
    aggression_level: float = 0.8  # How aggressive in accumulating BTC
    sensitivity: float = 0.5  # Trading sensitivity (0.1-2.0) - lower = more trades
    learning_rate: float = 0.1  # How quickly it adapts strategies
    confidence_threshold: float = 0.4  # Minimum confidence for trades
    llm_provider: Optional[str] = None  # Active LLM provider
    llm_enabled: bool = False  # Whether to use LLM analysis


class AutonomousAgent:
    """Advanced AI Trading Agent for Bitcoin accumulation"""
    
    def __init__(self, config: Config, wallet_manager: WalletManager, 
                 market_data: MarketDataProvider, strategy: TradingStrategy):
        self.config = config
        self.wallet_manager = wallet_manager
        self.market_data = market_data
        self.strategy = strategy
        self.trading_agent = TradingAgent(config)
        
        # Initialize logger and LLM manager
        self.logger = setup_logger("AutonomousAgent")
        self.llm_manager = get_llm_manager()
        
        # Agent state
        self.is_running = False
        self.agent_thread = None
        self.personality = AgentPersonality()
        
        # Session tracking
        self.session_start_time = None
        self.session_start_balance = 0.0
        
        # Performance metrics
        self.total_trades_executed = 0
        self.successful_trades = 0
        self.btc_accumulated = 0.0
        self.profit_loss = 0.0
        
        # Advanced trading features
        self.market_analysis_interval = 5  # seconds
        self.llm_analysis_interval = 300  # 5 minutes for LLM decisions
        self.last_llm_analysis = 0
        self.price_alerts = []
        self.trading_opportunities = []
        self.last_analysis_time = None
        
        # Interactive mode features
        self.pending_trades = []  # Store trades awaiting approval
        
        # AI decision-making parameters
        self.decision_history = []
        self.market_sentiment = "neutral"
        
        # Testing mode
        self.testing_mode = False
        self.testing_start_time = None
        self.testing_duration = 600  # 10 minutes in seconds
        self.last_test_trade = 0
    
    def set_personality(self, name: str, mode: str = "autonomous", avatar_path: Optional[str] = None, 
                       risk_tolerance: float = 0.7, aggression_level: float = 0.8, 
                       sensitivity: float = 0.5, llm_provider: Optional[str] = None, llm_enabled: bool = False):
        """Configure agent personality"""
        self.personality = AgentPersonality(
            name=name,
            mode=mode,
            avatar_path=avatar_path,
            risk_tolerance=risk_tolerance,
            aggression_level=aggression_level,
            sensitivity=sensitivity,
            llm_provider=llm_provider,
            llm_enabled=llm_enabled
        )
        
        # Configure LLM if enabled
        if llm_enabled and llm_provider:
            success = self.llm_manager.set_active_provider(llm_provider)
            if not success:
                self.logger.warning(f"Failed to set LLM provider {llm_provider}, continuing without LLM")
                self.personality.llm_enabled = False
        
        self.logger.info(f"Agent personality configured: {name} ({mode} mode, LLM: {llm_enabled})")
    
    def start_agent(self):
        """Start the autonomous trading agent"""
        if self.is_running:
            self.logger.warning("Agent is already running")
            return False
        
        try:
            self.is_running = True
            self.session_start_time = datetime.now()
            
            # Get initial balance
            initial_balance = self.wallet_manager.get_balance()
            if initial_balance:
                self.session_start_balance = initial_balance.get('BTC', 0.0)
            
            # Start agent loop in separate thread
            self.agent_thread = threading.Thread(target=self._agent_loop, daemon=True)
            self.agent_thread.start()
            
            self.logger.info(f"Autonomous agent '{self.personality.name}' started in {self.personality.mode} mode")
            return True
            
        except Exception as e:
            self.logger.error(f"Error starting agent: {e}")
            self.is_running = False
            return False
    
    def stop_agent(self):
        """Stop the autonomous trading agent"""
        if not self.is_running:
            self.logger.warning("Agent is not running")
            return False
        
        try:
            self.is_running = False
            if self.agent_thread and self.agent_thread.is_alive():
                self.agent_thread.join(timeout=5)
            
            self.logger.info(f"Autonomous agent '{self.personality.name}' stopped")
            return True
            
        except Exception as e:
            self.logger.error(f"Error stopping agent: {e}")
            return False
    
    def _agent_loop(self):
        """Main agent loop for autonomous operation"""
        self.logger.info("Agent loop started")
        
        while self.is_running:
            try:
                current_time = time.time()
                
                # Perform market analysis
                self._perform_market_analysis()
                
                # Check if it's time for LLM analysis (every 5 minutes)
                if current_time - self.last_llm_analysis >= self.llm_analysis_interval:
                    self._perform_llm_analysis()
                    self.last_llm_analysis = current_time
                
                # Make trading decisions
                self._make_trading_decisions()
                
                # Update performance metrics
                self._update_performance_metrics()
                
                # Adaptive learning
                self._adaptive_learning()
                
                # Sleep based on analysis interval
                time.sleep(self.market_analysis_interval)
                
            except Exception as e:
                self.logger.error(f"Error in agent loop: {e}")
                time.sleep(30)  # Wait before retrying
    
    def _perform_llm_analysis(self):
        """Perform LLM-enhanced market analysis every 5 minutes"""
        try:
            if not self.personality.llm_enabled:
                return
            
            # Get current market data and balance
            current_data = self.market_data.get_current_data(self.config.TRADING_PAIR)
            balance = self.wallet_manager.get_balance()
            
            if not current_data or not balance:
                return
            
            # Get basic strategy signal for LLM context
            signal = self.strategy.analyze_market(current_data)
            if not signal:
                signal = {'action': 'hold', 'confidence': 0.5, 'reasoning': 'No clear signal'}
            
            # Get LLM analysis
            llm_analysis = self.llm_manager.analyze_market(current_data, signal)
            
            if llm_analysis:
                # Log the LLM decision
                decision = {
                    'timestamp': datetime.now(),
                    'type': 'llm_analysis',
                    'action': llm_analysis.get('recommendation', 'hold'),
                    'confidence': llm_analysis.get('confidence', 0.5),
                    'reasoning': llm_analysis.get('reasoning', 'LLM market analysis'),
                    'price': current_data.get('price', 0),
                    'market_sentiment': llm_analysis.get('market_sentiment', 'neutral'),
                    'risk_assessment': llm_analysis.get('risk_assessment', 'medium')
                }
                
                # Add to decision history
                self.decision_history.append(decision)
                
                # Keep only last 50 decisions
                if len(self.decision_history) > 50:
                    self.decision_history = self.decision_history[-50:]
                
                self.logger.info(f"LLM Analysis Complete - Recommendation: {decision['action']} "
                               f"(Confidence: {decision['confidence']:.2f}, "
                               f"Sentiment: {decision['market_sentiment']})")
                
                # Update market sentiment
                self.market_sentiment = decision['market_sentiment']
                
        except Exception as e:
            self.logger.error(f"Error in LLM analysis: {e}")
    
    def _perform_market_analysis(self):
        """Advanced market analysis for trading opportunities"""
        try:
            self.last_analysis_time = datetime.now()
            
            # Get current market data
            current_data = self.market_data.get_current_data(self.config.TRADING_PAIR)
            if not current_data:
                return
            
            # Analyze market sentiment
            price_change = current_data.get('price_change_24h', 0)
            if price_change > 5:
                self.market_sentiment = "bullish"
            elif price_change < -5:
                self.market_sentiment = "bearish"
            else:
                self.market_sentiment = "neutral"
            
            # Look for trading opportunities
            self.trading_opportunities.clear()
            
            # Price momentum analysis
            if abs(price_change) > 3:
                self.trading_opportunities.append({
                    'type': 'momentum',
                    'signal': 'buy' if price_change > 0 else 'sell',
                    'strength': min(abs(price_change) / 10, 1.0),
                    'reasoning': f"Strong {'upward' if price_change > 0 else 'downward'} momentum detected"
                })
            
        except Exception as e:
            self.logger.error(f"Error in market analysis: {e}")
    
    def _make_trading_decisions(self):
        """AI-driven trading decision making"""
        try:
            # Check if testing mode is active
            if self.testing_mode:
                return self._make_testing_decisions()
            
            # Get current market data and balance
            current_data = self.market_data.get_current_data(self.config.TRADING_PAIR)
            balance = self.wallet_manager.get_balance()
            
            if not current_data or not balance:
                return
            
            # Get strategy signal with configurable sensitivity
            signal = self.strategy.analyze_market(current_data, self.personality.sensitivity)
            if not signal:
                return
            
            # Enhance signal with AI analysis
            enhanced_signal = self._enhance_signal_with_ai(signal, current_data, balance)
            if not enhanced_signal:
                return
            
            # Check confidence threshold
            confidence = enhanced_signal.get('confidence', 0.0)
            if confidence < self.personality.confidence_threshold:
                return
            
            # Execute trading decision
            self._execute_autonomous_trade(enhanced_signal, balance, current_data)
            
        except Exception as e:
            self.logger.error(f"Error making trading decisions: {e}")
    
    def _make_testing_decisions(self):
        """Make forced buy/sell decisions for testing purposes"""
        try:
            current_time = time.time()
            
            # Check if testing period has expired
            if self.testing_start_time and current_time - self.testing_start_time > self.testing_duration:
                self.disable_testing_mode()
                return
            
            # Force a trade every 10 seconds during testing for immediate results
            if current_time - self.last_test_trade < 10:
                return
            
            # Get current market data and balance
            current_data = self.market_data.get_current_data(self.config.TRADING_PAIR)
            balance = self.wallet_manager.get_balance()
            
            if not current_data or not balance:
                return
            
            # Alternate between buy and sell decisions
            trade_count = int((current_time - (self.testing_start_time or 0)) // 10)
            action = "buy" if trade_count % 2 == 0 else "sell"
            
            # Create forced signal for testing
            current_price = current_data.get('price', 0)
            usd_balance = balance.get('USD', 0)
            btc_balance = balance.get('BTC', 0)
            
            if action == "buy" and usd_balance > 100:
                # Force small buy order ($100)
                test_signal = {
                    'action': 'buy',
                    'confidence': 0.8,
                    'amount_usd': 100,
                    'price': current_price,
                    'reasoning': 'Testing mode - forced buy decision',
                    'test_mode': True
                }
            elif action == "sell" and btc_balance > 0.001:
                # Force small sell order (0.001 BTC)
                test_signal = {
                    'action': 'sell',
                    'confidence': 0.8,
                    'amount_btc': 0.001,
                    'price': current_price,
                    'reasoning': 'Testing mode - forced sell decision',
                    'test_mode': True
                }
            else:
                # Skip if insufficient balance
                self.last_test_trade = current_time
                return
            
            self.logger.info(f"Testing mode: Forcing {action} decision")
            self._execute_autonomous_trade(test_signal, balance, current_data)
            self.last_test_trade = current_time
            
        except Exception as e:
            self.logger.error(f"Error in testing mode decisions: {e}")
    
    def _enhance_signal_with_ai(self, signal: Dict[str, Any], market_data: Dict[str, Any], 
                               balance: Dict[str, float]) -> Optional[Dict[str, Any]]:
        """Enhance trading signal with AI analysis"""
        try:
            enhanced_signal = signal.copy()
            
            # Apply personality-based adjustments
            risk_adjustment = self.personality.risk_tolerance
            aggression_adjustment = self.personality.aggression_level
            
            # Get LLM analysis if enabled
            llm_analysis = None
            if self.personality.llm_enabled:
                try:
                    llm_analysis = self.llm_manager.analyze_market(market_data, signal)
                    self.logger.info(f"LLM analysis received: {llm_analysis.get('recommendation', 'N/A')}")
                except Exception as e:
                    self.logger.warning(f"LLM analysis failed: {e}")
            
            # Adjust confidence based on market sentiment and LLM analysis
            sentiment_multiplier = 1.0
            if self.market_sentiment == "bullish" and signal['action'] == 'buy':
                sentiment_multiplier = 1.2
            elif self.market_sentiment == "bearish" and signal['action'] == 'sell':
                sentiment_multiplier = 1.2
            elif self.market_sentiment != "neutral":
                sentiment_multiplier = 0.8
            
            # Apply LLM confidence adjustment if available
            if llm_analysis and not llm_analysis.get('error'):
                llm_confidence_adj = llm_analysis.get('confidence_adjustment', 1.0)
                sentiment_multiplier *= llm_confidence_adj
                
                # Override recommendation if LLM strongly disagrees
                llm_recommendation = llm_analysis.get('recommendation', '')
                if llm_recommendation in ['strong_sell', 'sell'] and signal['action'] == 'buy':
                    sentiment_multiplier *= 0.5  # Reduce buy confidence
                elif llm_recommendation in ['strong_buy', 'buy'] and signal['action'] == 'sell':
                    sentiment_multiplier *= 0.5  # Reduce sell confidence
                elif llm_recommendation in ['strong_buy'] and signal['action'] == 'buy':
                    sentiment_multiplier *= 1.3  # Boost buy confidence
                elif llm_recommendation in ['strong_sell'] and signal['action'] == 'sell':
                    sentiment_multiplier *= 1.3  # Boost sell confidence
            
            enhanced_signal['confidence'] = min(1.0, signal['confidence'] * sentiment_multiplier * risk_adjustment)
            
            # Calculate position size based on aggression and confidence
            size_multiplier = aggression_adjustment * enhanced_signal['confidence']
            enhanced_signal['size_multiplier'] = max(0.1, min(2.0, size_multiplier))
            
            # Create comprehensive reasoning
            reasoning_parts = [
                f"Technical: {signal.get('reasoning', 'N/A')}",
                f"Sentiment: {self.market_sentiment}",
                f"Risk: {risk_adjustment:.2f}",
                f"Aggression: {aggression_adjustment:.2f}"
            ]
            
            if llm_analysis and not llm_analysis.get('error'):
                reasoning_parts.append(f"LLM: {llm_analysis.get('reasoning', 'N/A')}")
                enhanced_signal['llm_recommendation'] = llm_analysis.get('recommendation')
                enhanced_signal['llm_sentiment'] = llm_analysis.get('sentiment')
                enhanced_signal['llm_risk_factors'] = llm_analysis.get('risk_factors', [])
            
            enhanced_signal['ai_reasoning'] = " | ".join(reasoning_parts)
            
            return enhanced_signal
            
        except Exception as e:
            self.logger.error(f"Error enhancing signal: {str(e)}")
            return signal
    
    def _execute_autonomous_trade(self, signal: Dict[str, Any], balance: Dict[str, float], 
                                 market_data: Dict[str, Any]):
        """Execute trading decision based on mode (autonomous or interactive)"""
        try:
            action = signal['action']
            confidence = signal['confidence']
            size_multiplier = signal.get('size_multiplier', 1.0)
            
            # Calculate trade amount
            if action == 'buy':
                available_usd = balance.get('USD', 0)
                base_trade_amount = available_usd * 0.1  # 10% of available USD
                trade_amount = base_trade_amount * confidence * size_multiplier
                trade_amount = max(10, min(trade_amount, available_usd * 0.5))  # Min $10, max 50% of USD
            else:  # sell
                available_btc = balance.get('BTC', 0)
                trade_amount = available_btc * 0.1 * confidence * size_multiplier  # 10% of BTC holdings
                trade_amount = max(0.0001, min(trade_amount, available_btc * 0.5))  # Min 0.0001 BTC, max 50%
            
            # Create decision record
            decision_record = {
                'id': len(self.decision_history) + 1,
                'timestamp': datetime.utcnow().isoformat(),
                'action': action,
                'confidence': confidence,
                'amount_usd': trade_amount if action == 'buy' else trade_amount * market_data.get('price', 1),
                'amount_btc': trade_amount / market_data.get('price', 1) if action == 'buy' else trade_amount,
                'price': market_data.get('price', 0.0),
                'reasoning': signal.get('ai_reasoning', signal.get('reasoning', '')),
                'executed': False,
                'status': 'pending' if self.personality.mode == 'interactive' else 'executed'
            }
            
            if self.personality.mode == 'interactive':
                # Queue trade for approval in interactive mode
                self.pending_trades.append(decision_record)
                self.logger.info(f"Trade queued for approval: {action} ${decision_record['amount_usd']:.2f} (ID: {decision_record['id']})")
                decision_record['executed'] = False
            else:
                # Execute immediately in autonomous mode
                self.total_trades_executed += 1
                success = self._execute_trade_directly(action, trade_amount, balance, market_data)
                decision_record['executed'] = success
                decision_record['status'] = 'executed' if success else 'failed'
                
                if success:
                    self.successful_trades += 1
            
            self.decision_history.append(decision_record)
            
            # Save decision to database for persistence and web interface
            try:
                from database import get_db_manager
                db_manager = get_db_manager()
                db_manager.save_strategy_signal(
                    strategy_name="autonomous_agent",
                    signal_data={
                        'signal_type': action,
                        'confidence': confidence,
                        'reason': signal.get('ai_reasoning', signal.get('reasoning', '')),
                        'executed': decision_record['executed'],
                        'result': decision_record['status'],
                        'profit_loss': 0  # Will be updated later when we have actual P&L data
                    }
                )
            except Exception as e:
                self.logger.error(f"Error saving decision to database: {e}")
            
            # Keep only recent decisions
            if len(self.decision_history) > 100:
                self.decision_history = self.decision_history[-100:]
                
        except Exception as e:
            self.logger.error(f"Error handling trade decision: {e}")
    
    def _execute_trade_directly(self, action: str, trade_amount: float, balance: Dict[str, float], 
                               market_data: Dict[str, Any]) -> bool:
        """Execute a trade directly (used in autonomous mode)"""
        try:
            if action == 'buy':
                success = self.wallet_manager.buy_crypto('BTC', trade_amount, 'USD')
                if success:
                    btc_bought = trade_amount / market_data['price']
                    self.btc_accumulated += btc_bought
                    self.logger.info(f"Autonomous BUY: ${trade_amount:.2f} worth of BTC")
                return success
                
            elif action == 'sell':
                success = self.wallet_manager.sell_crypto('BTC', trade_amount, 'USD')
                if success:
                    self.logger.info(f"Autonomous SELL: {trade_amount:.8f} BTC")
                return success
                
            return False
            
        except Exception as e:
            self.logger.error(f"Error executing trade directly: {e}")
            return False
    
    def _update_performance_metrics(self):
        """Update agent performance metrics"""
        try:
            current_balance = self.wallet_manager.get_balance()
            if current_balance and self.session_start_time:
                current_btc = current_balance.get('BTC', 0.0)
                self.btc_accumulated = current_btc - self.session_start_balance
                
                # Calculate profit/loss in USD
                current_data = self.market_data.get_current_data(self.config.TRADING_PAIR)
                if current_data:
                    current_portfolio_value = (
                        current_balance.get('USD', 0) + 
                        current_btc * current_data['price']
                    )
                    
                    start_portfolio_value = (
                        current_balance.get('USD', 0) + 
                        self.session_start_balance * current_data['price']
                    )
                    
                    self.profit_loss = current_portfolio_value - start_portfolio_value
                    
        except Exception as e:
            self.logger.error(f"Error updating performance metrics: {str(e)}")
    
    def _adaptive_learning(self):
        """Adaptive learning based on trading performance"""
        try:
            if len(self.decision_history) >= 10:
                recent_decisions = self.decision_history[-10:]
                success_rate = sum(1 for d in recent_decisions if d.get('executed')) / len(recent_decisions)
                
                # Adjust confidence threshold based on success rate
                if success_rate > 0.8:
                    # High success rate - can be more aggressive
                    self.personality.confidence_threshold = max(0.4, self.personality.confidence_threshold - 0.1)
                elif success_rate < 0.6:
                    # Low success rate - be more conservative
                    self.personality.confidence_threshold = min(0.9, self.personality.confidence_threshold + 0.1)
                
                # Adjust market analysis interval based on volatility
                if len(self.trading_opportunities) > 5:
                    # High volatility - analyze more frequently
                    self.market_analysis_interval = max(5, self.market_analysis_interval - 2)
                else:
                    # Low volatility - analyze less frequently
                    self.market_analysis_interval = min(60, self.market_analysis_interval + 2)
                    
        except Exception as e:
            self.logger.error(f"Error in adaptive learning: {str(e)}")
    
    def get_agent_status(self) -> Dict[str, Any]:
        """Get comprehensive agent status"""
        try:
            runtime = None
            if self.session_start_time:
                runtime = str(datetime.now() - self.session_start_time).split('.')[0]
            
            # Get actual trading data from database
            total_trades = 0
            successful_trades = 0
            btc_accumulated = 0.0
            
            try:
                from database import get_db_manager, Transaction
                db_manager = get_db_manager()
                
                with db_manager.get_session() as session:
                    # Count total transactions
                    transactions = session.query(Transaction).filter(
                        Transaction.is_simulated == True  # DRY RUN mode transactions
                    ).all()
                    
                    total_trades = len(transactions)
                    successful_trades = len([t for t in transactions if t.status == 'completed'])
                    btc_accumulated = sum(t.amount for t in transactions if t.type == 'buy')
                    
            except Exception as e:
                self.logger.error(f"Error getting transaction data: {e}")
                # Fallback to internal counters
                total_trades = self.total_trades_executed
                successful_trades = self.successful_trades
                btc_accumulated = self.btc_accumulated
            
            success_rate = 0.0
            if total_trades > 0:
                success_rate = (successful_trades / total_trades) * 100
            
            return {
                'name': self.personality.name,
                'mode': self.personality.mode,
                'avatar_path': self.personality.avatar_path,
                'is_running': self.is_running,
                'runtime': runtime,
                'session_start': self.session_start_time.isoformat() if self.session_start_time else None,
                'total_trades': self.total_trades_executed,
                'successful_trades': self.successful_trades,
                'success_rate': round(success_rate, 2),
                'btc_accumulated': round(self.btc_accumulated, 8),
                'profit_loss_usd': round(self.profit_loss, 2),
                'market_sentiment': self.market_sentiment,
                'confidence_threshold': round(self.personality.confidence_threshold, 2),
                'risk_tolerance': round(self.personality.risk_tolerance, 2),
                'aggression_level': round(self.personality.aggression_level, 2),
                'sensitivity': round(self.personality.sensitivity, 2),
                'trading_opportunities': len(self.trading_opportunities),
                'last_analysis': self.last_analysis_time.isoformat() if self.last_analysis_time else None,
                'analysis_interval': self.market_analysis_interval,
                'llm_enabled': self.personality.llm_enabled,
                'llm_provider': self.personality.llm_provider,
                'llm_status': self.llm_manager.get_status()
            }
            
        except Exception as e:
            self.logger.error(f"Error getting agent status: {str(e)}")
            return {'error': str(e)}
    
    def get_recent_decisions(self, limit: int = 20) -> List[Dict[str, Any]]:
        """Get recent trading decisions"""
        try:
            recent = self.decision_history[-limit:] if self.decision_history else []
            return recent
        except Exception as e:
            self.logger.error(f"Error getting recent decisions: {str(e)}")
            return []
    
    def force_market_analysis(self):
        """Force immediate market analysis"""
        try:
            self._perform_market_analysis()
            return True
        except Exception as e:
            self.logger.error(f"Error in forced market analysis: {str(e)}")
            return False
    
    def enable_testing_mode(self):
        """Enable testing mode for 10 minutes with forced buy/sell decisions"""
        self.testing_mode = True
        self.testing_start_time = time.time()
        self.last_test_trade = time.time()
        self.logger.info("Testing mode enabled - forcing buy/sell decisions for 10 minutes")
        
    def disable_testing_mode(self):
        """Disable testing mode"""
        self.testing_mode = False
        self.testing_start_time = None
        self.logger.info("Testing mode disabled")
    
    def update_personality_settings(self, risk_tolerance: Optional[float] = None, 
                                  aggression_level: Optional[float] = None,
                                  sensitivity: Optional[float] = None):
        """Update personality settings while running"""
        try:
            if risk_tolerance is not None:
                self.personality.risk_tolerance = max(0.0, min(1.0, risk_tolerance))
            
            if aggression_level is not None:
                self.personality.aggression_level = max(0.0, min(1.0, aggression_level))
            
            if sensitivity is not None:
                self.personality.sensitivity = max(0.1, min(2.0, sensitivity))
            
            self.logger.info(f"Updated personality: Risk={self.personality.risk_tolerance}, Aggression={self.personality.aggression_level}, Sensitivity={self.personality.sensitivity}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating personality: {str(e)}")
            return False
    
    def get_pending_trades(self) -> List[Dict[str, Any]]:
        """Get trades awaiting approval"""
        return [trade for trade in self.pending_trades if trade.get('status') == 'pending']
    
    def approve_trade(self, trade_id: int) -> bool:
        """Approve and execute a pending trade"""
        try:
            trade = next((t for t in self.pending_trades if t.get('id') == trade_id), None)
            if not trade or trade.get('status') != 'pending':
                return False
            
            action = trade.get('action')
            amount_usd = trade.get('amount_usd', 0.0)
            
            if action == 'buy':
                success = self.trading_agent.force_trade_signal('buy', amount_usd)
            elif action == 'sell':
                success = self.trading_agent.force_trade_signal('sell', amount_usd)
            else:
                return False
            
            # Update trade status
            trade['executed'] = success
            trade['status'] = 'executed' if success else 'failed'
            trade['executed_at'] = datetime.utcnow().isoformat()
            
            if success:
                self.total_trades_executed += 1
                self.successful_trades += 1
                self.logger.info(f"Approved and executed {action} trade for ${amount_usd:.2f}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Error approving trade: {e}")
            return False
    
    def reject_trade(self, trade_id: int) -> bool:
        """Reject a pending trade"""
        try:
            trade = next((t for t in self.pending_trades if t.get('id') == trade_id), None)
            if not trade or trade.get('status') != 'pending':
                return False
            
            trade['status'] = 'rejected'
            trade['rejected_at'] = datetime.utcnow().isoformat()
            self.logger.info(f"Rejected trade ID {trade_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error rejecting trade: {e}")
            return False


# Global agent instance
_autonomous_agent_instance = None

def get_autonomous_agent(config: Optional[Config] = None, wallet_manager: Optional[WalletManager] = None,
                        market_data: Optional[MarketDataProvider] = None, strategy: Optional[TradingStrategy] = None) -> AutonomousAgent:
    """Get or create the global autonomous agent instance"""
    global _autonomous_agent_instance
    
    if _autonomous_agent_instance is None:
        if not all([config, wallet_manager, market_data, strategy]):
            raise ValueError("All components required for first-time initialization")
        
        _autonomous_agent_instance = AutonomousAgent(config, wallet_manager, market_data, strategy)
    
    return _autonomous_agent_instance