#!/bin/bash

# Script to commit and push Bitcoin wallet functionality to ezmoney-dev branch
# Run this script to push your latest changes to GitLab

echo "=== Pushing to ezmoney-dev branch ==="

# Remove any Git lock files
rm -f .git/index.lock .git/refs/heads/*.lock

# Set up SSH configuration
export GIT_SSH_COMMAND="ssh -i ~/.ssh/id_ed25519 -o StrictHostKeyChecking=no"

# Check current branch
echo "Current branch:"
git branch

# Create and switch to ezmoney-dev branch
echo "Creating ezmoney-dev branch..."
git checkout -b ezmoney-dev 2>/dev/null || git checkout ezmoney-dev

# Add any uncommitted changes
echo "Adding changes..."
git add .

# Check if there are changes to commit
if git diff --cached --quiet; then
    echo "No changes to commit"
else
    echo "Committing changes..."
    git commit -m "Complete Bitcoin wallet functionality with send feature

- Added Bitcoin address display with click-to-copy functionality
- Implemented Bitcoin send feature with USD amount input
- Added address validation for mainnet and testnet Bitcoin addresses
- Fixed balance tracking to properly deduct USD and BTC for send transactions
- Added complete transaction history tracking for all operations
- Implemented database persistence for send transactions
- Enhanced wallet management with comprehensive Bitcoin operations"
fi

# Fetch remote branches
echo "Fetching remote branches..."
git fetch origin

# Check if remote ezmoney-dev exists and set upstream
if git ls-remote --exit-code --heads origin ezmoney-dev >/dev/null 2>&1; then
    echo "Setting upstream to origin/ezmoney-dev..."
    git branch --set-upstream-to=origin/ezmoney-dev ezmoney-dev
    
    echo "Pulling latest changes from ezmoney-dev..."
    git pull origin ezmoney-dev --no-rebase
else
    echo "Remote ezmoney-dev branch doesn't exist, will create it"
fi

# Push to ezmoney-dev branch
echo "Pushing to ezmoney-dev branch..."
git push origin ezmoney-dev

if [ $? -eq 0 ]; then
    echo "✓ Successfully pushed to ezmoney-dev branch!"
    echo "✓ Bitcoin wallet functionality is now available on GitLab"
else
    echo "✗ Push failed. Please check the error messages above."
    exit 1
fi

echo "=== Push complete ==="