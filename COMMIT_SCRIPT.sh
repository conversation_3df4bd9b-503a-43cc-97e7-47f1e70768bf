#!/bin/bash

# In your project directory
git init
export GIT_SSH_COMMAND="ssh -i ./id_ed25519 -o StrictHostKeyChecking=no"
git remote <NAME_EMAIL>:rospoplabs-group/ez-money2.git
git add .
git commit -m "Complete AI cryptocurrency trading agent - all features working"
git push -u origin main
# EZ Money 2 - GitLab Commit Script
# Run this script on your local machine after downloading all project files

echo "Setting up EZ Money 2 repository for GitLab..."

# Configure Git
git config --global user.name "support929"
git config --global user.email "<EMAIL>"

# Initialize repository
git init

# Add GitLab remote
git remote <NAME_EMAIL>:rospoplabs-group/ez-money2.git

# Add all files
git add .

# Commit with comprehensive message
git commit -m "Complete AI cryptocurrency trading agent with web interface

Core Features:
- AI-powered BTC trading with Coinbase AgentKit integration
- Real-time web dashboard with portfolio monitoring
- PostgreSQL database persistence for all transactions
- Custom trade amount handling (fixed - now processes $5000+ trades correctly)
- Balance calculation from transaction history (fixed - shows accurate $50M+ portfolio)
- Input field clearing after successful trades
- Technical analysis with multiple trading strategies
- Comprehensive error handling and logging
- CLI interface for monitoring and control

Recent Critical Fixes:
- Fixed custom amount handling in web interface
- Resolved balance validation using actual transaction history  
- Fixed sell order execution issues
- Improved transaction persistence in database
- Enhanced real-time dashboard updates

Architecture:
- Backend: Python Flask API with SQLAlchemy ORM
- Database: PostgreSQL with full transaction tracking
- Frontend: Responsive HTML dashboard with JavaScript
- Trading: Coinbase AgentKit with custom strategies
- Security: Environment-based configuration management

Status: Production ready with all major issues resolved
Mode: Currently running in DRY RUN mode for safe testing"

# Push to GitLab
echo "Pushing to GitLab repository..."
git push -u origin main

echo "Deployment complete! Repository available at:"
echo "https://gitlab.com/rospoplabs-group/ez-money2"
