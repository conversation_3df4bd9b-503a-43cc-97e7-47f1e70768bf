"""
Google OAuth Authentication Blueprint
Simplified implementation for Google authentication
"""

import json
import os
import requests
from datetime import datetime
from flask import Blueprint, redirect, request, url_for, jsonify
from flask_jwt_extended import create_access_token
from oauthlib.oauth2 import WebApplicationClient
from auth_service import auth_service

GOOGLE_CLIENT_ID = os.environ.get("GOOGLE_OAUTH_CLIENT_ID")
GOOGLE_CLIENT_SECRET = os.environ.get("GOOGLE_OAUTH_CLIENT_SECRET")
GOOGLE_DISCOVERY_URL = "https://accounts.google.com/.well-known/openid-configuration"

# Development redirect URL - ensure we use the correct Replit domain
REPLIT_DOMAIN = os.environ.get("REPLIT_DEV_DOMAIN")
if not REPLIT_DOMAIN:
    raise ValueError("REPLIT_DEV_DOMAIN environment variable not set")
DEV_REDIRECT_URL = f'https://{REPLIT_DOMAIN}/auth/google/callback'

print(f"""
🔐 Google OAuth Setup Instructions:
1. Go to https://console.cloud.google.com/apis/credentials
2. Create a new OAuth 2.0 Client ID
3. Add these URLs to your OAuth configuration:

   Authorized JavaScript origins:
   https://{REPLIT_DOMAIN}
   
   Authorized redirect URIs:
   {DEV_REDIRECT_URL}

Current domain: {REPLIT_DOMAIN}
Current redirect URL: {DEV_REDIRECT_URL}

For detailed setup guide:
https://docs.replit.com/additional-resources/google-auth-in-flask#set-up-your-oauth-app--client
""")

client = WebApplicationClient(GOOGLE_CLIENT_ID) if GOOGLE_CLIENT_ID else None

google_auth = Blueprint("google_auth", __name__)

@google_auth.route("/test")
def test_oauth():
    """Test Google OAuth configuration"""
    return jsonify({
        "client_id_exists": bool(GOOGLE_CLIENT_ID),
        "client_secret_exists": bool(GOOGLE_CLIENT_SECRET),
        "redirect_url": DEV_REDIRECT_URL,
        "domain": os.environ.get("REPLIT_DEV_DOMAIN", "localhost:5000"),
        "client_initialized": bool(client)
    })

def get_google_provider_cfg():
    """Get Google OAuth provider configuration"""
    return requests.get(GOOGLE_DISCOVERY_URL).json()

@google_auth.route("/google")
def login():
    """Initiate Google OAuth login"""
    if not GOOGLE_CLIENT_ID:
        return jsonify({"error": "Google OAuth not configured"}), 500
    
    print(f"Starting Google OAuth with Client ID: {GOOGLE_CLIENT_ID[:10]}...")
    print(f"Using redirect URI: {DEV_REDIRECT_URL}")
    
    try:
        google_provider_cfg = get_google_provider_cfg()
        authorization_endpoint = google_provider_cfg["authorization_endpoint"]
        
        request_uri = client.prepare_request_uri(
            authorization_endpoint,
            redirect_uri=DEV_REDIRECT_URL,
            scope=["openid", "email", "profile"],
        )
        
        print(f"Generated OAuth request URI: {request_uri}")
        return redirect(request_uri)
        
    except Exception as e:
        print(f"Error generating OAuth request: {str(e)}")
        return redirect("/auth?error=oauth_request_failed")

@google_auth.route("/google/callback")
def callback():
    """Handle Google OAuth callback"""
    if not GOOGLE_CLIENT_ID:
        return jsonify({"error": "Google OAuth not configured"}), 500
    
    # Check for errors from Google
    error = request.args.get("error")
    if error:
        print(f"Google OAuth error: {error}")
        print(f"Error description: {request.args.get('error_description', 'No description')}")
        return redirect("/auth?error=google_oauth_failed")
    
    code = request.args.get("code")
    if not code:
        print("No authorization code received from Google")
        return redirect("/auth?error=no_auth_code")
    
    try:
        google_provider_cfg = get_google_provider_cfg()
        token_endpoint = google_provider_cfg["token_endpoint"]
        
        # Exchange code for tokens
        token_url, headers, body = client.prepare_token_request(
            token_endpoint,
            authorization_response=request.url.replace("http://", "https://"),
            redirect_url=DEV_REDIRECT_URL,
            code=code,
        )
        
        print(f"Token request URL: {token_url}")
        print(f"Redirect URL being used: {DEV_REDIRECT_URL}")
        
        token_response = requests.post(
            token_url,
            headers=headers,
            data=body,
            auth=(GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET),
        )
        
        print(f"Token response status: {token_response.status_code}")
        if token_response.status_code != 200:
            print(f"Token exchange failed: {token_response.text}")
            return redirect("/auth?error=token_exchange_failed")
        
        client.parse_request_body_response(json.dumps(token_response.json()))
        
        # Get user info
        userinfo_endpoint = google_provider_cfg["userinfo_endpoint"]
        uri, headers, body = client.add_token(userinfo_endpoint)
        userinfo_response = requests.get(uri, headers=headers, data=body)
        userinfo = userinfo_response.json()
        
        if not userinfo.get("email_verified"):
            print("User email not verified by Google")
            return redirect("/auth?error=email_not_verified")
        
        # Process user authentication using auth_service
        provider_data = {
            'email': userinfo["email"],
            'id': userinfo["sub"],
            'given_name': userinfo.get("given_name", ""),
            'family_name': userinfo.get("family_name", ""),
            'name': userinfo.get("name", ""),
            'picture': userinfo.get("picture", "")
        }
        
        result = auth_service.oauth_login('google', provider_data)
        
        if result.get('success'):
            # Redirect to dashboard with token
            return redirect(f'/?token={result["access_token"]}')
        else:
            print(f"Auth service error: {result.get('error', 'Authentication failed')}")
            return redirect("/auth?error=auth_service_failed")
            
    except Exception as e:
        print(f"Google OAuth callback error: {str(e)}")
        return redirect("/auth?error=oauth_callback_failed")