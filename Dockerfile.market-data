# Dockerfile for Market Data Service
# This will be used when the service is deployed as a separate container

FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy market data service files
COPY market_data_service.js ./

# Create a simple server for the market data service
COPY market_data_server.js ./

# Expose port for health checks and API
EXPOSE 3001

# Health check for Kubernetes
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3001/health || exit 1

# Run the market data service
CMD ["node", "market_data_server.js"]
