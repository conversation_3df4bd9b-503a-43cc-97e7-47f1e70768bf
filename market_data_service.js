const axios = require('axios');

/**
 * Market Data Service - Microservice for cryptocurrency market data
 * Designed for Kubernetes deployment as a separate container
 */
class MarketDataService {
  constructor(options = {}) {
    this.baseUrl = options.baseUrl || 'https://api.coinbase.com/v2';
    this.fallbackUrl = options.fallbackUrl || 'https://api.coingecko.com/api/v3';
    this.cache = new Map();
    this.cacheTimeout = options.cacheTimeout || 60000; // 1 minute default
    this.retryAttempts = options.retryAttempts || 3;
    this.retryDelay = options.retryDelay || 1000;
    
    console.log('📊 Market Data Service initialized');
    console.log(`   Primary API: ${this.baseUrl}`);
    console.log(`   Fallback API: ${this.fallbackUrl}`);
    console.log(`   Cache timeout: ${this.cacheTimeout}ms`);
  }

  /**
   * Get current price for a cryptocurrency
   */
  async getPrice(symbol) {
    const cacheKey = `price_${symbol.toUpperCase()}`;
    
    // Check cache first
    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < this.cacheTimeout) {
        console.log(`📋 Returning cached price for ${symbol}: $${cached.price}`);
        return cached.price;
      }
    }

    try {
      console.log(`🔄 Fetching fresh price for ${symbol}...`);
      const price = await this.fetchPriceWithRetry(symbol);
      
      // Cache the result
      this.cache.set(cacheKey, {
        price: price,
        timestamp: Date.now()
      });
      
      console.log(`✅ Fresh price for ${symbol}: $${price}`);
      return price;
      
    } catch (error) {
      console.error(`❌ Failed to get price for ${symbol}:`, error.message);
      
      // Return cached data if available, even if expired
      if (this.cache.has(cacheKey)) {
        const cached = this.cache.get(cacheKey);
        console.log(`⚠️ Returning stale cached price for ${symbol}: $${cached.price}`);
        return cached.price;
      }
      
      throw error;
    }
  }

  /**
   * Fetch price with retry logic and fallback
   */
  async fetchPriceWithRetry(symbol) {
    let lastError;
    
    for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
      try {
        // Try primary API first
        return await this.fetchFromCoinbase(symbol);
      } catch (error) {
        lastError = error;
        console.log(`⚠️ Coinbase API attempt ${attempt} failed: ${error.message}`);
        
        if (attempt === this.retryAttempts) {
          // Try fallback API on final attempt
          try {
            console.log(`🔄 Trying fallback API (CoinGecko) for ${symbol}...`);
            return await this.fetchFromCoinGecko(symbol);
          } catch (fallbackError) {
            console.error(`❌ Fallback API also failed: ${fallbackError.message}`);
            throw new Error(`All APIs failed. Last error: ${lastError.message}`);
          }
        }
        
        // Wait before retry
        if (attempt < this.retryAttempts) {
          await this.sleep(this.retryDelay * attempt);
        }
      }
    }
    
    throw lastError;
  }

  /**
   * Fetch price from Coinbase API
   */
  async fetchFromCoinbase(symbol) {
    const pair = `${symbol.toUpperCase()}-USD`;
    const url = `${this.baseUrl}/exchange-rates?currency=${symbol.toUpperCase()}`;
    
    const response = await axios.get(url, {
      timeout: 5000,
      headers: {
        'User-Agent': 'MarketDataService/1.0'
      }
    });
    
    if (response.data && response.data.data && response.data.data.rates && response.data.data.rates.USD) {
      return parseFloat(response.data.data.rates.USD);
    }
    
    throw new Error('Invalid response format from Coinbase API');
  }

  /**
   * Fetch price from CoinGecko API (fallback)
   */
  async fetchFromCoinGecko(symbol) {
    const coinMap = {
      'BTC': 'bitcoin',
      'ETH': 'ethereum',
      'ADA': 'cardano',
      'SOL': 'solana'
    };
    
    const coinId = coinMap[symbol.toUpperCase()];
    if (!coinId) {
      throw new Error(`Unsupported symbol for CoinGecko: ${symbol}`);
    }
    
    const url = `${this.fallbackUrl}/simple/price?ids=${coinId}&vs_currencies=usd`;
    
    const response = await axios.get(url, {
      timeout: 5000,
      headers: {
        'User-Agent': 'MarketDataService/1.0'
      }
    });
    
    if (response.data && response.data[coinId] && response.data[coinId].usd) {
      return parseFloat(response.data[coinId].usd);
    }
    
    throw new Error('Invalid response format from CoinGecko API');
  }

  /**
   * Get multiple prices at once
   */
  async getPrices(symbols) {
    console.log(`🔄 Fetching prices for: ${symbols.join(', ')}`);
    
    const results = {};
    const promises = symbols.map(async (symbol) => {
      try {
        const price = await this.getPrice(symbol);
        results[symbol] = price;
      } catch (error) {
        console.error(`❌ Failed to get price for ${symbol}:`, error.message);
        results[symbol] = null;
      }
    });
    
    await Promise.all(promises);
    
    const successCount = Object.values(results).filter(price => price !== null).length;
    console.log(`✅ Successfully fetched ${successCount}/${symbols.length} prices`);
    
    return results;
  }

  /**
   * Health check endpoint for Kubernetes
   */
  async healthCheck() {
    try {
      // Test with a simple BTC price fetch
      await this.getPrice('BTC');
      
      return {
        status: 'healthy',
        service: 'MarketDataService',
        timestamp: new Date().toISOString(),
        cache_size: this.cache.size,
        uptime: process.uptime()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        service: 'MarketDataService',
        error: error.message,
        timestamp: new Date().toISOString(),
        cache_size: this.cache.size,
        uptime: process.uptime()
      };
    }
  }

  /**
   * Clear cache (useful for testing)
   */
  clearCache() {
    this.cache.clear();
    console.log('🗑️ Market data cache cleared');
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    const stats = {
      size: this.cache.size,
      entries: []
    };
    
    for (const [key, value] of this.cache.entries()) {
      stats.entries.push({
        key: key,
        age: Date.now() - value.timestamp,
        price: value.price
      });
    }
    
    return stats;
  }

  /**
   * Sleep utility for retry delays
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Graceful shutdown
   */
  async shutdown() {
    console.log('🛑 Market Data Service shutting down...');
    this.clearCache();
    console.log('✅ Market Data Service shutdown complete');
  }
}

module.exports = MarketDataService;
