"""
Market data provider for cryptocurrency prices and analysis
"""

import requests
import time
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

from config import Config
from logger_config import setup_logger

class MarketDataProvider:
    """Provides cryptocurrency market data from various sources"""
    
    def __init__(self, config: Config):
        """Initialize market data provider"""
        self.config = config
        self.logger = setup_logger()
        self.cache = {}
        self.cache_timeout = 60  # Cache data for 1 minute
        
        # API endpoints
        self.coinbase_pro_api = "https://api.exchange.coinbase.com"
        self.coingecko_api = "https://api.coingecko.com/api/v3"
        
        self.logger.info("Market data provider initialized")
    
    def get_current_data(self, trading_pair: str) -> Optional[Dict[str, Any]]:
        """Get current market data for a trading pair"""
        try:
            # Check cache first
            cache_key = f"current_{trading_pair}"
            if self._is_cache_valid(cache_key):
                return self.cache[cache_key]['data']
            
            # Fetch fresh data
            data = self._fetch_current_data(trading_pair)
            if data:
                # Cache the data
                self.cache[cache_key] = {
                    'data': data,
                    'timestamp': time.time()
                }
                return data
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting current market data: {str(e)}")
            return None
    
    def get_historical_data(self, trading_pair: str, days: int = 30) -> Optional[pd.DataFrame]:
        """Get historical market data"""
        try:
            # Check cache first
            cache_key = f"historical_{trading_pair}_{days}"
            if self._is_cache_valid(cache_key):
                return self.cache[cache_key]['data']
            
            # Fetch fresh data
            data = self._fetch_historical_data(trading_pair, days)
            if data is not None:
                # Cache the data
                self.cache[cache_key] = {
                    'data': data,
                    'timestamp': time.time()
                }
                return data
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting historical market data: {str(e)}")
            return None
    
    def _fetch_current_data(self, trading_pair: str) -> Optional[Dict[str, Any]]:
        """Fetch current market data from API"""
        try:
            # Try Coinbase Pro API first
            try:
                url = f"{self.coinbase_pro_api}/products/{trading_pair}/ticker"
                response = requests.get(url, timeout=10)
                response.raise_for_status()
                
                data = response.json()
                return {
                    'price': float(data['price']),
                    'bid': float(data['bid']),
                    'ask': float(data['ask']),
                    'volume': float(data['volume']),
                    'timestamp': datetime.now().timestamp(),
                    'source': 'coinbase_pro'
                }
                
            except (requests.RequestException, KeyError, ValueError) as e:
                self.logger.warning(f"Coinbase Pro API failed: {str(e)}, trying fallback")
                
                # Fallback to CoinGecko API
                return self._fetch_from_coingecko(trading_pair)
                
        except Exception as e:
            self.logger.error(f"Error fetching current data: {str(e)}")
            return None
    
    def _fetch_from_coingecko(self, trading_pair: str) -> Optional[Dict[str, Any]]:
        """Fetch data from CoinGecko API as fallback"""
        try:
            # Convert trading pair format (BTC-USD -> bitcoin, usd)
            crypto_symbol = trading_pair.split('-')[0].lower()
            fiat_symbol = trading_pair.split('-')[1].lower()
            
            # Map common symbols to CoinGecko IDs
            symbol_map = {
                'btc': 'bitcoin',
                'eth': 'ethereum',
                'ada': 'cardano',
                'dot': 'polkadot',
                'sol': 'solana',
                'matic': 'polygon',
                'avax': 'avalanche-2'
            }
            
            coin_id = symbol_map.get(crypto_symbol, crypto_symbol)
            
            url = f"{self.coingecko_api}/simple/price"
            params = {
                'ids': coin_id,
                'vs_currencies': fiat_symbol,
                'include_24hr_vol': 'true',
                'include_24hr_change': 'true'
            }
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            if coin_id not in data:
                self.logger.error(f"Coin {coin_id} not found in CoinGecko response")
                return None
            
            coin_data = data[coin_id]
            price = coin_data[fiat_symbol]
            
            return {
                'price': float(price),
                'bid': float(price * 0.999),  # Approximate bid/ask spread
                'ask': float(price * 1.001),
                'volume': float(coin_data.get(f'{fiat_symbol}_24h_vol', 0)),
                'change_24h': float(coin_data.get(f'{fiat_symbol}_24h_change', 0)),
                'timestamp': datetime.now().timestamp(),
                'source': 'coingecko'
            }
            
        except Exception as e:
            self.logger.error(f"Error fetching from CoinGecko: {str(e)}")
            return None
    
    def _fetch_historical_data(self, trading_pair: str, days: int) -> Optional[pd.DataFrame]:
        """Fetch historical market data"""
        try:
            # Try Coinbase Pro API first with limited time range
            try:
                # Coinbase Pro has restrictions on historical data range
                # Try with shorter periods first (max 7 days for unauthenticated requests)
                max_days = min(days, 7)
                end_time = datetime.now()
                start_time = end_time - timedelta(days=max_days)
                
                url = f"{self.coinbase_pro_api}/products/{trading_pair}/candles"
                params = {
                    'start': start_time.strftime('%Y-%m-%dT%H:%M:%S'),
                    'end': end_time.strftime('%Y-%m-%dT%H:%M:%S'),
                    'granularity': 3600  # 1 hour candles
                }
                
                response = requests.get(url, params=params, timeout=30)
                response.raise_for_status()
                
                data = response.json()
                if not data:
                    raise ValueError("No historical data returned")
                
                # Convert to DataFrame
                df = pd.DataFrame(data, columns=['timestamp', 'low', 'high', 'open', 'close', 'volume'])
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')
                df = df.sort_values('timestamp')
                df.set_index('timestamp', inplace=True)
                
                # Convert to numeric
                for col in ['low', 'high', 'open', 'close', 'volume']:
                    df[col] = pd.to_numeric(df[col])
                
                self.logger.info(f"Successfully fetched {len(df)} data points from Coinbase Pro")
                return df
                
            except (requests.RequestException, ValueError, KeyError) as e:
                self.logger.warning(f"Coinbase Pro historical data failed: {str(e)}, trying fallback")
                
                # Fallback to CoinGecko
                return self._fetch_historical_from_coingecko(trading_pair, days)
                
        except Exception as e:
            self.logger.error(f"Error fetching historical data: {str(e)}")
            return None
    
    def _fetch_historical_from_coingecko(self, trading_pair: str, days: int) -> Optional[pd.DataFrame]:
        """Fetch historical data from CoinGecko"""
        try:
            crypto_symbol = trading_pair.split('-')[0].lower()
            fiat_symbol = trading_pair.split('-')[1].lower()
            
            # Map symbols
            symbol_map = {
                'btc': 'bitcoin',
                'eth': 'ethereum',
                'ada': 'cardano',
                'dot': 'polkadot',
                'sol': 'solana',
                'matic': 'polygon',
                'avax': 'avalanche-2'
            }
            
            coin_id = symbol_map.get(crypto_symbol, crypto_symbol)
            
            # Use the free public API endpoint that doesn't require authentication
            url = f"{self.coingecko_api}/coins/{coin_id}/market_chart"
            params = {
                'vs_currency': fiat_symbol,
                'days': str(min(days, 365)),  # Free tier supports up to 365 days
                'interval': 'daily' if days > 1 else 'hourly'
            }
            
            # Add headers to mimic browser request
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'application/json',
                'Accept-Language': 'en-US,en;q=0.9'
            }
            
            response = requests.get(url, params=params, headers=headers, timeout=30)
            
            # Check if we hit rate limits
            if response.status_code == 429:
                self.logger.warning("CoinGecko rate limit hit, waiting before retry...")
                time.sleep(10)
                response = requests.get(url, params=params, headers=headers, timeout=30)
            
            if response.status_code == 200:
                self.logger.info(f"Successfully fetched historical data from CoinGecko for {coin_id}")
            else:
                response.raise_for_status()
            data = response.json()
            
            if 'prices' not in data or not data['prices']:
                self.logger.warning("No price data returned from CoinGecko")
                return None
            
            # Convert to DataFrame
            prices = data['prices']
            df = pd.DataFrame(prices, columns=['timestamp', 'close'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            
            # Sort by timestamp
            df = df.sort_index()
            
            # Add realistic OHLC data based on close prices
            df['open'] = df['close'].shift(1)
            df['high'] = df[['close', 'open']].max(axis=1) * (1 + np.random.uniform(0, 0.02, len(df)))
            df['low'] = df[['close', 'open']].min(axis=1) * (1 - np.random.uniform(0, 0.02, len(df)))
            df['volume'] = np.random.uniform(1000000, 10000000, len(df))  # Simulated volume
            
            # Fill first open value
            df.loc[df.index[0], 'open'] = df.loc[df.index[0], 'close']
            
            # Ensure no negative values
            df[df < 0] = df.abs()
            
            return df
            
        except Exception as e:
            self.logger.error(f"Error fetching historical data from CoinGecko: {str(e)}")
            return None
    
    def calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate technical indicators"""
        try:
            if df is None or df.empty:
                return df
            
            # Simple Moving Average
            df[f'sma_{self.config.MOVING_AVERAGE_PERIOD}'] = df['close'].rolling(
                window=self.config.MOVING_AVERAGE_PERIOD
            ).mean()
            
            # RSI (Relative Strength Index)
            df['rsi'] = self._calculate_rsi(df['close'], self.config.RSI_PERIOD)
            
            # MACD
            ema_12 = df['close'].ewm(span=12).mean()
            ema_26 = df['close'].ewm(span=26).mean()
            df['macd'] = ema_12 - ema_26
            df['macd_signal'] = df['macd'].ewm(span=9).mean()
            df['macd_histogram'] = df['macd'] - df['macd_signal']
            
            # Bollinger Bands
            sma_20 = df['close'].rolling(window=20).mean()
            std_20 = df['close'].rolling(window=20).std()
            df['bb_upper'] = sma_20 + (std_20 * 2)
            df['bb_lower'] = sma_20 - (std_20 * 2)
            df['bb_middle'] = sma_20
            
            return df
            
        except Exception as e:
            self.logger.error(f"Error calculating technical indicators: {str(e)}")
            return df
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI (Relative Strength Index)"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            
            return rsi
            
        except Exception as e:
            self.logger.error(f"Error calculating RSI: {str(e)}")
            return pd.Series(dtype=float)
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cached data is still valid"""
        if cache_key not in self.cache:
            return False
        
        cache_age = time.time() - self.cache[cache_key]['timestamp']
        return cache_age < self.cache_timeout
    
    def clear_cache(self):
        """Clear all cached data"""
        self.cache.clear()
        self.logger.info("Market data cache cleared")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """Get information about cached data"""
        cache_info = {}
        current_time = time.time()
        
        for key, value in self.cache.items():
            age = current_time - value['timestamp']
            cache_info[key] = {
                'age_seconds': age,
                'is_valid': age < self.cache_timeout,
                'data_type': type(value['data']).__name__
            }
        
        return cache_info
