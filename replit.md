# EZ Money 2 - AI Cryptocurrency Trading Agent

## Overview

EZ Money 2 is a comprehensive AI-powered cryptocurrency trading agent built with Python, Flask, and Coinbase AgentKit. The system provides automated Bitcoin trading capabilities with a web-based dashboard, real-time portfolio monitoring, and advanced technical analysis. The application supports both simulation (dry-run) and live trading modes.

## System Architecture

The application follows a modular architecture with clear separation of concerns:

- **Frontend**: Responsive HTML dashboard with real-time updates and manual trading controls
- **Backend**: Python Flask API server with SQLAlchemy ORM for database operations
- **Trading Engine**: Coinbase AgentKit integration with custom trading strategies
- **Database Layer**: PostgreSQL for transaction history and portfolio tracking
- **Authentication**: Google OAuth integration with JWT token management
- **AI Integration**: Multiple LLM providers for enhanced market analysis

## Key Components

### Core Trading System
- **Trading Agent** (`trading_agent.py`): Main trading logic with AgentKit integration
- **Wallet Manager** (`wallet_manager.py`): Balance tracking and transaction management
- **Market Data Provider** (`market_data.py`): Real-time price feeds from multiple sources
- **Trading Strategy** (`trading_strategy.py`): Technical analysis with RSI, moving averages, and trend following

### Web Interface
- **Flask Web App** (`web_app.py`): Main web server with REST API endpoints
- **Dashboard Template** (`templates/dashboard.html`): Real-time trading interface
- **Authentication System**: Google OAuth with user management

### Database Models
- **Transactions**: Complete audit trail of all trading operations
- **Portfolio**: Balance tracking across multiple currencies
- **Market Data**: Historical price and volume data
- **User Management**: Authentication and authorization

### Configuration Management
- Environment-based configuration with `.env` support
- Separate settings for development and production
- Configurable trading parameters and risk management

## Data Flow

1. **Market Data Collection**: Real-time price feeds from Coinbase Pro and CoinGecko APIs
2. **Technical Analysis**: RSI calculations, moving averages, and trend detection
3. **AI Enhancement**: Optional LLM analysis for market sentiment and decision support
4. **Trading Decisions**: Automated buy/sell signals based on strategy parameters
5. **Order Execution**: Coinbase AgentKit integration for actual trade execution
6. **Database Persistence**: All transactions and portfolio changes stored in PostgreSQL
7. **Web Dashboard Updates**: Real-time display of portfolio and trading activity

## External Dependencies

### Required APIs
- **Coinbase CDP API**: Primary trading and wallet management
- **Coinbase Pro API**: Market data and price feeds
- **CoinGecko API**: Alternative market data source
- **Google OAuth API**: User authentication (optional)

### LLM Providers (Optional)
- OpenAI GPT-4o for market analysis
- DeepSeek R1 for alternative AI insights
- Custom LLM endpoints supported

### Database
- PostgreSQL 16 for production deployment
- SQLite fallback for development

## Deployment Strategy

The application is designed for flexible deployment:

### Development Environment
- Replit-based development with automatic dependency management
- Hot reload for rapid development
- Integrated PostgreSQL database

### Production Deployment
- GitLab CI/CD integration
- Environment variable configuration
- Scalable Flask application server
- Database migration support

### Configuration Options
- **Dry Run Mode**: Safe testing with simulated trades
- **Live Trading**: Real money operations with Coinbase
- **Risk Management**: Configurable trade limits and stop-loss
- **Multiple Strategies**: Trend following, mean reversion, RSI-based

## Changelog

- June 14, 2025. Completed comprehensive biometric authentication system with WebAuthn/passkey support
- June 13, 2025. Initial setup

## User Preferences

Preferred communication style: Simple, everyday language.