const FacebookStrategy = require('passport-facebook').Strategy;

/**
 * Facebook OAuth Login Module
 * Handles Facebook OAuth authentication strategy and routes
 */

class FacebookLogin {
  constructor(passport, db) {
    this.passport = passport;
    this.db = db;
    this.config = {
      clientID: process.env.FACEBOOK_APP_ID || 'FACEBOOK_APP_ID',
      clientSecret: process.env.FACEBOOK_APP_SECRET || 'FACEBOOK_APP_SECRET',
      callbackURL: process.env.FACEBOOK_CALLBACK_URL || '/auth/facebook/callback'
    };
  }

  /**
   * Initialize Facebook OAuth strategy
   */
  initializeStrategy() {
    console.log('Initializing Facebook OAuth strategy...');
    console.log('Facebook App ID:', this.config.clientID.substring(0, 10) + '...');
    
    this.passport.use('facebook', new FacebookStrategy({
      clientID: this.config.clientID,
      clientSecret: this.config.clientSecret,
      callbackURL: this.config.callbackURL,
      profileFields: ['id', 'emails', 'name', 'displayName']
    }, (accessToken, refreshToken, profile, done) => {
      console.log('Facebook OAuth callback triggered for user:', profile.id);
      this.handleOAuthCallback(accessToken, refreshToken, profile, done);
    }));
  }

  /**
   * Handle OAuth callback from Facebook
   */
  handleOAuthCallback(accessToken, refreshToken, profile, done) {
    const providerId = profile.id;
    const email = profile.emails && profile.emails[0] && profile.emails[0].value;
    const name = profile.displayName || (profile.name ? `${profile.name.givenName} ${profile.name.familyName}` : null);

    console.log('Processing Facebook user:', { id: providerId, email, name });

    // Check if user already exists
    this.db.get(
      'SELECT * FROM users WHERE provider = ? AND provider_id = ?', 
      ['facebook', providerId], 
      (err, row) => {
        if (err) {
          console.error('Database error during Facebook user lookup:', err);
          return done(err);
        }

        if (row) {
          console.log('Existing Facebook user found:', row.id);
          return done(null, row);
        }

        // Check if user exists with same email but different provider
        this.db.get(
          'SELECT * FROM users WHERE email = ?',
          [email],
          (err, existingUser) => {
            if (err) {
              console.error('Database error during email lookup:', err);
              return done(err);
            }

            if (existingUser) {
              // Link Facebook account to existing user
              console.log('Linking Facebook account to existing user:', existingUser.id);
              this.db.run(
                'UPDATE users SET provider = ?, provider_id = ? WHERE id = ?',
                ['facebook', providerId, existingUser.id],
                (err) => {
                  if (err) {
                    console.error('Error linking Facebook account:', err);
                    return done(err);
                  }
                  
                  // Return updated user
                  this.db.get('SELECT * FROM users WHERE id = ?', existingUser.id, (e, updatedUser) => {
                    if (e) return done(e);
                    console.log('Successfully linked Facebook account');
                    done(null, updatedUser);
                  });
                }
              );
            } else {
              // Create new user
              console.log('Creating new Facebook user');
              this.createNewUser(email, providerId, name, done);
            }
          }
        );
      }
    );
  }

  /**
   * Create a new user from Facebook OAuth
   */
  createNewUser(email, providerId, name, done) {
    const db = this.db; // Capture db reference for use in callback
    db.run(
      'INSERT INTO users (email, provider, provider_id, name) VALUES (?, ?, ?, ?)',
      [email, 'facebook', providerId, name],
      function(err) {
        if (err) {
          console.error('Error creating Facebook user:', err);
          return done(err);
        }

        const newUserId = this.lastID;
        console.log('New Facebook user created with ID:', newUserId);

        // Fetch the newly created user
        db.get('SELECT * FROM users WHERE id = ?', newUserId, (e, newUser) => {
          if (e) {
            console.error('Error fetching newly created user:', e);
            return done(e);
          }
          if (!newUser) {
            console.error('Failed to create Facebook user - user not found after insertion');
            return done(null, false);
          }
          console.log('Successfully created and retrieved Facebook user');
          done(null, newUser);
        });
      }
    );
  }

  /**
   * Setup Facebook OAuth routes
   */
  setupRoutes(app) {
    console.log('Setting up Facebook OAuth routes...');

    // Initiate Facebook OAuth
    app.get('/auth/facebook', 
      this.passport.authenticate('facebook', { 
        scope: ['email'] 
      })
    );

    // Facebook OAuth callback
    app.get('/auth/facebook/callback',
      this.passport.authenticate('facebook', {
        successRedirect: '/trading',
        failureRedirect: '/login',
        failureFlash: false
      })
    );

    console.log('Facebook OAuth routes configured');
  }

  /**
   * Get configuration status
   */
  getConfigStatus() {
    return {
      provider: 'facebook',
      configured: this.config.clientID !== 'FACEBOOK_APP_ID' && 
                  this.config.clientSecret !== 'FACEBOOK_APP_SECRET',
      clientID: this.config.clientID.substring(0, 10) + '...',
      callbackURL: this.config.callbackURL
    };
  }
}

module.exports = FacebookLogin;
