"""
Trading strategy implementation with technical analysis
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
from datetime import datetime

from config import Config
from market_data import MarketDataProvider
from logger_config import setup_logger

class TradingStrategy:
    """Implements various trading strategies"""
    
    def __init__(self, config: Config):
        """Initialize trading strategy"""
        self.config = config
        self.logger = setup_logger()
        self.market_data_provider = MarketDataProvider(config)
        
        # Strategy state
        self.last_signal = None
        self.last_signal_time = None
        self.position_history = []
        
        self.logger.info(f"Trading strategy initialized: {config.STRATEGY_TYPE}")
    
    def analyze_market(self, current_data: Dict[str, Any], sensitivity: float = 0.5) -> Optional[Dict[str, Any]]:
        """Analyze market and generate trading signals"""
        try:
            if self.config.STRATEGY_TYPE == "trend_following":
                return self._trend_following_strategy(current_data, sensitivity)
            elif self.config.STRATEGY_TYPE == "mean_reversion":
                return self._mean_reversion_strategy(current_data)
            elif self.config.STRATEGY_TYPE == "rsi_based":
                return self._rsi_based_strategy(current_data)
            else:
                self.logger.warning(f"Unknown strategy type: {self.config.STRATEGY_TYPE}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error analyzing market: {str(e)}")
            return None
    
    def _trend_following_strategy(self, current_data: Dict[str, Any], sensitivity: float = 0.5) -> Optional[Dict[str, Any]]:
        """Trend following strategy using moving averages"""
        try:
            # Get historical data for technical analysis
            historical_data = self.market_data_provider.get_historical_data(
                self.config.TRADING_PAIR, days=30
            )
            
            if historical_data is None or len(historical_data) < self.config.MOVING_AVERAGE_PERIOD:
                self.logger.warning("Insufficient historical data for trend analysis")
                return None
            
            # Calculate technical indicators
            df = self.market_data_provider.calculate_technical_indicators(historical_data)
            
            if df.empty:
                return None
            
            # Get latest values
            latest = df.iloc[-1]
            current_price = current_data['price']
            
            # Get moving average
            sma_column = f'sma_{self.config.MOVING_AVERAGE_PERIOD}'
            if sma_column not in latest or pd.isna(latest[sma_column]):
                self.logger.warning("Moving average not available")
                return None
            
            sma_value = latest[sma_column]
            
            # Trend following logic
            price_above_sma = current_price > sma_value
            price_change_pct = ((current_price - sma_value) / sma_value) * 100
            
            # Calculate confidence based on distance from SMA
            confidence = min(abs(price_change_pct) / 3.0, 1.0)  # Max confidence at 3% deviation
            
            # Dynamic thresholds based on sensitivity (0.1 = very sensitive, 2.0 = less sensitive)
            buy_threshold = sensitivity * 0.5  # 0.05% to 1.0%
            dip_threshold = sensitivity * 0.3  # 0.03% to 0.6%
            
            # Generate trading signals based on configurable sensitivity
            if price_above_sma and price_change_pct > buy_threshold:  # Price above SMA - buy opportunity
                signal = {
                    'action': 'buy',
                    'confidence': max(confidence, 0.6),  # Minimum 60% confidence
                    'reason': f'Price ${current_price:.2f} is {price_change_pct:.2f}% above SMA ${sma_value:.2f} - uptrend detected (sensitivity: {sensitivity})',
                    'timestamp': datetime.now().timestamp()
                }
            elif not price_above_sma and abs(price_change_pct) > dip_threshold:  # Price below SMA - potential buy on dip
                signal = {
                    'action': 'buy',  # Buy the dip strategy
                    'confidence': max(confidence, 0.7),  # Higher confidence for dip buying
                    'reason': f'Price ${current_price:.2f} is {abs(price_change_pct):.2f}% below SMA ${sma_value:.2f} - buying opportunity (sensitivity: {sensitivity})',
                    'timestamp': datetime.now().timestamp()
                }
            else:
                # No clear signal
                return None
            
            # Check if signal is different from last signal to avoid spam
            if self._should_generate_signal(signal):
                self.logger.info(f"Generated {signal['action']} signal: {signal['reason']}")
                self.last_signal = signal
                self.last_signal_time = datetime.now().timestamp()
                return signal
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error in trend following strategy: {str(e)}")
            return None
    
    def _rsi_based_strategy(self, current_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """RSI-based trading strategy"""
        try:
            # Get historical data
            historical_data = self.market_data_provider.get_historical_data(
                self.config.TRADING_PAIR, days=30
            )
            
            if historical_data is None or len(historical_data) < self.config.RSI_PERIOD + 5:
                self.logger.warning("Insufficient historical data for RSI analysis")
                return None
            
            # Calculate technical indicators
            df = self.market_data_provider.calculate_technical_indicators(historical_data)
            
            if df.empty or 'rsi' not in df.columns:
                return None
            
            # Get latest RSI value
            latest_rsi = df['rsi'].iloc[-1]
            
            if pd.isna(latest_rsi):
                self.logger.warning("RSI value not available")
                return None
            
            current_price = current_data['price']
            
            # RSI-based signals
            if latest_rsi < self.config.RSI_OVERSOLD:
                # Oversold condition - potential buy signal
                confidence = (self.config.RSI_OVERSOLD - latest_rsi) / self.config.RSI_OVERSOLD
                signal = {
                    'action': 'buy',
                    'confidence': min(confidence, 1.0),
                    'reason': f'RSI oversold at {latest_rsi:.2f} (threshold: {self.config.RSI_OVERSOLD})',
                    'timestamp': datetime.now().timestamp(),
                    'rsi_value': latest_rsi
                }
                
            elif latest_rsi > self.config.RSI_OVERBOUGHT:
                # Overbought condition - potential sell signal
                confidence = (latest_rsi - self.config.RSI_OVERBOUGHT) / (100 - self.config.RSI_OVERBOUGHT)
                signal = {
                    'action': 'sell',
                    'confidence': min(confidence, 1.0),
                    'reason': f'RSI overbought at {latest_rsi:.2f} (threshold: {self.config.RSI_OVERBOUGHT})',
                    'timestamp': datetime.now().timestamp(),
                    'rsi_value': latest_rsi
                }
            else:
                # No signal in neutral zone
                return None
            
            # Check if we should generate this signal
            if self._should_generate_signal(signal):
                self.logger.info(f"Generated RSI {signal['action']} signal: {signal['reason']}")
                self.last_signal = signal
                self.last_signal_time = datetime.now().timestamp()
                return signal
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error in RSI strategy: {str(e)}")
            return None
    
    def _mean_reversion_strategy(self, current_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Mean reversion strategy using Bollinger Bands"""
        try:
            # Get historical data
            historical_data = self.market_data_provider.get_historical_data(
                self.config.TRADING_PAIR, days=30
            )
            
            if historical_data is None or len(historical_data) < 20:
                self.logger.warning("Insufficient historical data for Bollinger Bands")
                return None
            
            # Calculate technical indicators
            df = self.market_data_provider.calculate_technical_indicators(historical_data)
            
            if df.empty or 'bb_upper' not in df.columns:
                return None
            
            # Get latest Bollinger Band values
            latest = df.iloc[-1]
            current_price = current_data['price']
            
            bb_upper = latest['bb_upper']
            bb_lower = latest['bb_lower']
            bb_middle = latest['bb_middle']
            
            if pd.isna(bb_upper) or pd.isna(bb_lower) or pd.isna(bb_middle):
                self.logger.warning("Bollinger Bands values not available")
                return None
            
            # Mean reversion logic
            if current_price < bb_lower:
                # Price below lower band - potential buy signal
                distance_pct = ((bb_lower - current_price) / bb_middle) * 100
                confidence = min(distance_pct / 5.0, 1.0)  # Max confidence at 5% below
                
                signal = {
                    'action': 'buy',
                    'confidence': confidence,
                    'reason': f'Price ${current_price:.2f} below lower BB ${bb_lower:.2f}',
                    'timestamp': datetime.now().timestamp(),
                    'bb_position': 'below_lower'
                }
                
            elif current_price > bb_upper:
                # Price above upper band - potential sell signal
                distance_pct = ((current_price - bb_upper) / bb_middle) * 100
                confidence = min(distance_pct / 5.0, 1.0)  # Max confidence at 5% above
                
                signal = {
                    'action': 'sell',
                    'confidence': confidence,
                    'reason': f'Price ${current_price:.2f} above upper BB ${bb_upper:.2f}',
                    'timestamp': datetime.now().timestamp(),
                    'bb_position': 'above_upper'
                }
            else:
                # Price within bands - no signal
                return None
            
            # Check if we should generate this signal
            if self._should_generate_signal(signal):
                self.logger.info(f"Generated mean reversion {signal['action']} signal: {signal['reason']}")
                self.last_signal = signal
                self.last_signal_time = datetime.now().timestamp()
                return signal
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error in mean reversion strategy: {str(e)}")
            return None
    
    def _should_generate_signal(self, signal: Dict[str, Any]) -> bool:
        """Check if we should generate a new signal"""
        try:
            # Don't generate signals too frequently
            if self.last_signal_time:
                time_since_last = datetime.now().timestamp() - self.last_signal_time
                min_interval = 300  # 5 minutes minimum between signals
                
                if time_since_last < min_interval:
                    return False
            
            # Don't generate same signal repeatedly
            if self.last_signal:
                if (self.last_signal['action'] == signal['action'] and 
                    abs(self.last_signal['confidence'] - signal['confidence']) < 0.1):
                    return False
            
            # Only generate signals above minimum confidence threshold
            min_confidence = 0.3
            if signal['confidence'] < min_confidence:
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error checking signal generation: {str(e)}")
            return False
    
    def get_strategy_status(self) -> Dict[str, Any]:
        """Get current strategy status"""
        try:
            return {
                "strategy_type": self.config.STRATEGY_TYPE,
                "last_signal": self.last_signal,
                "last_signal_time": self.last_signal_time,
                "parameters": {
                    "rsi_period": self.config.RSI_PERIOD,
                    "rsi_oversold": self.config.RSI_OVERSOLD,
                    "rsi_overbought": self.config.RSI_OVERBOUGHT,
                    "moving_average_period": self.config.MOVING_AVERAGE_PERIOD
                },
                "position_history_count": len(self.position_history)
            }
        except Exception as e:
            self.logger.error(f"Error getting strategy status: {str(e)}")
            return {"error": str(e)}
    
    def add_position_to_history(self, position: Dict[str, Any]):
        """Add a position to history for analysis"""
        try:
            position['timestamp'] = datetime.now().timestamp()
            self.position_history.append(position)
            
            # Keep only last 100 positions
            if len(self.position_history) > 100:
                self.position_history = self.position_history[-100:]
                
        except Exception as e:
            self.logger.error(f"Error adding position to history: {str(e)}")
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Calculate basic performance metrics"""
        try:
            if not self.position_history:
                return {"message": "No trading history available"}
            
            # Basic metrics calculation
            total_trades = len(self.position_history)
            buy_trades = len([p for p in self.position_history if p.get('action') == 'buy'])
            sell_trades = len([p for p in self.position_history if p.get('action') == 'sell'])
            
            return {
                "total_trades": total_trades,
                "buy_trades": buy_trades,
                "sell_trades": sell_trades,
                "strategy_type": self.config.STRATEGY_TYPE,
                "analysis_period": "30_days"
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating performance metrics: {str(e)}")
            return {"error": str(e)}
