# EZ Money 2 - AI Cryptocurrency Trading Agent

An advanced AI-powered cryptocurrency trading agent with web interface, real-time portfolio management, and comprehensive database persistence.

## Features

- **AI-Powered Trading**: Automated BTC trading with technical analysis
- **Web Dashboard**: Real-time portfolio monitoring and manual trading controls
- **Database Persistence**: Complete transaction history and portfolio tracking
- **Risk Management**: Configurable trade limits and balance validation
- **Market Data Integration**: Real-time price feeds from multiple sources
- **Dry Run Mode**: Safe testing environment with simulated trades

## Architecture

- **Backend**: Python Flask API with PostgreSQL database
- **Frontend**: Responsive HTML dashboard with real-time updates
- **Trading Engine**: Coinbase AgentKit integration with custom strategies
- **Database**: PostgreSQL with transaction history and portfolio snapshots

## Quick Start

1. **Install Dependencies**:
   ```bash
   pip install coinbase-agentkit flask sqlalchemy psycopg2-binary python-dotenv requests pandas numpy
   ```

2. **Database Setup**:
   - Configure PostgreSQL database
   - Set DATABASE_URL environment variable

3. **Run the Application**:
   ```bash
   # Start the trading agent
   python main.py
   
   # Start the web interface (separate terminal)
   python web_app.py
   ```

4. **Access Dashboard**: Open http://localhost:5000

## Configuration

Key settings in `config.py`:
- `DRY_RUN`: Enable/disable simulation mode
- `TRADING_PAIR`: Cryptocurrency pair (default: BTC-USD)
- `MAX_TRADE_AMOUNT`: Maximum trade size
- `TRADING_INTERVAL`: Time between trading cycles

## Project Structure

```
├── main.py              # Application entry point
├── trading_agent.py     # Core trading logic
├── web_app.py          # Flask web interface
├── wallet_manager.py   # Coinbase AgentKit integration
├── trading_strategy.py # Technical analysis strategies
├── market_data.py      # Price data providers
├── database.py         # PostgreSQL models and operations
├── config.py           # Configuration management
├── logger_config.py    # Logging setup
├── cli_interface.py    # Command-line interface
└── templates/
    └── dashboard.html  # Web dashboard template
```

## API Endpoints

- `GET /` - Web dashboard
- `GET /api/status` - Trading agent status
- `GET /api/portfolio` - Portfolio balances
- `GET /api/transactions` - Transaction history
- `GET /api/market-data` - Current market prices
- `POST /api/force-trade` - Manual trade execution

## Database Schema

- **transactions**: Trade history with amounts, prices, timestamps
- **portfolios**: Portfolio snapshots over time
- **market_data**: Historical price and volume data
- **trading_strategies**: Strategy signals and performance

## Security Notes

- All API keys stored as environment variables
- Database connections use connection pooling
- Input validation on all API endpoints
- Dry run mode prevents accidental live trading

## Recent Updates

- Fixed custom trade amount handling in web interface
- Improved balance calculation from transaction history
- Added input field clearing after successful trades
- Enhanced database persistence for all operations
- Resolved sell order execution issues

## License

MIT License - See LICENSE file for details