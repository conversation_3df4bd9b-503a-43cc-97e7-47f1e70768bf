#!/bin/bash

# Commit script for LLM integration features
echo "Committing LLM integration changes..."

# Add all changes
git add -A

# Commit with descriptive message
git commit -m "feat: Add comprehensive LLM integration for AI-enhanced trading

- Implement multiple LLM providers (OpenAI GPT-4o, DeepSeek R1, FinGPT, Bloomberg GPT, Custom)
- Add hybrid decision-making system combining technical analysis with AI insights
- Create LLM manager with provider switching and API key management
- Update web interface with AI configuration controls
- Add API endpoints for LLM provider management
- Enhance autonomous agent with AI-powered market sentiment analysis
- Integrate confidence boosting and reasoning from AI models
- Support for custom LLM endpoints and enterprise models"

# Push to remote
git push origin main

echo "✅ LLM integration committed and pushed successfully!"