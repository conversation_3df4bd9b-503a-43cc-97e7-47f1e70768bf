"""
Main trading agent implementation using Coinbase AgentKit
"""

import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from coinbase_agentkit import AgentKit, AgentKitConfig

from config import Config
from wallet_manager import WalletManager
from market_data import MarketDataProvider
from trading_strategy import TradingStrategy
from logger_config import setup_logger

class TradingAgent:
    """AI Cryptocurrency Trading Agent"""
    
    def __init__(self, config: Config):
        """Initialize the trading agent"""
        self.config = config
        self.logger = setup_logger()
        self.is_running = False
        self.trade_thread = None
        self.daily_trade_count = 0
        self.last_trade_reset = datetime.now().date()
        
        # Initialize AgentKit
        try:
            if config.DRY_RUN:
                self.logger.info("Initializing in dry-run mode - Agent<PERSON>it will be mocked")
                # In dry-run mode, we'll use a mock AgentKit to avoid credential requirements
                self.agent_kit = None
            else:
                self.logger.info("Initializing AgentKit for live trading")
                agent_config = AgentKitConfig(**config.get_agentkit_config())
                self.agent_kit = AgentKit(config=agent_config)
            
            self.logger.info("AgentKit initialization completed")
        except Exception as e:
            self.logger.error(f"Failed to initialize AgentKit: {str(e)}")
            if not config.DRY_RUN:
                raise
            else:
                self.logger.warning("Continuing in dry-run mode without AgentKit")
                self.agent_kit = None
        
        # Initialize components
        self.wallet_manager = WalletManager(self.agent_kit, config)
        self.market_data = MarketDataProvider(config)
        self.trading_strategy = TradingStrategy(config)
        
        self.logger.info("Trading agent initialized successfully")
    
    def start(self):
        """Start the trading agent"""
        if self.is_running:
            self.logger.warning("Trading agent is already running")
            return
        
        self.logger.info("Starting trading agent")
        self.is_running = True
        
        # Start trading thread
        self.trade_thread = threading.Thread(target=self._trading_loop, daemon=True)
        self.trade_thread.start()
        
        self.logger.info("Trading agent started successfully")
    
    def stop(self):
        """Stop the trading agent"""
        if not self.is_running:
            return
        
        self.logger.info("Stopping trading agent")
        self.is_running = False
        
        if self.trade_thread and self.trade_thread.is_alive():
            self.trade_thread.join(timeout=10)
        
        self.logger.info("Trading agent stopped")
    
    def _trading_loop(self):
        """Main trading loop"""
        self.logger.info("Trading loop started")
        
        while self.is_running:
            try:
                # Reset daily trade count if new day
                self._reset_daily_trades_if_needed()
                
                # Check if we've reached daily trade limit
                if self.daily_trade_count >= self.config.MAX_DAILY_TRADES:
                    self.logger.info(f"Daily trade limit ({self.config.MAX_DAILY_TRADES}) reached")
                    time.sleep(self.config.TRADING_INTERVAL)
                    continue
                
                # Get market data
                market_data = self.market_data.get_current_data(self.config.TRADING_PAIR)
                if not market_data:
                    self.logger.warning("No market data available, skipping trading cycle")
                    time.sleep(self.config.TRADING_INTERVAL)
                    continue
                
                # Get wallet balance
                balance = self.wallet_manager.get_balance()
                if not balance:
                    self.logger.warning("Could not retrieve balance, skipping trading cycle")
                    time.sleep(self.config.TRADING_INTERVAL)
                    continue
                
                # Execute trading strategy
                signal = self.trading_strategy.analyze_market(market_data)
                if signal:
                    success = self._execute_trade_signal(signal, balance, market_data)
                    if success:
                        self.daily_trade_count += 1
                
                # Wait for next trading cycle
                time.sleep(self.config.TRADING_INTERVAL)
                
            except Exception as e:
                self.logger.error(f"Error in trading loop: {str(e)}")
                time.sleep(60)  # Wait 1 minute before retrying
    
    def _execute_trade_signal(self, signal: Dict[str, Any], balance: Dict[str, float], market_data: Dict[str, Any]) -> bool:
        """Execute a trading signal"""
        try:
            action = signal['action']  # 'buy' or 'sell'
            confidence = signal['confidence']
            price = market_data['price']
            custom_amount = signal.get('amount')  # Custom amount from user input
            
            self.logger.info(f"Received {action} signal with confidence {confidence:.2f}")
            
            if action == 'buy':
                return self._execute_buy_order(balance, price, confidence, custom_amount)
            elif action == 'sell':
                return self._execute_sell_order(balance, price, confidence, custom_amount)
            else:
                self.logger.warning(f"Unknown trading action: {action}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error executing trade signal: {str(e)}")
            return False
    
    def _execute_buy_order(self, balance: Dict[str, float], price: float, confidence: float, custom_amount: float = None) -> bool:
        """Execute a buy order"""
        try:
            base_currency_balance = balance.get(self.config.DEFAULT_BASE_CURRENCY, 0)
            
            # Use custom amount if provided, otherwise calculate automatically
            if custom_amount is not None:
                trade_amount = float(custom_amount)
                self.logger.info(f"Using custom trade amount: ${trade_amount:.2f}")
                
                # Check if we have sufficient balance
                if trade_amount > base_currency_balance:
                    self.logger.error(f"Insufficient balance for buy order: ${base_currency_balance:.2f} < ${trade_amount:.2f}")
                    return False
            else:
                # Calculate trade amount based on confidence and available balance
                if base_currency_balance < self.config.MIN_TRADE_AMOUNT:
                    self.logger.info(f"Insufficient balance for buy order: {base_currency_balance}")
                    return False
                
                # Calculate trade amount (scale with confidence)
                max_trade = min(self.config.MAX_TRADE_AMOUNT, base_currency_balance * 0.9)
                trade_amount = min(max_trade, self.config.MIN_TRADE_AMOUNT + 
                                 (max_trade - self.config.MIN_TRADE_AMOUNT) * confidence)
            
            # Execute buy order using wallet manager (handles both dry-run and live trading)
            result = self.wallet_manager.buy_crypto(
                self.config.DEFAULT_QUOTE_CURRENCY,
                trade_amount,
                self.config.DEFAULT_BASE_CURRENCY
            )
            
            if result:
                self.logger.info(f"Successfully bought {trade_amount / price:.8f} {self.config.DEFAULT_QUOTE_CURRENCY} "
                               f"for ${trade_amount:.2f} at price ${price:.2f}")
                return True
            else:
                self.logger.error("Buy order failed")
                return False
                
        except Exception as e:
            self.logger.error(f"Error executing buy order: {str(e)}")
            return False
    
    def _execute_sell_order(self, balance: Dict[str, float], price: float, confidence: float, custom_amount: float = None) -> bool:
        """Execute a sell order"""
        try:
            crypto_balance = balance.get(self.config.DEFAULT_QUOTE_CURRENCY, 0)
            
            # Use custom amount if provided (convert USD to BTC), otherwise calculate automatically
            if custom_amount is not None:
                # Custom amount is in USD, convert to BTC
                sell_amount = float(custom_amount) / price
                self.logger.info(f"Using custom sell amount: ${custom_amount:.2f} (≈{sell_amount:.8f} BTC)")
                
                # Check if we have sufficient crypto balance
                if sell_amount > crypto_balance:
                    self.logger.error(f"Insufficient crypto balance for sell order: {crypto_balance:.8f} < {sell_amount:.8f}")
                    return False
            else:
                # Calculate sell amount automatically
                if crypto_balance * price < self.config.MIN_TRADE_AMOUNT:
                    self.logger.info(f"Insufficient crypto balance for sell order: {crypto_balance}")
                    return False
                
                # Calculate sell amount (scale with confidence)
                max_sell_amount = crypto_balance * 0.9  # Keep some for fees
                sell_amount = max_sell_amount * confidence
            
            # Execute sell order using wallet manager (handles both dry-run and live trading)
            result = self.wallet_manager.sell_crypto(
                self.config.DEFAULT_QUOTE_CURRENCY,
                sell_amount,
                self.config.DEFAULT_BASE_CURRENCY
            )
            
            if result:
                trade_value = sell_amount * price
                self.logger.info(f"Successfully sold {sell_amount:.8f} {self.config.DEFAULT_QUOTE_CURRENCY} "
                               f"for ${trade_value:.2f} at price ${price:.2f}")
                return True
            else:
                self.logger.error("Sell order failed")
                return False
                
        except Exception as e:
            self.logger.error(f"Error executing sell order: {str(e)}")
            return False
    
    def _reset_daily_trades_if_needed(self):
        """Reset daily trade count if it's a new day"""
        current_date = datetime.now().date()
        if current_date > self.last_trade_reset:
            self.daily_trade_count = 0
            self.last_trade_reset = current_date
            self.logger.info("Daily trade count reset")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        try:
            balance = self.wallet_manager.get_balance()
            market_data = self.market_data.get_current_data(self.config.TRADING_PAIR)
            
            return {
                "is_running": self.is_running,
                "daily_trades": self.daily_trade_count,
                "max_daily_trades": self.config.MAX_DAILY_TRADES,
                "balance": balance,
                "current_price": market_data.get('price') if market_data else None,
                "trading_pair": self.config.TRADING_PAIR,
                "dry_run": self.config.DRY_RUN,
                "last_update": datetime.now().isoformat()
            }
        except Exception as e:
            self.logger.error(f"Error getting agent status: {str(e)}")
            return {
                "is_running": self.is_running,
                "error": str(e),
                "last_update": datetime.now().isoformat()
            }
    
    def get_transaction_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent transaction history"""
        return self.wallet_manager.get_transaction_history(limit)
    
    def force_trade_signal(self, action: str, amount: float = None) -> bool:
        """Force execute a trade signal (for manual testing)"""
        try:
            balance = self.wallet_manager.get_balance()
            market_data = self.market_data.get_current_data(self.config.TRADING_PAIR)
            
            if not balance or not market_data:
                return False
            
            signal = {
                'action': action,
                'confidence': 1.0,
                'amount': amount
            }
            
            return self._execute_trade_signal(signal, balance, market_data)
            
        except Exception as e:
            self.logger.error(f"Error forcing trade signal: {str(e)}")
            return False
