#!/usr/bin/env python3
"""
Simple test script to verify the CryptoTraderAI bridge works
"""

from flask import Flask, jsonify
from flask_cors import CORS
from datetime import datetime
import os

app = Flask(__name__)
CORS(app)

@app.route('/health')
def health():
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'message': 'Simple test bridge is working',
        'python_version': os.sys.version,
        'cwd': os.getcwd()
    })

@app.route('/')
def home():
    return """
    <html>
    <head><title>CryptoTraderAI Test Bridge</title></head>
    <body style="font-family: Arial; margin: 40px;">
        <h1>🧪 CryptoTraderAI Test Bridge</h1>
        <p>✅ Flask server is running successfully!</p>
        <p>🔗 This confirms the Python bridge can start and respond to requests.</p>
        <p>📊 <a href="/health">Health Check</a></p>
        <p>🔙 <a href="javascript:window.parent.location.href='/logout'">Back to MultiLogin</a></p>
    </body>
    </html>
    """

@app.route('/api/status')
def status():
    return jsonify({
        'initialized': True,
        'message': 'Test bridge is running',
        'timestamp': datetime.now().isoformat()
    })

if __name__ == '__main__':
    print("🧪 Starting Simple Test Bridge...")
    print("📍 Available at: http://localhost:8080")

    app.run(
        host='0.0.0.0',
        port=8080,
        debug=True,
        threaded=True
    )
