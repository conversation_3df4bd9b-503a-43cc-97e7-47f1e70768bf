<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EZ Money Trading Agent - Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #0f1419;
            color: #ffffff;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #1e3a8a, #3b82f6);
            border-radius: 10px;
        }
        
        .header-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .btn-logout {
            background-color: #ef4444;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: background-color 0.2s;
        }
        
        .btn-logout:hover {
            background-color: #dc2626;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            margin: 0 10px;
        }
        
        .status-running {
            background-color: #10b981;
            color: white;
        }
        
        .status-stopped {
            background-color: #ef4444;
            color: white;
        }
        
        .status-dry-run {
            background-color: #f59e0b;
            color: white;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background-color: #1f2937;
            border-radius: 10px;
            padding: 20px;
            border: 1px solid #374151;
        }
        
        .card h3 {
            color: #60a5fa;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }
        
        .portfolio-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #374151;
        }
        
        .portfolio-item:last-child {
            border-bottom: none;
        }
        
        .currency-info {
            display: flex;
            flex-direction: column;
        }
        
        .currency-name {
            font-weight: bold;
            font-size: 1.1rem;
        }
        
        .currency-amount {
            color: #9ca3af;
            font-size: 0.9rem;
        }
        
        .currency-value {
            text-align: right;
            display: flex;
            flex-direction: column;
        }
        
        .value-usd {
            font-weight: bold;
            color: #10b981;
        }
        
        .value-percentage {
            color: #9ca3af;
            font-size: 0.9rem;
        }
        
        .metric {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
        }
        
        .metric-label {
            color: #9ca3af;
        }
        
        .metric-value {
            font-weight: bold;
        }
        
        .price-positive {
            color: #10b981;
        }
        
        .price-negative {
            color: #ef4444;
        }
        
        .transactions-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .transactions-table th,
        .transactions-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #374151;
        }
        
        .transactions-table th {
            background-color: #374151;
            color: #60a5fa;
        }
        
        .trade-buy {
            color: #10b981;
        }
        
        .trade-sell {
            color: #ef4444;
        }
        
        .loading {
            text-align: center;
            color: #9ca3af;
            padding: 20px;
        }
        
        .error {
            background-color: #7f1d1d;
            color: #fca5a5;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .refresh-btn {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 0;
        }
        
        .refresh-btn:hover {
            background-color: #2563eb;
        }
        
        .total-portfolio {
            font-size: 1.5rem;
            font-weight: bold;
            color: #10b981;
            text-align: center;
            padding: 15px;
            background-color: #065f46;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .trading-section {
            padding: 15px 0;
        }
        
        .trading-section h4 {
            color: #60a5fa;
            margin-bottom: 15px;
            font-size: 1.1rem;
        }
        
        .trade-buttons {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .trade-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .trade-group label {
            color: #9ca3af;
            font-size: 0.9rem;
        }
        
        .trade-group input {
            padding: 10px;
            border: 1px solid #374151;
            border-radius: 5px;
            background-color: #1f2937;
            color: white;
            font-size: 1rem;
        }
        
        .trade-group input:focus {
            outline: none;
            border-color: #60a5fa;
        }
        
        .trade-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            font-size: 1rem;
            flex: 1;
            transition: all 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-1px);
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn-buy {
            background-color: #10b981;
            color: white;
        }
        
        .btn-buy:hover:not(:disabled) {
            background-color: #059669;
        }
        
        .btn-sell {
            background-color: #ef4444;
            color: white;
        }
        
        .btn-sell:hover:not(:disabled) {
            background-color: #dc2626;
        }
        
        .trade-status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
            text-align: center;
            display: none;
        }
        
        .trade-status.success {
            background-color: #065f46;
            color: #10b981;
            display: block;
        }
        
        .trade-status.error {
            background-color: #7f1d1d;
            color: #fca5a5;
            display: block;
        }
        
        .trade-status.loading {
            background-color: #1e3a8a;
            color: #60a5fa;
            display: block;
        }
        
        .agent-status-running {
            color: #10b981;
            font-weight: bold;
        }
        
        .agent-status-stopped {
            color: #f87171;
            font-weight: bold;
        }
        
        .decision-buy {
            border-left: 4px solid #10b981;
            background-color: rgba(16, 185, 129, 0.1);
        }
        
        .decision-sell {
            border-left: 4px solid #f87171;
            background-color: rgba(248, 113, 113, 0.1);
        }
        
        .decision-hold {
            border-left: 4px solid #fbbf24;
            background-color: rgba(251, 191, 36, 0.1);
        }
        
        .decision-item {
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 5px;
            background-color: #1f2937;
        }
        
        .decision-time {
            font-size: 0.8rem;
            color: #9ca3af;
            margin-bottom: 5px;
        }
        
        .decision-details {
            color: #e5e7eb;
            margin-bottom: 3px;
        }
        
        .decision-sentiment {
            font-size: 0.8rem;
            color: #60a5fa;
            font-style: italic;
        }
        
        .wallet-section {
            padding: 10px 0;
        }
        
        .wallet-address {
            margin-bottom: 25px;
        }
        
        .wallet-address label {
            color: #9ca3af;
            font-size: 0.9rem;
            display: block;
            margin-bottom: 8px;
        }
        
        .address-display {
            background-color: #1f2937;
            border: 1px solid #374151;
            border-radius: 5px;
            padding: 12px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            word-break: break-all;
            margin-bottom: 8px;
            position: relative;
        }
        
        .address-display:hover {
            background-color: #374151;
            cursor: pointer;
        }
        
        .address-warning {
            color: #fbbf24;
            font-size: 0.8rem;
            font-weight: 500;
            margin-top: 5px;
        }
        
        .send-section {
            border-top: 1px solid #374151;
            padding-top: 20px;
        }
        
        .send-section h4 {
            color: #60a5fa;
            margin-bottom: 15px;
            font-size: 1.1rem;
        }
        
        .send-form {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        /* Autonomous Agent Styles */
        .agent-config {
            display: grid;
            gap: 20px;
        }
        
        .agent-status {
            background-color: #1f2937;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #374151;
        }
        
        .agent-setup, .agent-performance, .agent-decisions {
            background-color: #111827;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #2d3748;
        }
        
        .agent-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 15px;
        }
        
        .agent-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        
        .agent-metric {
            background-color: #1f2937;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }
        
        .agent-metric-value {
            font-size: 1.2rem;
            font-weight: bold;
            color: #10b981;
        }
        
        .agent-metric-label {
            font-size: 0.9rem;
            color: #9ca3af;
        }
        
        .decision-item {
            background-color: #1f2937;
            padding: 10px;
            border-radius: 5px;
            margin: 5px 0;
            border-left: 4px solid #3b82f6;
        }
        
        .decision-buy {
            border-left-color: #10b981;
        }
        
        .decision-sell {
            border-left-color: #ef4444;
        }
        
        .decision-time {
            font-size: 0.8rem;
            color: #9ca3af;
        }
        
        .decision-details {
            margin-top: 5px;
            font-size: 0.9rem;
        }
        
        .agent-status-running {
            color: #10b981;
            font-weight: bold;
        }
        
        .agent-status-stopped {
            color: #ef4444;
            font-weight: bold;
        }
        
        .security-warning {
            background: #fef3cd;
            border: 1px solid #fbbf24;
            color: #92400e;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .range-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .range-container input[type="range"] {
            flex: 1;
        }
        
        .range-container span {
            min-width: 30px;
            color: #60a5fa;
            font-weight: bold;
        }
        
        /* Tooltip Styles */
        .tooltip {
            position: relative;
            display: inline-block;
            cursor: help;
            color: #60a5fa;
            margin-left: 5px;
        }
        
        .tooltip .tooltip-icon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background-color: #3b82f6;
            color: white;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        
        .tooltip .tooltip-text {
            visibility: hidden;
            width: 280px;
            background-color: #1f2937;
            color: #e5e7eb;
            text-align: left;
            border-radius: 8px;
            padding: 12px;
            position: absolute;
            z-index: 1000;
            bottom: 125%;
            left: 50%;
            margin-left: -140px;
            opacity: 0;
            transition: opacity 0.3s;
            border: 1px solid #374151;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            font-size: 14px;
            line-height: 1.4;
        }
        
        .tooltip .tooltip-text::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: #1f2937 transparent transparent transparent;
        }
        
        .tooltip:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
        }
        
        .tooltip-title {
            font-weight: bold;
            color: #60a5fa;
            margin-bottom: 6px;
        }
        
        .tooltip-description {
            color: #d1d5db;
        }
        
        /* Responsive tooltips */
        @media (max-width: 768px) {
            .tooltip .tooltip-text {
                width: 240px;
                margin-left: -120px;
            }
        }

        /* Enhanced Interactive Tooltip Styles */
        .interactive-tooltip {
            position: relative;
            display: inline-block;
        }

        .animated-icon {
            display: inline-block;
            animation: pulse 2s infinite;
            transition: all 0.3s ease;
        }

        .animated-icon:hover {
            animation: none;
            transform: scale(1.2);
            color: #60a5fa;
            cursor: pointer;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.6; }
            100% { opacity: 1; }
        }

        .enhanced-tooltip-panel {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 600px;
            max-width: 90vw;
            max-height: 80vh;
            background: linear-gradient(145deg, #1f2937, #111827);
            border: 2px solid #374151;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
            z-index: 10000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
        }

        .enhanced-tooltip-panel.active {
            opacity: 1;
            visibility: visible;
            transform: translate(-50%, -50%) scale(1);
        }

        .tooltip-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 24px 16px;
            border-bottom: 1px solid #374151;
            background: linear-gradient(90deg, #1f2937, #374151);
        }

        .tooltip-header .tooltip-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #60a5fa;
            margin: 0;
        }

        .tooltip-close {
            background: none;
            border: none;
            color: #9ca3af;
            font-size: 1.5rem;
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 4px;
            transition: all 0.2s;
        }

        .tooltip-close:hover {
            background-color: #374151;
            color: #f3f4f6;
        }

        .tooltip-content {
            padding: 24px;
            overflow-y: auto;
            max-height: calc(80vh - 80px);
        }

        .sensitivity-explanation {
            margin-bottom: 24px;
        }

        .sensitivity-explanation p {
            color: #d1d5db;
            font-size: 1rem;
            line-height: 1.6;
            margin: 0;
        }

        .sensitivity-visual {
            margin-bottom: 24px;
        }

        .price-chart-demo {
            position: relative;
            height: 120px;
            background: #0f172a;
            border-radius: 8px;
            margin-bottom: 20px;
            padding: 16px;
            border: 1px solid #334155;
        }

        .chart-line {
            width: 100%;
            height: 80px;
            background: linear-gradient(90deg, 
                #10b981 0%, #10b981 20%,
                #ef4444 20%, #ef4444 40%,
                #10b981 40%, #10b981 60%,
                #f59e0b 60%, #f59e0b 80%,
                #10b981 80%);
            border-radius: 4px;
            position: relative;
            overflow: hidden;
        }

        .chart-line::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: repeating-linear-gradient(
                90deg,
                transparent,
                transparent 2px,
                rgba(255, 255, 255, 0.1) 2px,
                rgba(255, 255, 255, 0.1) 4px
            );
            animation: chartFlow 3s linear infinite;
        }

        @keyframes chartFlow {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .trade-indicators {
            position: absolute;
            top: 16px;
            right: 16px;
            display: flex;
            gap: 8px;
        }

        .sensitivity-levels {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 16px;
        }

        .level-card {
            background: #0f172a;
            border: 2px solid #334155;
            border-radius: 12px;
            padding: 16px;
            transition: all 0.3s;
            cursor: pointer;
        }

        .level-card:hover {
            border-color: #60a5fa;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(96, 165, 250, 0.15);
        }

        .level-card.very-sensitive {
            border-color: #ef4444;
        }

        .level-card.very-sensitive:hover {
            border-color: #f87171;
            box-shadow: 0 8px 20px rgba(239, 68, 68, 0.2);
        }

        .level-card.balanced {
            border-color: #10b981;
        }

        .level-card.balanced:hover {
            border-color: #34d399;
            box-shadow: 0 8px 20px rgba(16, 185, 129, 0.2);
        }

        .level-card.conservative {
            border-color: #f59e0b;
        }

        .level-card.conservative:hover {
            border-color: #fbbf24;
            box-shadow: 0 8px 20px rgba(245, 158, 11, 0.2);
        }

        .level-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .level-value {
            font-weight: 600;
            color: #f3f4f6;
            font-size: 0.9rem;
        }

        .level-name {
            font-weight: 500;
            color: #9ca3af;
            font-size: 0.85rem;
        }

        .level-description {
            space-y: 8px;
        }

        .trade-frequency {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            font-size: 0.85rem;
            color: #d1d5db;
        }

        .frequency-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            animation: blink 2s infinite;
        }

        .frequency-indicator.high {
            background-color: #ef4444;
            animation-duration: 0.5s;
        }

        .frequency-indicator.medium {
            background-color: #10b981;
            animation-duration: 1.5s;
        }

        .frequency-indicator.low {
            background-color: #f59e0b;
            animation-duration: 3s;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        .trigger-threshold {
            font-size: 0.8rem;
            color: #9ca3af;
            font-style: italic;
        }

        .current-setting {
            background: linear-gradient(135deg, #1e293b, #0f172a);
            border: 1px solid #334155;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
        }

        .setting-label {
            font-size: 0.9rem;
            color: #9ca3af;
            margin-bottom: 8px;
        }

        .setting-value {
            font-size: 1.2rem;
            font-weight: 600;
            color: #60a5fa;
            margin-bottom: 8px;
        }

        .setting-impact {
            font-size: 0.85rem;
            color: #d1d5db;
            line-height: 1.4;
        }

        .enhanced-range {
            position: relative;
        }

        .range-value-display {
            background: #1f2937;
            border: 1px solid #374151;
            padding: 4px 8px;
            border-radius: 6px;
            font-weight: 500;
            color: #60a5fa;
            min-width: 40px;
            text-align: center;
        }

        .range-labels {
            display: flex;
            justify-content: space-between;
            margin-top: 4px;
        }

        .range-label-min,
        .range-label-max {
            font-size: 0.75rem;
            color: #9ca3af;
        }

        /* Backdrop */
        .tooltip-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s;
        }

        .tooltip-backdrop.active {
            opacity: 1;
            visibility: visible;
        }

        /* Mobile responsive */
        @media (max-width: 768px) {
            .enhanced-tooltip-panel {
                width: 95vw;
                max-height: 90vh;
            }

            .tooltip-content {
                padding: 16px;
            }

            .sensitivity-levels {
                grid-template-columns: 1fr;
            }
        }
        
        .btn-secondary {
            background-color: #7c3aed;
            border-color: #7c3aed;
        }
        
        .btn-secondary:hover {
            background-color: #6d28d9;
            border-color: #6d28d9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>EZ Money Trading Agent
                <span class="tooltip">
                    <span class="tooltip-icon">?</span>
                    <span class="tooltip-text">
                        <div class="tooltip-title">EZ Money Trading Agent</div>
                        <div class="tooltip-description">Your complete Bitcoin trading control center! Monitor your autonomous agent, track portfolio performance, execute manual trades, and manage your Bitcoin wallet. The system uses intelligent algorithms to accumulate Bitcoin while you stay in full control.</div>
                    </span>
                </span>
            </h1>
            <div class="header-right">
                <div id="agent-status">
                    <span class="loading">Loading...</span>
                </div>
                <button onclick="logout()" class="btn-logout" title="Logout">
                    🚪 Logout
                </button>
            </div>
        </div>
        
        <div class="grid">
            <!-- Portfolio Overview -->
            <div class="card">
                <h3>Portfolio Overview
                    <span class="tooltip">
                        <span class="tooltip-icon">?</span>
                        <span class="tooltip-text">
                            <div class="tooltip-title">Your Portfolio</div>
                            <div class="tooltip-description">Your current holdings and their values. This shows how much USD and Bitcoin you own, plus the total value of everything combined. Watch this grow as your trading agent accumulates more Bitcoin!</div>
                        </span>
                    </span>
                </h3>
                <div id="total-value" class="total-portfolio">
                    Loading...
                </div>
                <div id="portfolio-breakdown">
                    <div class="loading">Loading portfolio data...</div>
                </div>
            </div>
            
            <!-- Bitcoin Wallet -->
            <div class="card">
                <h3>Bitcoin Wallet
                    <span class="tooltip">
                        <span class="tooltip-icon">?</span>
                        <span class="tooltip-text">
                            <div class="tooltip-title">Bitcoin Wallet</div>
                            <div class="tooltip-description">Your Bitcoin wallet for receiving and sending Bitcoin. The receive address is where others can send Bitcoin to you. You can also send Bitcoin to other addresses from here. Always double-check addresses before sending!</div>
                        </span>
                    </span>
                </h3>
                <div class="wallet-section">
                    <div class="wallet-address">
                        <label>Receive Address:</label>
                        <div id="wallet-address" class="address-display">
                            <span class="loading">Loading address...</span>
                        </div>
                        <div class="address-warning">
                            ⚠️ Only send Bitcoin to this address. Do not send other cryptocurrencies.
                        </div>
                    </div>
                    
                    <div class="send-section">
                        <h4>Send Bitcoin</h4>
                        <div class="send-form">
                            <div class="trade-group">
                                <label>Send To Address:</label>
                                <input type="text" id="send-address" placeholder="Enter Bitcoin address (1..., 3..., or bc1...)">
                                <div class="address-warning">
                                    ⚠️ Only send to Bitcoin network addresses. Double-check before sending.
                                </div>
                            </div>
                            <div class="trade-group">
                                <label>Amount (USD):</label>
                                <input type="number" id="send-amount" placeholder="Enter amount in USD" min="0.01" step="0.01">
                            </div>
                            <button class="btn btn-secondary" onclick="sendBitcoin()">Send Bitcoin</button>
                            <div id="send-status" class="trade-status"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Market Data -->
            <div class="card">
                <h3>Market Data</h3>
                <div id="market-data">
                    <div class="loading">Loading market data...</div>
                </div>
                <button class="refresh-btn" onclick="refreshData()">Refresh Data</button>
            </div>
            
            <!-- Trading Status -->
            <div class="card">
                <h3>Trading Status</h3>
                <div id="trading-status">
                    <div class="loading">Loading trading status...</div>
                </div>
            </div>
        </div>
        
        <!-- Autonomous Agent Controls -->
        <div class="card">
            <h3>🤖 Autonomous Trading Agent</h3>
            <div id="agent-controls">
                <div class="agent-config">
                    <div class="agent-status" id="agent-status">
                        <div class="loading">Loading agent status...</div>
                    </div>
                    
                    <div class="agent-setup">
                        <h4>Agent Configuration</h4>
                        
                        <div class="trade-group">
                            <label for="agent-mode">Trading Mode:
                                <span class="tooltip">
                                    <span class="tooltip-icon">?</span>
                                    <span class="tooltip-text">
                                        <div class="tooltip-title">Trading Mode</div>
                                        <div class="tooltip-description">Autonomous = agent makes all trading decisions automatically. Interactive = agent suggests trades but waits for your approval before executing. Autonomous is recommended for hands-off Bitcoin accumulation.</div>
                                    </span>
                                </span>
                            </label>
                            <select id="agent-mode" class="trade-input">
                                <option value="autonomous" selected>Autonomous (Default)</option>
                                <option value="interactive">Interactive (Manual Approval)</option>
                            </select>
                        </div>
                        
                        <div class="trade-group">
                            <label for="agent-name">Agent Name:
                                <span class="tooltip">
                                    <span class="tooltip-icon">?</span>
                                    <span class="tooltip-text">
                                        <div class="tooltip-title">Agent Name</div>
                                        <div class="tooltip-description">Give your trading bot a personalized name! This helps you identify and connect with your automated trader. Popular names include BitHunter, CryptoTrader, or anything that motivates you.</div>
                                    </span>
                                </span>
                            </label>
                            <input type="text" id="agent-name" placeholder="BitHunter" value="BitHunter">
                        </div>
                        
                        <div class="trade-group">
                            <label for="risk-tolerance">Risk Tolerance:
                                <span class="tooltip">
                                    <span class="tooltip-icon">?</span>
                                    <span class="tooltip-text">
                                        <div class="tooltip-title">Risk Tolerance</div>
                                        <div class="tooltip-description">How much risk you're comfortable with. Low (0.1-0.3) = very safe, only trades when very confident. Medium (0.4-0.7) = balanced approach. High (0.8-1.0) = more aggressive, willing to take bigger chances for bigger gains.</div>
                                    </span>
                                </span>
                            </label>
                            <div class="range-container">
                                <input type="range" id="risk-tolerance" min="0" max="1" step="0.1" value="0.7">
                                <span id="risk-value">0.7</span>
                            </div>
                        </div>
                        
                        <div class="trade-group">
                            <label for="aggression-level">Aggression Level:
                                <span class="tooltip">
                                    <span class="tooltip-icon">?</span>
                                    <span class="tooltip-text">
                                        <div class="tooltip-title">Aggression Level</div>
                                        <div class="tooltip-description">How eagerly your agent hunts for Bitcoin. Low (0.1-0.3) = patient, waits for perfect opportunities. Medium (0.4-0.7) = balanced trading frequency. High (0.8-1.0) = very active, constantly looking for ways to accumulate more Bitcoin.</div>
                                    </span>
                                </span>
                            </label>
                            <div class="range-container">
                                <input type="range" id="aggression-level" min="0" max="1" step="0.1" value="0.8">
                                <span id="aggression-value">0.8</span>
                            </div>
                        </div>
                        
                        <div class="trade-group">
                            <label for="sensitivity">Trading Sensitivity:
                                <span class="interactive-tooltip" id="sensitivity-tooltip">
                                    <span class="tooltip-icon animated-icon">?</span>
                                    <div class="enhanced-tooltip-panel" id="sensitivity-panel">
                                        <div class="tooltip-header">
                                            <div class="tooltip-title">Trading Sensitivity</div>
                                            <button class="tooltip-close" onclick="closeSensitivityTooltip()">×</button>
                                        </div>
                                        
                                        <div class="tooltip-content">
                                            <div class="sensitivity-explanation">
                                                <p>Controls how responsive your agent is to Bitcoin price movements:</p>
                                            </div>
                                            
                                            <div class="sensitivity-visual">
                                                <div class="price-chart-demo">
                                                    <div class="chart-line" id="demo-chart"></div>
                                                    <div class="trade-indicators" id="trade-indicators"></div>
                                                </div>
                                                
                                                <div class="sensitivity-levels">
                                                    <div class="level-card very-sensitive" data-level="0.2">
                                                        <div class="level-header">
                                                            <span class="level-value">0.1-0.3</span>
                                                            <span class="level-name">Very Sensitive</span>
                                                        </div>
                                                        <div class="level-description">
                                                            <div class="trade-frequency">
                                                                <span class="frequency-indicator high"></span>
                                                                High frequency trading
                                                            </div>
                                                            <div class="trigger-threshold">Triggers on 0.03-0.15% moves</div>
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="level-card balanced" data-level="0.5">
                                                        <div class="level-header">
                                                            <span class="level-value">0.4-0.7</span>
                                                            <span class="level-name">Balanced</span>
                                                        </div>
                                                        <div class="level-description">
                                                            <div class="trade-frequency">
                                                                <span class="frequency-indicator medium"></span>
                                                                Moderate trading
                                                            </div>
                                                            <div class="trigger-threshold">Triggers on 0.2-0.35% moves</div>
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="level-card conservative" data-level="1.5">
                                                        <div class="level-header">
                                                            <span class="level-value">0.8-2.0</span>
                                                            <span class="level-name">Conservative</span>
                                                        </div>
                                                        <div class="level-description">
                                                            <div class="trade-frequency">
                                                                <span class="frequency-indicator low"></span>
                                                                Selective trading
                                                            </div>
                                                            <div class="trigger-threshold">Triggers on 0.4-1.0% moves</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="current-setting" id="current-sensitivity-display">
                                                <div class="setting-label">Current Setting:</div>
                                                <div class="setting-value" id="current-sensitivity-value">0.5 (Balanced)</div>
                                                <div class="setting-impact" id="current-sensitivity-impact">
                                                    Expects ~3-8 trades per day depending on market volatility
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </span>
                            </label>
                            <div class="range-container enhanced-range">
                                <input type="range" id="sensitivity" min="0.1" max="2.0" step="0.1" value="0.5" oninput="updateSensitivityDisplay(this.value); updateAgentSensitivity(this.value)">
                                <span id="sensitivity-value" class="range-value-display">0.5</span>
                                <div class="range-labels">
                                    <span class="range-label-min">Very Sensitive</span>
                                    <span class="range-label-max">Conservative</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="trade-group">
                            <label for="llm-enabled">AI Enhancement:
                                <span class="tooltip">
                                    <span class="tooltip-icon">?</span>
                                    <span class="tooltip-text">
                                        <div class="tooltip-title">AI Enhancement</div>
                                        <div class="tooltip-description">Enable advanced AI models to enhance trading decisions. The system combines traditional technical analysis with AI insights for better market understanding and risk assessment. OpenAI is used by default.</div>
                                    </span>
                                </span>
                            </label>
                            <div class="checkbox-container">
                                <input type="checkbox" id="llm-enabled" name="llm_enabled" onchange="toggleLLMProvider()">
                                <label for="llm-enabled" class="checkbox-label">Enable AI Analysis</label>
                            </div>
                        </div>
                        
                        <div class="trade-group" id="llm-provider-group" style="display: none;">
                            <label for="llm-provider">AI Model:
                                <span class="tooltip">
                                    <span class="tooltip-icon">?</span>
                                    <span class="tooltip-text">
                                        <div class="tooltip-title">AI Model Selection</div>
                                        <div class="tooltip-description">Choose your AI provider: OpenAI GPT-4o (default, general analysis), DeepSeek R1 (advanced reasoning), FinGPT (financial markets), Bloomberg GPT (institutional insights), or Custom (your own endpoint).</div>
                                    </span>
                                </span>
                            </label>
                            <select id="llm-provider" class="trade-input" onchange="handleProviderChange()">
                                <option value="openai">OpenAI GPT-4o (Default)</option>
                                <option value="deepseek">DeepSeek R1 (Reasoning)</option>
                                <option value="fingpt">FinGPT (Financial)</option>
                                <option value="bloomberg">Bloomberg GPT (Institutional)</option>
                                <option value="custom">Custom Model</option>
                            </select>
                        </div>
                        
                        <div class="trade-group" id="custom-endpoint-group" style="display: none;">
                            <label for="custom-endpoint">Custom Endpoint URL:
                                <span class="tooltip">
                                    <span class="tooltip-icon">?</span>
                                    <span class="tooltip-text">
                                        <div class="tooltip-title">Custom Model Endpoint</div>
                                        <div class="tooltip-description">Enter the API endpoint URL for your custom AI model. This should be a compatible OpenAI-style API endpoint.</div>
                                    </span>
                                </span>
                            </label>
                            <input type="url" id="custom-endpoint" class="trade-input" placeholder="https://api.your-model.com/v1">
                        </div>
                        
                        <div class="agent-actions">
                            <button class="btn btn-primary" id="configure-agent-btn" onclick="configureAgent()">Configure Agent</button>
                            <button class="btn btn-success" id="start-agent-btn" onclick="startAgent()">Start Agent</button>
                            <button class="btn btn-danger" id="stop-agent-btn" onclick="stopAgent()" style="display: none;">Stop Agent</button>
                            <button class="btn btn-secondary" onclick="forceAnalysis()">Force Analysis</button>
                            <button class="btn btn-warning" onclick="enableTestingMode()" id="testing-btn">Enable Testing (10min)</button>
                        </div>
                        
                        <div id="agent-config-status" class="trade-status"></div>
                    </div>
                    
                    <div class="agent-performance">
                        <h4>Agent Performance
                            <span class="tooltip">
                                <span class="tooltip-icon">?</span>
                                <span class="tooltip-text">
                                    <div class="tooltip-title">Performance Metrics</div>
                                    <div class="tooltip-description">Track how well your agent is doing at accumulating Bitcoin. Total Trades = how many buy/sell decisions made. Success Rate = percentage of profitable trades. BTC Accumulated = total Bitcoin gained. Profit/Loss = money made or lost in USD.</div>
                                </span>
                            </span>
                        </h4>
                        <div id="agent-metrics">
                            <div class="loading">Configure agent to see performance metrics...</div>
                        </div>
                    </div>
                    
                    <div class="agent-decisions">
                        <h4>Recent Decisions
                            <span class="tooltip">
                                <span class="tooltip-icon">?</span>
                                <span class="tooltip-text">
                                    <div class="tooltip-title">Trading Decisions</div>
                                    <div class="tooltip-description">See what your agent is thinking! This shows recent buy/sell decisions with the reasoning behind each trade. Green = buy decisions, Red = sell decisions. Confidence shows how sure the agent was about the decision.</div>
                                </span>
                            </span>
                        </h4>
                        <div id="agent-decision-log">
                            <div class="loading">Start agent to see trading decisions...</div>
                        </div>
                    </div>
                    
                    <!-- Interactive Mode Pending Trades -->
                    <div class="agent-pending" id="agent-pending-section" style="display: none;">
                        <h4>Pending Trade Approvals
                            <span class="tooltip">
                                <span class="tooltip-icon">?</span>
                                <span class="tooltip-text">
                                    <div class="tooltip-title">Pending Trades</div>
                                    <div class="tooltip-description">In Interactive mode, your agent suggests trades but waits for your approval. Review each trade recommendation and approve or reject based on your judgment. This gives you full control while benefiting from AI analysis.</div>
                                </span>
                            </span>
                        </h4>
                        <div id="pending-trades-list">
                            <div class="loading">No pending trades...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Trading Controls -->
        <div class="card">
            <h3>Trading Controls
                <span class="tooltip">
                    <span class="tooltip-icon">?</span>
                    <span class="tooltip-text">
                        <div class="tooltip-title">Manual Trading</div>
                        <div class="tooltip-description">Take control and make your own trading decisions! Use these buttons to manually buy or sell Bitcoin when you spot a good opportunity. The system will execute your trade at current market prices.</div>
                    </span>
                </span>
            </h3>
            <div id="trading-controls">
                <div class="trading-section">
                    <h4>Manual BTC Trading</h4>
                    <div class="trade-buttons">
                        <div class="trade-group">
                            <label for="trade-amount">Amount (USD):
                                <span class="tooltip">
                                    <span class="tooltip-icon">?</span>
                                    <span class="tooltip-text">
                                        <div class="tooltip-title">Trade Amount</div>
                                        <div class="tooltip-description">How much money to use for this trade. For buying: this is the USD amount to spend on Bitcoin. For selling: this is the USD value of Bitcoin to sell. Start with smaller amounts to test the system.</div>
                                    </span>
                                </span>
                            </label>
                            <input type="number" id="trade-amount" placeholder="100" min="1" step="1" value="100">
                        </div>
                        <div class="trade-actions">
                            <button class="btn btn-buy" onclick="executeTrade('buy')">Buy BTC</button>
                            <button class="btn btn-sell" onclick="executeTrade('sell')">Sell BTC</button>
                        </div>
                    </div>
                    <div id="trade-status" class="trade-status"></div>
                </div>
            </div>
        </div>

        <!-- Transaction History -->
        <div class="card">
            <h3>Recent Transactions
                <span class="tooltip">
                    <span class="tooltip-icon">?</span>
                    <span class="tooltip-text">
                        <div class="tooltip-title">Transaction History</div>
                        <div class="tooltip-description">Your recent trading activity. This shows all the buy and sell orders that have been executed, whether by you manually or by your autonomous agent. Each transaction shows the amount, price, and timestamp.</div>
                    </span>
                </span>
            </h3>
            <div id="transactions">
                <div class="loading">Loading transactions...</div>
            </div>
        </div>
    </div>

    <script>
        let refreshInterval;
        
        // Check authentication on page load
        function checkAuth() {
            const token = localStorage.getItem('access_token');
            if (!token) {
                window.location.href = '/auth';
                return false;
            }
            return true;
        }
        
        // Get authorization headers for API requests
        function getAuthHeaders() {
            const token = localStorage.getItem('access_token');
            return {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            };
        }
        
        // Logout function
        function logout() {
            // Clear the JWT token from localStorage
            localStorage.removeItem('access_token');
            
            // Clear any other authentication-related data
            localStorage.removeItem('user_profile');
            
            // Stop the refresh interval
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
            
            // Redirect to auth page
            window.location.href = '/auth';
        }
        
        function formatCurrency(amount, currency = 'USD') {
            if (currency === 'USD') {
                return new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: 'USD'
                }).format(amount);
            } else if (currency === 'BTC') {
                return amount.toFixed(8) + ' BTC';
            } else {
                return amount.toFixed(6) + ' ' + currency;
            }
        }
        
        function formatDate(timestamp) {
            return new Date(timestamp * 1000).toLocaleString();
        }
        
        async function fetchData() {
            try {
                const headers = getAuthHeaders();
                const [statusRes, portfolioRes, marketRes, transactionsRes, walletRes, agentRes] = await Promise.all([
                    fetch('/api/status', { headers }),
                    fetch('/api/portfolio', { headers }),
                    fetch('/api/market-data', { headers }),
                    fetch('/api/transactions?limit=10', { headers }),
                    fetch('/api/wallet-address', { headers }),
                    fetch('/api/agent/status', { headers })
                ]);
                
                const status = await statusRes.json();
                const portfolio = await portfolioRes.json();
                const market = await marketRes.json();
                const transactions = await transactionsRes.json();
                const wallet = await walletRes.json();
                const agentStatus = await agentRes.json();
                
                updateAgentStatus(status);
                updatePortfolio(portfolio);
                updateMarketData(market);
                updateTradingStatus(status);
                updateTransactions(transactions);
                updateWalletAddress(wallet);
                
                // Load agent status separately
                await loadAgentStatus();
                
            } catch (error) {
                console.error('Error fetching data:', error);
                showError('Failed to fetch data. Please check your connection.');
            }
        }
        
        function updateAgentStatus(status) {
            const statusDiv = document.getElementById('agent-status');
            
            if (status.error) {
                statusDiv.innerHTML = `<span class="error">Error: ${status.error}</span>`;
                return;
            }
            
            const runningStatus = status.is_running ? 'RUNNING' : 'STOPPED';
            const runningClass = status.is_running ? 'status-running' : 'status-stopped';
            const modeClass = status.dry_run ? 'status-dry-run' : 'status-running';
            const mode = status.dry_run ? 'DRY RUN' : 'LIVE TRADING';
            
            statusDiv.innerHTML = `
                <span class="status-badge ${runningClass}">Agent: ${runningStatus}</span>
                <span class="status-badge ${modeClass}">Mode: ${mode}</span>
                <div style="margin-top: 10px;">
                    <small>Trading Pair: ${status.trading_pair || 'N/A'} | 
                    Daily Trades: ${status.daily_trades || 0}/${status.max_daily_trades || 0}</small>
                </div>
            `;
        }
        
        function updatePortfolio(data) {
            const totalValueDiv = document.getElementById('total-value');
            const portfolioDiv = document.getElementById('portfolio-breakdown');
            
            if (data.error) {
                portfolioDiv.innerHTML = `<div class="error">Error: ${data.error}</div>`;
                return;
            }
            
            totalValueDiv.textContent = formatCurrency(data.total_value_usd || 0);
            
            let portfolioHtml = '';
            if (data.portfolio && Object.keys(data.portfolio).length > 0) {
                for (const [currency, info] of Object.entries(data.portfolio)) {
                    portfolioHtml += `
                        <div class="portfolio-item">
                            <div class="currency-info">
                                <div class="currency-name">${currency}</div>
                                <div class="currency-amount">${formatCurrency(info.amount, currency)}</div>
                            </div>
                            <div class="currency-value">
                                <div class="value-usd">${formatCurrency(info.value_usd)}</div>
                                <div class="value-percentage">${info.percentage.toFixed(1)}%</div>
                            </div>
                        </div>
                    `;
                }
            } else {
                portfolioHtml = '<div class="loading">No portfolio data available</div>';
            }
            
            portfolioDiv.innerHTML = portfolioHtml;
        }
        
        function updateMarketData(data) {
            const marketDiv = document.getElementById('market-data');
            
            if (data.error) {
                marketDiv.innerHTML = `<div class="error">Error: ${data.error}</div>`;
                return;
            }
            
            if (data.price) {
                const priceChange = data.price_change_24h || 0;
                const priceClass = priceChange >= 0 ? 'price-positive' : 'price-negative';
                
                marketDiv.innerHTML = `
                    <div class="metric">
                        <span class="metric-label">BTC Price:</span>
                        <span class="metric-value ${priceClass}">${formatCurrency(data.price)}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">24h Change:</span>
                        <span class="metric-value ${priceClass}">${priceChange.toFixed(2)}%</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Volume:</span>
                        <span class="metric-value">${data.volume_24h ? formatCurrency(data.volume_24h) : 'N/A'}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Last Updated:</span>
                        <span class="metric-value">${new Date().toLocaleTimeString()}</span>
                    </div>
                `;
            } else {
                marketDiv.innerHTML = '<div class="loading">No market data available</div>';
            }
        }
        
        function updateTradingStatus(status) {
            const tradingDiv = document.getElementById('trading-status');
            
            if (status.error) {
                tradingDiv.innerHTML = `<div class="error">Error: ${status.error}</div>`;
                return;
            }
            
            tradingDiv.innerHTML = `
                <div class="metric">
                    <span class="metric-label">Status:</span>
                    <span class="metric-value">${status.is_running ? 'Active' : 'Inactive'}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Mode:</span>
                    <span class="metric-value">${status.dry_run ? 'Simulation' : 'Live Trading'}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Daily Trades:</span>
                    <span class="metric-value">${status.daily_trades || 0}/${status.max_daily_trades || 0}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Trading Pair:</span>
                    <span class="metric-value">${status.trading_pair || 'N/A'}</span>
                </div>
            `;
        }
        
        function updateTransactions(data) {
            const transactionsDiv = document.getElementById('transactions');
            
            if (data.error) {
                transactionsDiv.innerHTML = `<div class="error">Error: ${data.error}</div>`;
                return;
            }
            
            if (data.transactions && data.transactions.length > 0) {
                let tableHtml = `
                    <table class="transactions-table">
                        <thead>
                            <tr>
                                <th>Time</th>
                                <th>Type</th>
                                <th>Amount</th>
                                <th>Value</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                `;
                
                data.transactions.forEach(tx => {
                    const tradeClass = tx.type === 'buy' ? 'trade-buy' : 'trade-sell';
                    tableHtml += `
                        <tr>
                            <td>${formatDate(tx.timestamp)}</td>
                            <td class="${tradeClass}">${tx.type.toUpperCase()}</td>
                            <td>${formatCurrency(parseFloat(tx.amount), tx.currency)}</td>
                            <td>${formatCurrency(parseFloat(tx.fiat_amount))}</td>
                            <td>${tx.status}</td>
                        </tr>
                    `;
                });
                
                tableHtml += '</tbody></table>';
                transactionsDiv.innerHTML = tableHtml;
            } else {
                transactionsDiv.innerHTML = '<div class="loading">No transactions available (DRY RUN mode)</div>';
            }
        }
        
        function showError(message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = message;
            document.body.insertBefore(errorDiv, document.body.firstChild);
            
            setTimeout(() => {
                errorDiv.remove();
            }, 5000);
        }
        
        function refreshData() {
            fetchData();
        }
        
        async function executeTrade(action) {
            const amountInput = document.getElementById('trade-amount');
            const statusDiv = document.getElementById('trade-status');
            const buyBtn = document.querySelector('.btn-buy');
            const sellBtn = document.querySelector('.btn-sell');
            
            const amount = parseFloat(amountInput.value);
            
            if (!amount || amount <= 0) {
                showTradeStatus('Please enter a valid amount', 'error');
                return;
            }
            
            // Disable buttons during trade
            buyBtn.disabled = true;
            sellBtn.disabled = true;
            showTradeStatus(`Processing ${action} order...`, 'loading');
            
            try {
                const response = await fetch('/api/force-trade', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: action,
                        amount: amount
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showTradeStatus(`${action.toUpperCase()} order executed successfully!`, 'success');
                    // Clear the input field
                    amountInput.value = '';
                    // Refresh data to show updated balances
                    setTimeout(fetchData, 1000);
                } else {
                    showTradeStatus(`${action.toUpperCase()} order failed: ${result.message || 'Unknown error'}`, 'error');
                }
                
            } catch (error) {
                console.error('Trade execution error:', error);
                showTradeStatus(`Error executing ${action} order. Please try again.`, 'error');
            } finally {
                // Re-enable buttons
                buyBtn.disabled = false;
                sellBtn.disabled = false;
            }
        }
        
        function updateWalletAddress(data) {
            const addressDiv = document.getElementById('wallet-address');
            
            if (data.error || !data.address) {
                addressDiv.innerHTML = '<span class="error">Error loading address</span>';
                return;
            }
            
            addressDiv.innerHTML = `
                <span onclick="copyToClipboard('${data.address}')" title="Click to copy">
                    ${data.address}
                </span>
            `;
        }
        
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showNotification('Address copied to clipboard!', 'success');
            }).catch(err => {
                console.error('Failed to copy:', err);
                showNotification('Failed to copy address', 'error');
            });
        }
        
        async function sendBitcoin() {
            const toAddress = document.getElementById('send-address').value.trim();
            const amountUsd = document.getElementById('send-amount').value;
            const statusDiv = document.getElementById('send-status');
            
            // Clear previous status
            statusDiv.className = 'trade-status';
            statusDiv.textContent = '';
            
            // Validate inputs
            if (!toAddress) {
                showNotification('Please enter a destination address', 'error');
                return;
            }
            
            if (!amountUsd || parseFloat(amountUsd) <= 0) {
                showNotification('Please enter a valid amount', 'error');
                return;
            }
            
            // Show loading status
            statusDiv.className = 'trade-status loading';
            statusDiv.textContent = 'Sending Bitcoin...';
            
            try {
                const response = await fetch('/api/send-bitcoin', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        to_address: toAddress,
                        amount_usd: parseFloat(amountUsd)
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    statusDiv.className = 'trade-status success';
                    statusDiv.textContent = result.message;
                    
                    // Clear form
                    document.getElementById('send-address').value = '';
                    document.getElementById('send-amount').value = '';
                    
                    showNotification('Bitcoin sent successfully!', 'success');
                    
                    // Refresh data to show updated balance
                    setTimeout(fetchData, 1000);
                } else {
                    statusDiv.className = 'trade-status error';
                    statusDiv.textContent = result.error || 'Failed to send Bitcoin';
                    showNotification(result.error || 'Failed to send Bitcoin', 'error');
                }
                
            } catch (error) {
                console.error('Send error:', error);
                statusDiv.className = 'trade-status error';
                statusDiv.textContent = 'Network error occurred';
                showNotification('Network error occurred', 'error');
            }
        }
        
        function showNotification(message, type) {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px;
                border-radius: 5px;
                color: white;
                font-weight: bold;
                z-index: 1000;
                max-width: 300px;
                ${type === 'success' ? 'background-color: #10b981;' : 'background-color: #ef4444;'}
            `;
            
            document.body.appendChild(notification);
            
            // Remove after 3 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 3000);
        }
        
        function showTradeStatus(message, type) {
            const statusDiv = document.getElementById('trade-status');
            statusDiv.className = `trade-status ${type}`;
            statusDiv.textContent = message;
            
            // Auto-hide success/error messages after 5 seconds
            if (type === 'success' || type === 'error') {
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 5000);
            }
        }
        
        // Flag to track if form has been populated on initial load
        let formPopulated = false;
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication first
            if (!checkAuth()) {
                return; // Will redirect to auth page
            }
            
            fetchData();
            
            // Load initial agent configuration form values
            loadInitialAgentConfiguration();
            
            // Auto-refresh every 30 seconds
            refreshInterval = setInterval(fetchData, 30000);
        });
        
        async function loadInitialAgentConfiguration() {
            try {
                const headers = getAuthHeaders();
                const response = await fetch('/api/agent/status', { headers });
                
                if (response.status === 401) {
                    localStorage.removeItem('access_token');
                    window.location.href = '/auth';
                    return;
                }
                
                const status = await response.json();
                
                if (!status.error && !formPopulated) {
                    populateConfigurationForm(status);
                    formPopulated = true;
                }
            } catch (error) {
                console.error('Error loading initial agent configuration:', error);
            }
        }
        
        function populateConfigurationForm(status) {
            // Update the form fields with current agent configuration
            const nameField = document.getElementById('agent-name');
            const modeField = document.getElementById('agent-mode');
            const riskField = document.getElementById('risk-tolerance');
            const aggressionField = document.getElementById('aggression-level');
            const llmEnabledField = document.getElementById('llm-enabled');
            const llmProviderField = document.getElementById('llm-provider');
            
            if (nameField && status.name) {
                nameField.value = status.name;
            }
            if (modeField && status.mode) {
                modeField.value = status.mode;
            }
            if (riskField && status.risk_tolerance !== undefined) {
                riskField.value = status.risk_tolerance;
                // Update the display value
                const riskValue = document.getElementById('risk-value');
                if (riskValue) riskValue.textContent = status.risk_tolerance;
            }
            if (aggressionField && status.aggression_level !== undefined) {
                aggressionField.value = status.aggression_level;
                // Update the display value
                const aggressionValue = document.getElementById('aggression-value');
                if (aggressionValue) aggressionValue.textContent = status.aggression_level;
            }
            // Load sensitivity setting
            const sensitivityField = document.getElementById('sensitivity');
            if (sensitivityField && status.sensitivity !== undefined) {
                sensitivityField.value = status.sensitivity;
                // Update the display value
                const sensitivityValue = document.getElementById('sensitivity-value');
                if (sensitivityValue) sensitivityValue.textContent = status.sensitivity;
            }
            if (llmEnabledField && status.llm_enabled !== undefined) {
                llmEnabledField.checked = status.llm_enabled;
                toggleLLMProvider(); // Show/hide LLM provider field
            }
            if (llmProviderField && status.llm_provider) {
                llmProviderField.value = status.llm_provider;
            }
        }
        
        // Autonomous Agent Functions
        let agentUpdateInterval;
        
        async function configureAgent() {
            // Security check: prevent configuration changes while agent is running
            try {
                const statusResponse = await fetch('/api/agent/status');
                const agentStatus = await statusResponse.json();
                
                if (agentStatus.is_running) {
                    showNotification('Configuration changes are blocked while agent is running for security', 'error');
                    return;
                }
            } catch (error) {
                console.error('Error checking agent status:', error);
            }
            
            const name = document.getElementById('agent-name').value.trim();
            const mode = document.getElementById('agent-mode').value;
            const riskTolerance = parseFloat(document.getElementById('risk-tolerance').value);
            const aggressionLevel = parseFloat(document.getElementById('aggression-level').value);
            const sensitivity = parseFloat(document.getElementById('sensitivity').value);
            const statusDiv = document.getElementById('agent-config-status');
            
            if (!name) {
                showNotification('Please enter an agent name', 'error');
                return;
            }
            
            statusDiv.className = 'trade-status loading';
            statusDiv.textContent = 'Configuring agent...';
            
            try {
                const response = await fetch('/api/agent/configure', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        name: name,
                        mode: mode,
                        risk_tolerance: riskTolerance,
                        aggression_level: aggressionLevel,
                        sensitivity: sensitivity
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    statusDiv.className = 'trade-status success';
                    statusDiv.textContent = result.message;
                    showNotification('Agent configured successfully', 'success');
                    loadAgentStatus();
                } else {
                    statusDiv.className = 'trade-status error';
                    statusDiv.textContent = result.error || 'Configuration failed';
                    showNotification(result.error || 'Configuration failed', 'error');
                }
            } catch (error) {
                statusDiv.className = 'trade-status error';
                statusDiv.textContent = 'Error configuring agent';
                showNotification('Error configuring agent', 'error');
                console.error('Agent configuration error:', error);
            }
        }
        
        async function startAgent() {
            const statusDiv = document.getElementById('agent-config-status');
            
            statusDiv.className = 'trade-status loading';
            statusDiv.textContent = 'Starting agent...';
            
            try {
                const response = await fetch('/api/agent/start', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    statusDiv.className = 'trade-status success';
                    statusDiv.textContent = result.message;
                    showNotification('Agent started successfully', 'success');
                    
                    document.getElementById('start-agent-btn').style.display = 'none';
                    document.getElementById('stop-agent-btn').style.display = 'inline-block';
                    
                    if (agentUpdateInterval) clearInterval(agentUpdateInterval);
                    agentUpdateInterval = setInterval(loadAgentStatus, 5000);
                    
                    loadAgentStatus();
                } else {
                    statusDiv.className = 'trade-status error';
                    statusDiv.textContent = result.error || 'Failed to start agent';
                    showNotification(result.error || 'Failed to start agent', 'error');
                }
            } catch (error) {
                statusDiv.className = 'trade-status error';
                statusDiv.textContent = 'Error starting agent';
                showNotification('Error starting agent', 'error');
                console.error('Agent start error:', error);
            }
        }
        
        async function stopAgent() {
            const statusDiv = document.getElementById('agent-config-status');
            
            statusDiv.className = 'trade-status loading';
            statusDiv.textContent = 'Stopping agent...';
            
            try {
                const response = await fetch('/api/agent/stop', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    statusDiv.className = 'trade-status success';
                    statusDiv.textContent = result.message;
                    showNotification('Agent stopped successfully', 'success');
                    
                    document.getElementById('start-agent-btn').style.display = 'inline-block';
                    document.getElementById('stop-agent-btn').style.display = 'none';
                    
                    if (agentUpdateInterval) {
                        clearInterval(agentUpdateInterval);
                        agentUpdateInterval = null;
                    }
                    
                    loadAgentStatus();
                } else {
                    statusDiv.className = 'trade-status error';
                    statusDiv.textContent = result.error || 'Failed to stop agent';
                    showNotification(result.error || 'Failed to stop agent', 'error');
                }
            } catch (error) {
                statusDiv.className = 'trade-status error';
                statusDiv.textContent = 'Error stopping agent';
                showNotification('Error stopping agent', 'error');
                console.error('Agent stop error:', error);
            }
        }
        
        async function forceAnalysis() {
            try {
                const response = await fetch('/api/agent/force-analysis', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showNotification('Market analysis completed', 'success');
                    loadAgentStatus();
                } else {
                    showNotification(result.error || 'Analysis failed', 'error');
                }
            } catch (error) {
                showNotification('Error performing analysis', 'error');
                console.error('Force analysis error:', error);
            }
        }
        
        async function loadAgentStatus() {
            try {
                const response = await fetch('/api/agent/status');
                const status = await response.json();
                
                if (status.error) {
                    document.getElementById('agent-status').innerHTML = `<div class="error">Error: ${status.error}</div>`;
                    return;
                }
                
                updateAgentStatus(status);
                await loadAgentDecisions();
                
            } catch (error) {
                console.error('Error loading agent status:', error);
                document.getElementById('agent-status').innerHTML = '<div class="error">Failed to load agent status</div>';
            }
        }
        
        function updateAgentStatus(status) {
            const headerStatusDiv = document.getElementById('agent-status');
            const metricsDiv = document.getElementById('agent-metrics');
            
            const statusClass = status.is_running ? 'agent-status-running' : 'agent-status-stopped';
            const statusText = status.is_running ? 'RUNNING' : 'STOPPED';
            const statusIcon = status.is_running ? '🟢' : '🔴';
            
            // Update header status (simple display)
            headerStatusDiv.innerHTML = `<span class="${statusClass}">${statusIcon} ${statusText}</span>`;
            
            // Update detailed metrics in the agent section
            const agentStatusDiv = document.getElementById('agent-detailed-status');
            if (agentStatusDiv) {
                agentStatusDiv.innerHTML = `
                    <div class="metric">
                        <span class="metric-label">Agent Status:</span>
                        <span class="metric-value ${statusClass}">${statusText}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Agent Name:</span>
                        <span class="metric-value">${status.name || 'Loading...'}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Runtime:</span>
                        <span class="metric-value">${status.runtime || 'N/A'}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Market Sentiment:</span>
                        <span class="metric-value">${status.market_sentiment || 'neutral'}</span>
                    </div>
                `;
            }
            
            metricsDiv.innerHTML = `
                <div class="agent-metric">
                    <div class="agent-metric-value">${status.total_trades || 0}</div>
                    <div class="agent-metric-label">Total Trades</div>
                </div>
                <div class="agent-metric">
                    <div class="agent-metric-value">${status.success_rate || 0}%</div>
                    <div class="agent-metric-label">Success Rate</div>
                </div>
                <div class="agent-metric">
                    <div class="agent-metric-value">${status.btc_accumulated || 0}</div>
                    <div class="agent-metric-label">BTC Accumulated</div>
                </div>
                <div class="agent-metric">
                    <div class="agent-metric-value">$${status.profit_loss_usd || 0}</div>
                    <div class="agent-metric-label">Profit/Loss</div>
                </div>
            `;
            
            if (status.is_running) {
                document.getElementById('start-agent-btn').style.display = 'none';
                document.getElementById('stop-agent-btn').style.display = 'inline-block';
                // Lock configuration form when agent is running for security
                setConfigurationFormLocked(true);
            } else {
                document.getElementById('start-agent-btn').style.display = 'inline-block';
                document.getElementById('stop-agent-btn').style.display = 'none';
                // Unlock configuration form when agent is stopped
                setConfigurationFormLocked(false);
            }
            
        }
        
        function toggleLLMProvider() {
            const llmEnabled = document.getElementById('llm-enabled').checked;
            const llmProviderGroup = document.getElementById('llm-provider-group');
            
            if (llmProviderGroup) {
                llmProviderGroup.style.display = llmEnabled ? 'block' : 'none';
            }
        }
        
        function setConfigurationFormLocked(locked) {
            const configForm = document.getElementById('config-form');
            if (!configForm) return;
            
            // Get all form inputs
            const inputs = configForm.querySelectorAll('input, select, button[type="submit"]');
            
            inputs.forEach(input => {
                input.disabled = locked;
                if (locked) {
                    input.style.opacity = '0.6';
                    input.style.cursor = 'not-allowed';
                } else {
                    input.style.opacity = '1';
                    input.style.cursor = '';
                }
            });
            
            // Show/hide security message
            let securityMessage = document.getElementById('security-lock-message');
            if (locked) {
                if (!securityMessage) {
                    securityMessage = document.createElement('div');
                    securityMessage.id = 'security-lock-message';
                    securityMessage.className = 'security-warning';
                    securityMessage.innerHTML = '🔒 Configuration locked while agent is running for security';
                    configForm.insertBefore(securityMessage, configForm.firstChild);
                }
            } else {
                if (securityMessage) {
                    securityMessage.remove();
                }
            }
        }
        
        async function loadAgentDecisions() {
            const decisionsDiv = document.getElementById('agent-decision-log');
            if (!decisionsDiv) return;
            
            try {
                const response = await fetch('/api/agent/decisions?limit=10');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const text = await response.text();
                let data;
                try {
                    data = JSON.parse(text);
                } catch (parseError) {
                    throw new Error('Invalid JSON response');
                }
                
                if (data.error) {
                    decisionsDiv.innerHTML = `<div class="error">Error: ${data.error}</div>`;
                    return;
                }
                
                if (!data.decisions || data.decisions.length === 0) {
                    decisionsDiv.innerHTML = '<div class="loading">No trading decisions yet...</div>';
                    return;
                }
                
                let decisionsHtml = '';
                for (const decision of data.decisions) {
                    const actionClass = decision.action === 'buy' ? 'decision-buy' : 
                                      decision.action === 'sell' ? 'decision-sell' : 'decision-hold';
                    
                    const timestamp = decision.timestamp || 'Unknown time';
                    const action = (decision.action || 'hold').toUpperCase();
                    const price = parseFloat(decision.price) || 0;
                    const confidence = parseFloat(decision.confidence) || 0;
                    const reasoning = decision.reasoning || 'No reasoning provided';
                    const sentiment = decision.market_sentiment || '';
                    
                    decisionsHtml += `
                        <div class="decision-item ${actionClass}">
                            <div class="decision-time">${timestamp}</div>
                            <div class="decision-details">
                                <strong>LLM Analysis: ${action}</strong> recommendation at $${price.toFixed(2)}
                                (Confidence: ${(confidence * 100).toFixed(1)}%)
                            </div>
                            <div class="decision-details"><em>${reasoning}</em></div>
                            ${sentiment ? `<div class="decision-sentiment">Market Sentiment: ${sentiment}</div>` : ''}
                        </div>
                    `;
                }
                
                decisionsDiv.innerHTML = decisionsHtml;
                
            } catch (error) {
                console.error('Error loading agent decisions:', error);
                decisionsDiv.innerHTML = `<div class="error">Failed to load decisions</div>`;
            }
        }

        // Initialize range sliders
        document.addEventListener('DOMContentLoaded', function() {
            const riskSlider = document.getElementById('risk-tolerance');
            const riskValue = document.getElementById('risk-value');
            const aggressionSlider = document.getElementById('aggression-level');
            const aggressionValue = document.getElementById('aggression-value');
            const sensitivitySlider = document.getElementById('sensitivity');
            const sensitivityValue = document.getElementById('sensitivity-value');
            
            if (riskSlider && riskValue) {
                riskSlider.addEventListener('input', function() {
                    riskValue.textContent = this.value;
                });
            }
            
            if (aggressionSlider && aggressionValue) {
                aggressionSlider.addEventListener('input', function() {
                    aggressionValue.textContent = this.value;
                });
            }
            
            if (sensitivitySlider && sensitivityValue) {
                sensitivitySlider.addEventListener('input', function() {
                    sensitivityValue.textContent = this.value;
                });
            }
            
            // Load agent status on page load
            loadAgentStatus();
            
            // Set up periodic agent updates
            setInterval(loadAgentStatus, 10000);
        });

        // Clean up on page unload
        window.addEventListener('beforeunload', function() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
            if (agentUpdateInterval) {
                clearInterval(agentUpdateInterval);
            }
        });
        
        async function enableTestingMode() {
            const statusDiv = document.getElementById('agent-config-status');
            const testingBtn = document.getElementById('testing-btn');
            
            statusDiv.className = 'trade-status loading';
            statusDiv.textContent = 'Enabling testing mode...';
            testingBtn.disabled = true;
            testingBtn.style.backgroundColor = '#f59e0b';
            testingBtn.textContent = 'Activating...';
            
            try {
                const response = await fetch('/api/agent/enable-testing', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    statusDiv.className = 'trade-status success';
                    statusDiv.textContent = result.message;
                    showNotification('Testing mode enabled - forced buy/sell decisions for 10 minutes', 'success');
                    testingBtn.textContent = 'Testing Active';
                    testingBtn.style.backgroundColor = '#dc2626';
                    
                    // Re-enable button after 10 minutes
                    setTimeout(() => {
                        testingBtn.disabled = false;
                        testingBtn.textContent = 'Enable Testing (10min)';
                        testingBtn.style.backgroundColor = '';
                    }, 600000); // 10 minutes
                } else {
                    statusDiv.className = 'trade-status error';
                    statusDiv.textContent = result.error || 'Testing mode failed';
                    showNotification(result.error || 'Testing mode failed', 'error');
                    testingBtn.disabled = false;
                    testingBtn.textContent = 'Enable Testing (10min)';
                    testingBtn.style.backgroundColor = '';
                }
            } catch (error) {
                statusDiv.className = 'trade-status error';
                statusDiv.textContent = 'Error enabling testing mode';
                showNotification('Error enabling testing mode', 'error');
                testingBtn.disabled = false;
                testingBtn.textContent = 'Enable Testing (10min)';
                testingBtn.style.backgroundColor = '';
                console.error('Testing mode error:', error);
            }
        }

        // Update agent sensitivity setting
        async function updateAgentSensitivity(value) {
            try {
                // Add visual feedback on the slider
                const slider = document.getElementById('sensitivity');
                const valueDisplay = document.getElementById('sensitivity-value');
                
                slider.style.background = 'linear-gradient(90deg, #60a5fa, #3b82f6)';
                valueDisplay.style.background = '#60a5fa';
                valueDisplay.style.color = '#000';
                
                const response = await fetch('/api/agent/configure', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        sensitivity: parseFloat(value)
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    // Success feedback
                    valueDisplay.style.background = '#10b981';
                    setTimeout(() => {
                        slider.style.background = '';
                        valueDisplay.style.background = '#1f2937';
                        valueDisplay.style.color = '#60a5fa';
                    }, 1000);
                } else {
                    // Error feedback
                    valueDisplay.style.background = '#ef4444';
                    setTimeout(() => {
                        slider.style.background = '';
                        valueDisplay.style.background = '#1f2937';
                        valueDisplay.style.color = '#60a5fa';
                    }, 2000);
                    showNotification(result.error || 'Failed to update sensitivity', 'error');
                }
            } catch (error) {
                const valueDisplay = document.getElementById('sensitivity-value');
                valueDisplay.style.background = '#ef4444';
                setTimeout(() => {
                    valueDisplay.style.background = '#1f2937';
                    valueDisplay.style.color = '#60a5fa';
                }, 2000);
                console.error('Sensitivity update error:', error);
            }
        }

        // Interactive Sensitivity Tooltip Functions
        let sensitivityTooltipOpen = false;

        function showSensitivityTooltip() {
            if (sensitivityTooltipOpen) return;
            
            sensitivityTooltipOpen = true;
            
            // Create backdrop if it doesn't exist
            let backdrop = document.getElementById('tooltip-backdrop');
            if (!backdrop) {
                backdrop = document.createElement('div');
                backdrop.id = 'tooltip-backdrop';
                backdrop.className = 'tooltip-backdrop';
                backdrop.onclick = closeSensitivityTooltip;
                document.body.appendChild(backdrop);
            }
            
            // Show backdrop and panel
            backdrop.classList.add('active');
            document.getElementById('sensitivity-panel').classList.add('active');
            
            // Initialize chart demo
            initializePriceDemo();
            
            // Prevent body scroll
            document.body.style.overflow = 'hidden';
        }

        function closeSensitivityTooltip() {
            if (!sensitivityTooltipOpen) return;
            
            sensitivityTooltipOpen = false;
            
            const backdrop = document.getElementById('tooltip-backdrop');
            const panel = document.getElementById('sensitivity-panel');
            
            if (backdrop) backdrop.classList.remove('active');
            if (panel) panel.classList.remove('active');
            
            // Restore body scroll
            document.body.style.overflow = '';
        }

        function updateSensitivityDisplay(value) {
            const numValue = parseFloat(value);
            document.getElementById('sensitivity-value').textContent = value;
            
            // Update current setting display in tooltip
            const currentValueElement = document.getElementById('current-sensitivity-value');
            const currentImpactElement = document.getElementById('current-sensitivity-impact');
            
            if (currentValueElement && currentImpactElement) {
                let category = '';
                let impact = '';
                let trades = '';
                
                if (numValue <= 0.3) {
                    category = 'Very Sensitive';
                    impact = `Triggers on ${(0.03 * numValue * 10).toFixed(2)}-${(0.15 * numValue * 2).toFixed(2)}% price moves`;
                    trades = '8-20 trades per day';
                } else if (numValue <= 0.7) {
                    category = 'Balanced';
                    impact = `Triggers on ${(0.2 * numValue).toFixed(2)}-${(0.35 * numValue).toFixed(2)}% price moves`;
                    trades = '3-8 trades per day';
                } else {
                    category = 'Conservative';
                    impact = `Triggers on ${(0.4 * numValue).toFixed(2)}-${(1.0 * numValue).toFixed(2)}% price moves`;
                    trades = '1-4 trades per day';
                }
                
                currentValueElement.textContent = `${value} (${category})`;
                currentImpactElement.textContent = `${impact} - Expects ~${trades} depending on market volatility`;
            }
            
            // Highlight corresponding level card
            highlightLevelCard(numValue);
        }

        function highlightLevelCard(value) {
            const cards = document.querySelectorAll('.level-card');
            cards.forEach(card => {
                card.style.transform = '';
                card.style.boxShadow = '';
            });
            
            let targetCard = null;
            if (value <= 0.3) {
                targetCard = document.querySelector('.level-card.very-sensitive');
            } else if (value <= 0.7) {
                targetCard = document.querySelector('.level-card.balanced');
            } else {
                targetCard = document.querySelector('.level-card.conservative');
            }
            
            if (targetCard) {
                targetCard.style.transform = 'translateY(-4px) scale(1.02)';
                targetCard.style.boxShadow = '0 12px 25px rgba(96, 165, 250, 0.3)';
            }
        }

        function initializePriceDemo() {
            const chartElement = document.getElementById('demo-chart');
            const indicatorsElement = document.getElementById('trade-indicators');
            
            if (!chartElement || !indicatorsElement) return;
            
            // Clear existing content
            indicatorsElement.innerHTML = '';
            
            // Add trade point indicators based on current sensitivity
            const sensitivity = parseFloat(document.getElementById('sensitivity').value);
            const tradePoints = generateTradePoints(sensitivity);
            
            tradePoints.forEach((point, index) => {
                const indicator = document.createElement('div');
                indicator.style.cssText = `
                    position: absolute;
                    width: 8px;
                    height: 8px;
                    background: ${point.type === 'buy' ? '#10b981' : '#ef4444'};
                    border-radius: 50%;
                    left: ${point.position}%;
                    top: ${point.height}px;
                    animation: tradePoint 2s ease-in-out infinite;
                    animation-delay: ${index * 0.3}s;
                `;
                indicatorsElement.appendChild(indicator);
            });
        }

        function generateTradePoints(sensitivity) {
            const points = [];
            
            // Generate fewer points for higher sensitivity values (more conservative)
            const numPoints = Math.max(2, Math.floor(8 / sensitivity));
            
            for (let i = 0; i < numPoints; i++) {
                points.push({
                    type: Math.random() > 0.5 ? 'buy' : 'sell',
                    position: (i + 1) * (80 / (numPoints + 1)) + 10,
                    height: Math.random() * 60 + 10
                });
            }
            
            return points;
        }

        // Add click event to sensitivity tooltip icon
        document.addEventListener('DOMContentLoaded', function() {
            const tooltipIcon = document.querySelector('#sensitivity-tooltip .animated-icon');
            if (tooltipIcon) {
                tooltipIcon.addEventListener('click', showSensitivityTooltip);
            }
            
            // Add level card click handlers
            document.querySelectorAll('.level-card').forEach(card => {
                card.addEventListener('click', function() {
                    const level = parseFloat(this.getAttribute('data-level'));
                    if (level) {
                        document.getElementById('sensitivity').value = level;
                        updateSensitivityDisplay(level.toString());
                        
                        // Update the actual sensitivity setting
                        updateAgentSensitivity(level);
                    }
                });
            });
        });

        // Add custom CSS animation for trade points
        const style = document.createElement('style');
        style.textContent = `
            @keyframes tradePoint {
                0%, 100% { transform: scale(1); opacity: 0.8; }
                50% { transform: scale(1.3); opacity: 1; }
            }
        `;
        document.head.appendChild(style);

        // LLM Provider Management Functions
        function toggleLLMProvider() {
            const llmEnabled = document.getElementById('llm-enabled').checked;
            const providerGroup = document.getElementById('llm-provider-group');
            const customEndpointGroup = document.getElementById('custom-endpoint-group');
            
            if (llmEnabled) {
                providerGroup.style.display = 'block';
                // Set OpenAI as default when first enabled
                const providerSelect = document.getElementById('llm-provider');
                if (!providerSelect.value) {
                    providerSelect.value = 'openai';
                }
                handleProviderChange();
            } else {
                providerGroup.style.display = 'none';
                customEndpointGroup.style.display = 'none';
            }
        }

        function handleProviderChange() {
            const provider = document.getElementById('llm-provider').value;
            const customEndpointGroup = document.getElementById('custom-endpoint-group');
            
            if (provider === 'custom') {
                customEndpointGroup.style.display = 'block';
            } else {
                customEndpointGroup.style.display = 'none';
            }
            
            // Check if API key is needed for the selected provider
            checkAPIKeyRequirement(provider);
        }

        async function checkAPIKeyRequirement(provider) {
            if (!provider || provider === '') return;
            
            try {
                const response = await fetch('/api/check-api-key', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ provider: provider })
                });
                
                const result = await response.json();
                
                if (!result.has_key) {
                    await promptForAPIKey(provider);
                }
            } catch (error) {
                console.error('Error checking API key:', error);
            }
        }

        async function promptForAPIKey(provider) {
            let providerName, keyName, description;
            
            switch (provider) {
                case 'openai':
                    providerName = 'OpenAI';
                    keyName = 'OPENAI_API_KEY';
                    description = 'OpenAI API key for GPT-4o model access. Get your key from https://platform.openai.com/api-keys';
                    break;
                case 'deepseek':
                    providerName = 'DeepSeek';
                    keyName = 'DEEPSEEK_API_KEY';
                    description = 'DeepSeek API key for R1 reasoning model. Get your key from https://platform.deepseek.com';
                    break;
                case 'fingpt':
                    providerName = 'FinGPT';
                    keyName = 'FINGPT_API_KEY';
                    description = 'FinGPT API key for financial analysis. Contact your FinGPT provider for access';
                    break;
                case 'bloomberg':
                    providerName = 'Bloomberg GPT';
                    keyName = 'BLOOMBERG_API_KEY';
                    description = 'Bloomberg Terminal API key for institutional trading insights';
                    break;
                case 'custom':
                    providerName = 'Custom Model';
                    keyName = 'CUSTOM_API_KEY';
                    description = 'API key for your custom model endpoint';
                    break;
                default:
                    return;
            }

            const apiKey = prompt(`${providerName} API Key Required\n\n${description}\n\nPlease enter your ${keyName}:`);
            
            if (apiKey && apiKey.trim()) {
                try {
                    const response = await fetch('/api/save-api-key', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ 
                            provider: provider,
                            key_name: keyName,
                            api_key: apiKey.trim()
                        })
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        showNotification(`${providerName} API key saved successfully`, 'success');
                    } else {
                        showNotification(`Failed to save ${providerName} API key: ${result.error}`, 'error');
                        // Reset to OpenAI default if custom key fails
                        document.getElementById('llm-provider').value = 'openai';
                        handleProviderChange();
                    }
                } catch (error) {
                    showNotification(`Error saving ${providerName} API key`, 'error');
                    console.error('API key save error:', error);
                    // Reset to OpenAI default if error occurs
                    document.getElementById('llm-provider').value = 'openai';
                    handleProviderChange();
                }
            } else {
                // User cancelled or entered empty key, reset to OpenAI default
                showNotification('API key required for selected provider. Defaulting to OpenAI.', 'warning');
                document.getElementById('llm-provider').value = 'openai';
                handleProviderChange();
            }
        }
    </script>
</body>
</html>