<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Permissions-Policy" content="publickey-credentials-get=*, publickey-credentials-create=*, cross-origin-isolated=*">
    <title>EZ Money Trading - Authentication</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .auth-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 100%;
            max-width: 900px;
            min-height: 600px;
            display: flex;
        }
        
        .auth-left {
            flex: 1;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            padding: 60px 40px;
            color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .auth-right {
            flex: 1;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .logo {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 20px;
        }
        
        .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
            line-height: 1.6;
        }
        
        .auth-tabs {
            display: flex;
            margin-bottom: 30px;
        }
        
        .tab-btn {
            flex: 1;
            padding: 12px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            color: #666;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .tab-btn.active {
            color: #667eea;
            border-bottom-color: #667eea;
        }
        
        .auth-form {
            display: none;
        }
        
        .auth-form.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            width: 100%;
            padding: 14px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-google {
            background: #db4437;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        .btn-google:hover {
            background: #c23321;
        }
        
        .btn-facebook {
            background: #3b5998;
            color: white;
        }
        
        .btn-facebook:hover {
            background: #2d4373;
        }
        
        .btn-biometric {
            background: #28a745;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        .btn-biometric:hover {
            background: #218838;
        }
        
        .divider {
            text-align: center;
            margin: 20px 0;
            position: relative;
            color: #666;
        }
        
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e1e5e9;
            z-index: 1;
        }
        
        .divider span {
            background: white;
            padding: 0 15px;
            position: relative;
            z-index: 2;
        }
        
        .mfa-section {
            display: none;
            margin-top: 20px;
        }
        
        .mfa-section.show {
            display: block;
        }
        
        .qr-code {
            text-align: center;
            margin: 20px 0;
        }
        
        .qr-code img {
            max-width: 200px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }
        
        .loading {
            opacity: 0.7;
            pointer-events: none;
        }
        
        @media (max-width: 768px) {
            .auth-container {
                flex-direction: column;
                margin: 20px;
            }
            
            .auth-left {
                padding: 40px 30px;
            }
            
            .auth-right {
                padding: 40px 30px;
            }
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-left">
            <div class="logo">💰 EZ Money</div>
            <div class="subtitle">
                Advanced AI-powered cryptocurrency trading platform with enterprise-grade security.
                <br><br>
                • Multi-factor authentication
                • Biometric security
                • Real-time trading insights
                • Professional-grade analytics
            </div>
        </div>
        
        <div class="auth-right">
            <div class="auth-tabs">
                <button class="tab-btn active" onclick="switchTab('login')">Sign In</button>
                <button class="tab-btn" onclick="switchTab('register')">Sign Up</button>
            </div>
            
            <div class="error-message" id="errorMessage"></div>
            <div class="success-message" id="successMessage"></div>
            
            <!-- Login Form -->
            <form class="auth-form active" id="loginForm">
                <div class="form-group">
                    <label class="form-label">Email Address</label>
                    <input type="email" class="form-input" name="email" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Password</label>
                    <input type="password" class="form-input" name="password" required>
                </div>
                
                <div class="mfa-section" id="mfaSection">
                    <div class="form-group">
                        <label class="form-label">Authentication Code</label>
                        <input type="text" class="form-input" name="mfa_token" placeholder="Enter 6-digit code">
                    </div>
                </div>
                
                <button type="submit" class="btn btn-primary">Sign In</button>
                
                <div style="text-align: center; margin: 15px 0;">
                    <a href="#" onclick="showForgotPassword()" style="color: #666; font-size: 14px; text-decoration: none;">Forgot your password?</a>
                </div>
                
                <div class="divider"><span>or continue with</span></div>
                
                <button type="button" class="btn btn-google" onclick="loginWithGoogle()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    Google
                </button>
                
                <button type="button" class="btn btn-facebook" onclick="loginWithFacebook()">Facebook</button>
                
                <button type="button" class="btn btn-biometric" onclick="loginWithBiometric()" id="biometricBtn">
                    🔐 Face/Fingerprint
                </button>
            </form>
            
            <!-- Passkey Registration Section (shown after login) -->
            <div class="auth-form" id="passkeySetup" style="display: none;">
                <h3 style="text-align: center; margin-bottom: 20px; color: #333;">Set Up Biometric Login</h3>
                <p style="text-align: center; color: #666; margin-bottom: 20px; font-size: 14px;">
                    Enable Face ID, Touch ID, or fingerprint login for faster and more secure access.
                </p>
                
                <button type="button" class="btn btn-primary" onclick="registerBiometric()" id="registerPasskeyBtn">
                    🔐 Set Up Face/Fingerprint Login
                </button>
                
                <button type="button" class="btn btn-secondary" onclick="skipPasskeySetup()" style="margin-top: 10px;">
                    Skip for Now
                </button>
            </div>
            
            <!-- Register Form -->
            <form class="auth-form" id="registerForm">
                <div class="form-group">
                    <label class="form-label">First Name</label>
                    <input type="text" class="form-input" name="first_name" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Last Name</label>
                    <input type="text" class="form-input" name="last_name" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Email Address</label>
                    <input type="email" class="form-input" name="email" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Password</label>
                    <input type="password" class="form-input" name="password" required>
                    <div class="password-requirements">
                        <small style="color: #666; font-size: 12px; margin-top: 4px; display: block;">
                            Password requirements:
                            <br>• Minimum 8 characters
                            <br>• At least one uppercase letter (A-Z)
                            <br>• At least one lowercase letter (a-z)
                            <br>• At least one number (0-9)
                            <br>• At least one special character (!@#$%^&*()_+-=[]{}|;:,.<>?)
                        </small>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Confirm Password</label>
                    <input type="password" class="form-input" name="confirm_password" required>
                </div>
                
                <button type="submit" class="btn btn-primary">Create Account</button>
                
                <div class="divider"><span>or sign up with</span></div>
                
                <button type="button" class="btn btn-google" onclick="loginWithGoogle()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    Google
                </button>
                
                <button type="button" class="btn btn-facebook" onclick="loginWithFacebook()">Facebook</button>
            </form>
        </div>
    </div>

    <script>
        let currentUser = null;
        let pendingMFA = false;

        // Tab switching
        function switchTab(tab) {
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.auth-form').forEach(form => form.classList.remove('active'));
            
            event.target.classList.add('active');
            document.getElementById(tab + 'Form').classList.add('active');
            
            clearMessages();
        }

        // Message handling
        function showError(message) {
            const errorEl = document.getElementById('errorMessage');
            errorEl.textContent = message;
            errorEl.style.display = 'block';
            document.getElementById('successMessage').style.display = 'none';
        }

        function showSuccess(message) {
            const successEl = document.getElementById('successMessage');
            successEl.textContent = message;
            successEl.style.display = 'block';
            document.getElementById('errorMessage').style.display = 'none';
        }

        function clearMessages() {
            document.getElementById('errorMessage').style.display = 'none';
            document.getElementById('successMessage').style.display = 'none';
        }

        // Login form handler
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            clearMessages();
            
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData);
            
            try {
                const response = await fetch('/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (result.mfa_required) {
                    document.getElementById('mfaSection').classList.add('show');
                    showError('Please enter your authentication code');
                    pendingMFA = true;
                    return;
                }
                
                if (result.success) {
                    localStorage.setItem('access_token', result.access_token);
                    localStorage.setItem('user', JSON.stringify(result.user));
                    
                    // Check if user has WebAuthn credentials already
                    checkAndShowPasskeySetup(result.user);
                } else {
                    showError(result.error || 'Login failed');
                }
            } catch (error) {
                showError('Network error. Please try again.');
            }
        });

        // Register form handler
        document.getElementById('registerForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            clearMessages();
            
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData);
            
            if (data.password !== data.confirm_password) {
                showError('Passwords do not match');
                return;
            }
            
            try {
                const response = await fetch('/auth/register', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    localStorage.setItem('access_token', result.access_token);
                    localStorage.setItem('user', JSON.stringify(result.user));
                    showSuccess('Registration successful! Redirecting...');
                    setTimeout(() => window.location.href = '/', 1500);
                } else {
                    showError(result.error || 'Registration failed');
                }
            } catch (error) {
                showError('Network error. Please try again.');
            }
        });

        // OAuth login functions
        function loginWithGoogle() {
            window.location.href = '/auth/google';
        }

        function loginWithFacebook() {
            showError('Facebook login coming soon');
        }

        // WebAuthn biometric authentication
        async function loginWithBiometric() {
            if (!window.PublicKeyCredential) {
                showError('Biometric authentication not supported in this browser');
                return;
            }
            
            try {
                // Check if biometric is available
                const available = await PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable();
                if (!available) {
                    showError('Biometric authentication not available on this device');
                    return;
                }
                
                showError('Biometric authentication setup coming soon');
            } catch (error) {
                showError('Biometric authentication failed');
            }
        }

        // Forgot password functionality
        function showForgotPassword() {
            const email = prompt('Enter your email address to reset your password:');
            if (!email) return;
            
            fetch('/auth/forgot-password', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ email: email })
            })
            .then(response => response.json())
            .then(result => {
                if (result.reset_token) {
                    // For testing - show the reset token
                    const newPassword = prompt(`Reset token received. Enter your new password:\n(Reset token: ${result.reset_token})`);
                    if (newPassword) {
                        resetPassword(result.reset_token, newPassword);
                    }
                } else {
                    showSuccess(result.message);
                }
            })
            .catch(error => {
                showError('Failed to send reset email. Please try again.');
            });
        }

        function resetPassword(resetToken, newPassword) {
            fetch('/auth/reset-password', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ 
                    reset_token: resetToken,
                    new_password: newPassword 
                })
            })
            .then(response => response.json())
            .then(result => {
                if (result.message) {
                    showSuccess(result.message + ' You can now login with your new password.');
                } else {
                    showError(result.error || 'Password reset failed');
                }
            })
            .catch(error => {
                showError('Password reset failed. Please try again.');
            });
        }

        // Check if user is already logged in
        window.addEventListener('load', () => {
            const token = localStorage.getItem('access_token');
            if (token) {
                // Verify token with server
                fetch('/auth/profile', {
                    headers: { 'Authorization': `Bearer ${token}` }
                })
                .then(response => response.json())
                .then(result => {
                    if (result.profile) {
                        window.location.href = '/';
                    }
                })
                .catch(() => {
                    // Token invalid, remove it
                    localStorage.removeItem('access_token');
                    localStorage.removeItem('user');
                });
            }
        });

        // WebAuthn/Biometric Authentication Functions
        async function loginWithBiometric() {
            const biometricBtn = document.getElementById('biometricBtn');
            const originalText = biometricBtn.innerHTML;
            
            try {
                // Check if WebAuthn is supported
                if (!window.PublicKeyCredential) {
                    showError('Biometric authentication is not supported on this device/browser.');
                    return;
                }

                biometricBtn.innerHTML = '🔄 Authenticating...';
                biometricBtn.disabled = true;

                // Start authentication
                const response = await fetch('/auth/webauthn/authenticate/start', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({})
                });

                const authOptions = await response.json();

                if (!authOptions.success) {
                    throw new Error(authOptions.error || 'Failed to start authentication');
                }

                // Convert challenge and allowCredentials
                const publicKeyOptions = authOptions.options.publicKey;
                publicKeyOptions.challenge = Uint8Array.from(atob(publicKeyOptions.challenge), c => c.charCodeAt(0));
                
                if (publicKeyOptions.allowCredentials) {
                    publicKeyOptions.allowCredentials = publicKeyOptions.allowCredentials.map(cred => ({
                        ...cred,
                        id: Uint8Array.from(atob(cred.id), c => c.charCodeAt(0))
                    }));
                }

                // Get credential from authenticator
                const credential = await navigator.credentials.get({
                    publicKey: publicKeyOptions
                });

                if (!credential) {
                    throw new Error('Authentication was cancelled');
                }

                // Convert credential response for server
                const credentialData = {
                    id: credential.id,
                    rawId: btoa(String.fromCharCode(...new Uint8Array(credential.rawId))),
                    type: credential.type,
                    response: {
                        authenticatorData: btoa(String.fromCharCode(...new Uint8Array(credential.response.authenticatorData))),
                        clientDataJSON: btoa(String.fromCharCode(...new Uint8Array(credential.response.clientDataJSON))),
                        signature: btoa(String.fromCharCode(...new Uint8Array(credential.response.signature))),
                        userHandle: credential.response.userHandle ? btoa(String.fromCharCode(...new Uint8Array(credential.response.userHandle))) : null
                    }
                };

                // Complete authentication
                const authResponse = await fetch('/auth/webauthn/authenticate/complete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        credential: credentialData
                    })
                });

                const authResult = await authResponse.json();

                if (authResult.success) {
                    // Store token and user data
                    localStorage.setItem('access_token', authResult.access_token);
                    localStorage.setItem('user', JSON.stringify(authResult.user));
                    
                    showSuccess('Biometric authentication successful! Redirecting...');
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 1000);
                } else {
                    throw new Error(authResult.error || 'Authentication failed');
                }

            } catch (error) {
                console.error('Biometric authentication error:', error);
                showError('Biometric authentication failed: ' + error.message);
            } finally {
                biometricBtn.innerHTML = originalText;
                biometricBtn.disabled = false;
            }
        }

        // Register biometric authentication for logged-in users
        async function registerBiometric() {
            try {
                if (!window.PublicKeyCredential) {
                    showError('Biometric authentication is not supported on this device/browser.');
                    return;
                }

                const token = localStorage.getItem('access_token');
                if (!token) {
                    showError('Please log in first to register biometric authentication.');
                    return;
                }

                // Start registration
                const response = await fetch('/auth/webauthn/register/start', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + token
                    },
                    body: JSON.stringify({
                        authenticator_type: 'platform'
                    })
                });

                const regOptions = await response.json();

                if (!regOptions.success) {
                    throw new Error(regOptions.error || 'Failed to start registration');
                }

                // Convert challenge and excludeCredentials
                const publicKeyOptions = regOptions.options.publicKey;
                publicKeyOptions.challenge = Uint8Array.from(atob(publicKeyOptions.challenge), c => c.charCodeAt(0));
                publicKeyOptions.user.id = Uint8Array.from(atob(publicKeyOptions.user.id), c => c.charCodeAt(0));
                
                if (publicKeyOptions.excludeCredentials) {
                    publicKeyOptions.excludeCredentials = publicKeyOptions.excludeCredentials.map(cred => ({
                        ...cred,
                        id: Uint8Array.from(atob(cred.id), c => c.charCodeAt(0))
                    }));
                }

                // Create credential
                const credential = await navigator.credentials.create({
                    publicKey: publicKeyOptions
                });

                if (!credential) {
                    throw new Error('Registration was cancelled');
                }

                // Convert credential response for server
                const credentialData = {
                    id: credential.id,
                    rawId: btoa(String.fromCharCode(...new Uint8Array(credential.rawId))),
                    type: credential.type,
                    response: {
                        attestationObject: btoa(String.fromCharCode(...new Uint8Array(credential.response.attestationObject))),
                        clientDataJSON: btoa(String.fromCharCode(...new Uint8Array(credential.response.clientDataJSON)))
                    }
                };

                // Complete registration
                const regResponse = await fetch('/auth/webauthn/register/complete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + token
                    },
                    body: JSON.stringify({
                        credential: credentialData,
                        name: 'Biometric Key'
                    })
                });

                const regResult = await regResponse.json();

                if (regResult.success) {
                    showSuccess('Biometric authentication registered successfully!');
                } else {
                    throw new Error(regResult.error || 'Registration failed');
                }

            } catch (error) {
                console.error('Biometric registration error:', error);
                showError('Biometric registration failed: ' + error.message);
            }
        }

        // Check if biometric authentication is available
        async function checkBiometricSupport() {
            const biometricBtn = document.getElementById('biometricBtn');
            
            if (!window.PublicKeyCredential) {
                biometricBtn.style.display = 'none';
                return;
            }

            // Check if platform authenticator is available
            try {
                const available = await PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable();
                if (!available) {
                    biometricBtn.style.display = 'none';
                }
            } catch (error) {
                console.warn('Could not check biometric availability:', error);
            }
        }

        // Initialize biometric support check
        checkBiometricSupport();

        // Register biometric authentication
        async function registerBiometric() {
            const btn = document.getElementById('registerPasskeyBtn');
            const originalText = btn.textContent;
            
            try {
                btn.textContent = 'Setting up...';
                btn.disabled = true;
                
                // Start WebAuthn registration
                const response = await fetch('/auth/webauthn/register/start', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        name: 'My Device' // Default name, could be made customizable
                    })
                });
                
                if (!response.ok) {
                    throw new Error('Failed to start registration');
                }
                
                const options = await response.json();
                
                // Convert base64 to ArrayBuffer for WebAuthn
                options.challenge = base64ToArrayBuffer(options.challenge);
                options.user.id = base64ToArrayBuffer(options.user.id);
                
                // Create credentials
                const credential = await navigator.credentials.create({
                    publicKey: options
                });
                
                // Complete registration
                const completeResponse = await fetch('/auth/webauthn/register/complete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        credential_id: arrayBufferToBase64(credential.rawId),
                        client_data_json: arrayBufferToBase64(credential.response.clientDataJSON),
                        attestation_object: arrayBufferToBase64(credential.response.attestationObject),
                        name: 'My Device'
                    })
                });
                
                if (completeResponse.ok) {
                    showMessage('Biometric authentication set up successfully! You can now use Face ID, Touch ID, or fingerprint to sign in.', 'success');
                    
                    // Redirect to dashboard after successful setup
                    setTimeout(() => {
                        window.location.href = '/dashboard';
                    }, 2000);
                } else {
                    const error = await completeResponse.json();
                    throw new Error(error.error || 'Registration failed');
                }
                
            } catch (error) {
                console.error('Passkey registration error:', error);
                showMessage('Failed to set up biometric authentication: ' + error.message, 'error');
            } finally {
                btn.textContent = originalText;
                btn.disabled = false;
            }
        }
        
        // Skip passkey setup and go to dashboard
        function skipPasskeySetup() {
            window.location.href = '/dashboard';
        }

        // Check if user needs passkey setup
        async function checkAndShowPasskeySetup(user) {
            try {
                // Check if user already has WebAuthn credentials
                const response = await fetch('/auth/webauthn/credentials', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + localStorage.getItem('access_token')
                    }
                });
                
                if (response.ok) {
                    const credentials = await response.json();
                    
                    // If user has no passkeys and biometric is available, show setup
                    if (credentials.credentials && credentials.credentials.length === 0) {
                        if (window.PublicKeyCredential) {
                            const available = await PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable();
                            if (available) {
                                // Hide login forms and show passkey setup
                                document.getElementById('loginForm').style.display = 'none';
                                document.getElementById('registerForm').style.display = 'none';
                                document.getElementById('authTabs').style.display = 'none';
                                document.getElementById('passkeySetup').style.display = 'block';
                                showSuccess('Login successful! Set up biometric authentication for faster access.');
                                return;
                            }
                        }
                    }
                }
                
                // Default: redirect to dashboard
                showSuccess('Login successful! Redirecting...');
                setTimeout(() => window.location.href = '/dashboard', 1500);
                
            } catch (error) {
                console.error('Error checking credentials:', error);
                // Default: redirect to dashboard
                showSuccess('Login successful! Redirecting...');
                setTimeout(() => window.location.href = '/dashboard', 1500);
            }
        }
    </script>
</body>
</html>