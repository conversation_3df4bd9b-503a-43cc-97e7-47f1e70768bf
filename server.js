require('dotenv').config();
const express = require('express');
const session = require('express-session');
const bodyParser = require('body-parser');
const passport = require('passport');
const LocalStrategy = require('passport-local').Strategy;
// const GoogleStrategy = require('passport-google-oauth20').Strategy; // Moved to google_login.js
const GoogleLogin = require('./google_login');
// const FacebookStrategy = require('passport-facebook').Strategy; // Moved to facebook_login.js
const FacebookLogin = require('./facebook_login');
const AppleStrategy = require('passport-apple');
// const TwitterStrategy = require('passport-twitter-oauth2'); // Moved to twitter_login.js
const TwitterLogin = require('./twitter_login');
const BiometricLogin = require('./biometric_login');
const TradingIntegration = require('./trading_integration');
const bcrypt = require('bcrypt');
const nodemailer = require('nodemailer');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const app = express();
const db = new sqlite3.Database('users.db');

app.use(bodyParser.urlencoded({ extended: true }));
app.use(bodyParser.json());

// Session configuration with proper settings
app.use(session({
  secret: 'secret-key',
  resave: false,
  saveUninitialized: true, // Changed to true to ensure session is created
  cookie: {
    secure: false, // Set to false for HTTP (true for HTTPS)
    maxAge: 24 * 60 * 60 * 1000 // 24 hours
  }
}));

// Debug middleware to check session (commented out for production)
// app.use((req, res, next) => {
//   console.log('Session ID:', req.sessionID);
//   console.log('Session exists:', !!req.session);
//   next();
// });

app.use(passport.initialize());
app.use(passport.session());

// Setup database
db.serialize(() => {
  db.run(`CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    email TEXT UNIQUE,
    password TEXT,
    provider TEXT,
    provider_id TEXT,
    name TEXT,
    reset_token TEXT,
    reset_expiry INTEGER
  )`);
});

// Fixed serializeUser with proper error handling
passport.serializeUser((user, done) => {
  console.log('SerializeUser called with user:', user);
  try {
    if (!user) {
      console.error('SerializeUser error: User is null or undefined');
      return done(null, false);
    }
    if (!user.id) {
      console.error('SerializeUser error: User missing id property', user);
      return done(null, false);
    }
    console.log('SerializeUser success: storing user id', user.id);
    done(null, user.id);
  } catch (error) {
    console.error('SerializeUser exception:', error);
    done(error);
  }
});

passport.deserializeUser((id, done) => {
  console.log('DeserializeUser called with id:', id);
  if (!id) {
    console.error('DeserializeUser error: id is null or undefined');
    return done(null, false);
  }
  db.get('SELECT * FROM users WHERE id = ?', id, (err, row) => {
    if (err) {
      console.error('DeserializeUser database error:', err);
      return done(err);
    }
    if (!row) {
      console.error('DeserializeUser: user not found for id:', id);
      return done(null, false);
    }
    console.log('DeserializeUser success: found user', row.id);
    done(null, row);
  });
});

// Local strategy
passport.use(new LocalStrategy({ usernameField: 'email' }, (email, password, done) => {
  console.log('Local strategy called with email:', email);
  db.get('SELECT * FROM users WHERE email = ?', email, async (err, row) => {
    if (err) {
      console.error('Local strategy database error:', err);
      return done(err);
    }
    if (!row) {
      console.log('Local strategy: user not found for email:', email);
      return done(null, false, { message: 'Incorrect email.' });
    }
    console.log('Local strategy: found user:', row.id);
    const match = await bcrypt.compare(password, row.password);
    if (!match) {
      console.log('Local strategy: password mismatch for user:', row.id);
      return done(null, false, { message: 'Incorrect password.' });
    }
    console.log('Local strategy: authentication successful for user:', row.id);
    return done(null, row);
  });
}));

// Initialize Google OAuth module
const googleLogin = new GoogleLogin(passport, db);
googleLogin.initializeStrategy();

// Initialize Facebook OAuth module
const facebookLogin = new FacebookLogin(passport, db);
facebookLogin.initializeStrategy();

// Initialize Twitter OAuth module
const twitterLogin = new TwitterLogin(passport, db);
twitterLogin.initializeStrategy();

// Initialize Biometric/Passkey authentication
const biometricLogin = new BiometricLogin(passport, db);
biometricLogin.initializeDatabase();

// Initialize Trading Integration
const tradingIntegration = new TradingIntegration();
tradingIntegration.initialize().catch(console.error);

// Fixed OAuth strategy with proper error handling
function setupOAuthStrategy(Strategy, name, options) {
  passport.use(new Strategy(options, (accessToken, refreshToken, profile, done) => {
    const providerId = profile.id;
    db.get('SELECT * FROM users WHERE provider = ? AND provider_id = ?', [name, providerId], (err, row) => {
      if (err) return done(err);
      if (row) return done(null, row);
      
      // create new user
      const email = profile.emails && profile.emails[0] && profile.emails[0].value;
      db.run('INSERT INTO users (email, provider, provider_id) VALUES (?, ?, ?)', [email, name, providerId], function(err) {
        if (err) return done(err);
        
        // Fetch the newly created user with proper error handling
        db.get('SELECT * FROM users WHERE id = ?', this.lastID, (e, r) => {
          if (e) return done(e);
          if (!r) {
            console.error('Failed to create user - user not found after insertion');
            return done(null, false);
          }
          done(null, r);
        });
      });
    });
  }));
}

// Google OAuth setup moved to google_login.js module
// Facebook OAuth setup moved to facebook_login.js module

setupOAuthStrategy(AppleStrategy, 'apple', {
  clientID: 'APPLE_CLIENT_ID',
  teamID: 'APPLE_TEAM_ID',
  keyID: 'APPLE_KEY_ID',
  privateKeyLocation: 'path/to/key.p8',
  callbackURL: '/auth/apple/callback'
});

// Twitter OAuth setup moved to twitter_login.js module

// Nodemailer transporter placeholder
const transporter = nodemailer.createTransport({
  host: 'localhost',
  port: 25,
  secure: false,
  tls: { rejectUnauthorized: false }
});

app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

app.get('/', (req, res) => {
  res.render('index', { user: req.user });
});

// Dashboard for managing biometric credentials
app.get('/dashboard', (req, res) => {
  if (!req.isAuthenticated()) {
    return res.redirect('/login');
  }
  res.render('dashboard', { user: req.user });
});

// Configuration status endpoint (for debugging)
app.get('/config-status', (req, res) => {
  const status = {
    google: googleLogin.getConfigStatus(),
    facebook: facebookLogin.getConfigStatus(),
    twitter: twitterLogin.getConfigStatus(),
    server: {
      port: process.env.PORT || 3000,
      environment: process.env.NODE_ENV || 'development'
    }
  };
  res.json(status);
});

app.get('/login', (req, res) => {
  res.render('login');
});

app.post('/login', passport.authenticate('local', {
  successRedirect: '/trading',
  failureRedirect: '/login'
}));

app.get('/register', (req, res) => {
  res.render('register');
});

app.post('/register', async (req, res) => {
  const { email, password } = req.body;
  const hashed = await bcrypt.hash(password, 10);
  db.run('INSERT INTO users (email, password) VALUES (?, ?)', [email, hashed], err => {
    if (err) return res.send('Error');
    res.redirect('/login?registered=true');
  });
});

app.get('/logout', (req, res) => {
  req.logout(() => {
    res.redirect('/');
  });
});

app.get('/forgot', (req, res) => {
  res.render('forgot');
});

app.post('/forgot', (req, res) => {
  const { email } = req.body;
  db.get('SELECT * FROM users WHERE email = ?', email, (err, row) => {
    if (err) return res.send('Database error');
    if (!row) return res.send('No account');
    const token = Math.random().toString(36).substring(2);
    const expiry = Date.now() + 3600000;
    db.run('UPDATE users SET reset_token = ?, reset_expiry = ? WHERE email = ?', [token, expiry, email]);
    const link = `http://localhost:3000/reset/${token}`;
    transporter.sendMail({
      from: '<EMAIL>',
      to: email,
      subject: 'Password reset',
      text: `Reset link: ${link}`
    }, () => {
      console.log('Password reset link:', link);
      res.send('Check your email for reset instructions');
    });
  });
});

app.get('/reset/:token', (req, res) => {
  res.render('reset', { token: req.params.token });
});

app.post('/reset/:token', async (req, res) => {
  const { token } = req.params;
  const { password } = req.body;
  db.get('SELECT * FROM users WHERE reset_token = ? AND reset_expiry > ?', [token, Date.now()], async (err, row) => {
    if (err) return res.send('Database error');
    if (!row) return res.send('Invalid or expired token');
    const hashed = await bcrypt.hash(password, 10);
    db.run('UPDATE users SET password = ?, reset_token = NULL, reset_expiry = NULL WHERE id = ?', [hashed, row.id], err => {
      if (err) return res.send('Error');
      res.redirect('/login');
    });
  });
});

function authRoute(provider) {
  app.get(`/auth/${provider}`, passport.authenticate(provider));
  app.get(`/auth/${provider}/callback`, passport.authenticate(provider, {
    successRedirect: '/',
    failureRedirect: '/login'
  }));
}

// Setup Google OAuth routes
googleLogin.setupRoutes(app);

// Setup Facebook OAuth routes
facebookLogin.setupRoutes(app);

// Setup Twitter OAuth routes
twitterLogin.setupRoutes(app);

// Setup Biometric/Passkey authentication routes
biometricLogin.setupRoutes(app);

// Setup Trading Integration routes
tradingIntegration.setupRoutes(app);

authRoute('apple');

app.listen(3000, () => console.log('Server started on port 3000'));