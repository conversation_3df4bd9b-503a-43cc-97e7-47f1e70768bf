[project]
name = "repl-nix-workspace"
version = "0.1.0"
description = "Add your description here"
requires-python = ">=3.11"
dependencies = [
    "alembic>=1.16.1",
    "coinbase-agentkit>=0.6.0",
    "flask-login>=0.6.3",
    "flask>=3.1.1",
    "numpy>=2.3.0",
    "openai>=1.86.0",
    "pandas>=2.3.0",
    "psycopg2-binary>=2.9.10",
    "python-dotenv>=1.1.0",
    "requests>=2.32.3",
    "sqlalchemy>=2.0.41",
    "oauthlib>=3.2.2",
    "authlib>=1.6.0",
    "requests-oauthlib>=2.0.0",
    "flask-jwt-extended>=4.7.1",
    "flask-bcrypt>=1.0.1",
    "flask-cors>=6.0.1",
    "pyotp>=2.9.0",
    "qrcode>=8.2",
    "pillow>=11.2.1",
    "webauthn>=2.5.2",
]
