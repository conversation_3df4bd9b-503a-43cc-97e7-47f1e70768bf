<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Dashboard - MultiLogin</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e1e5e9;
        }

        .header h1 {
            color: #333;
            font-size: 2.5rem;
        }

        .user-info {
            text-align: right;
        }

        .user-info h3 {
            color: #333;
            margin-bottom: 5px;
        }

        .user-info p {
            color: #666;
        }

        .nav-links {
            margin-bottom: 30px;
        }

        .nav-links a {
            display: inline-block;
            padding: 10px 20px;
            margin-right: 15px;
            background: #f8f9fa;
            color: #333;
            text-decoration: none;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .nav-links a:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }

        .status-panel {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #dc3545;
        }

        .status-dot.running {
            background: #28a745;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            color: white;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .trading-iframe {
            width: 100%;
            height: 800px;
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            background: white;
        }

        .loading {
            text-align: center;
            padding: 40px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .message-placeholder {
            min-height: 60px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .message {
            padding: 15px;
            border-radius: 10px;
            width: 100%;
            box-sizing: border-box;
            animation: slideIn 0.3s ease-out;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .message.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message-fade-out {
            animation: fadeOut 0.5s ease-out forwards;
        }

        @keyframes fadeOut {
            from {
                opacity: 1;
                transform: translateY(0);
            }
            to {
                opacity: 0;
                transform: translateY(-10px);
            }
        }

        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .card h3 {
            color: #333;
            margin-bottom: 15px;
        }

        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
            
            .header {
                flex-direction: column;
                text-align: center;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Trading Dashboard</h1>
            <div class="user-info">
                <h3>Welcome, <%= user.name || user.email %>!</h3>
                <p>Secure Trading Environment</p>
            </div>
        </div>

        <div class="nav-links">
            <a href="/dashboard">🔐 Security Settings</a>
            <a href="/logout">🚪 Logout & Exit Trading</a>
        </div>

        <div id="message-container" class="message-placeholder">
            <!-- Fixed space for messages to prevent layout shifts -->
        </div>

        <div class="status-panel">
            <h3>Trading Service Status</h3>
            <div class="status-indicator">
                <div id="status-dot" class="status-dot"></div>
                <span id="status-text">Checking status...</span>
            </div>
            
            <div>
                <button id="start-btn" class="btn btn-success" onclick="startTradingService()">
                    ▶️ Start Trading Service
                </button>
                <button id="stop-btn" class="btn btn-danger" onclick="stopTradingService()">
                    ⏹️ Stop Trading Service
                </button>
                <button id="refresh-btn" class="btn btn-primary" onclick="checkStatus()">
                    🔄 Refresh Status
                </button>
            </div>
        </div>

        <div id="trading-content">
            <div id="loading" class="loading" style="display: none;">
                <div class="spinner"></div>
                <p>Loading trading interface...</p>
            </div>

            <div id="trading-interface">
                <div class="grid">
                    <div class="card">
                        <h3>💰 Current User Wallet</h3>
                        <div id="wallet-info">
                            <p>Loading wallet data...</p>
                        </div>
                    </div>

                    <div class="card">
                        <h3>📊 Market Data</h3>
                        <div id="market-data">
                            <p>Loading market prices...</p>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <h3>🏦 All Coinbase Wallets</h3>
                    <div style="display: flex; gap: 15px; margin-bottom: 20px; flex-wrap: wrap;">
                        <button class="btn btn-primary" onclick="testApiConnection()">🧪 Test API</button>
                        <button class="btn btn-primary" onclick="loadAllWallets()">🔄 Load All Wallets</button>
                        <button class="btn btn-success" onclick="createNewWallet()">➕ Create Testnet Wallet</button>
                        <button class="btn btn-success" onclick="toggleWalletView()">👁️ Toggle View</button>
                    </div>

                    <div style="display: flex; gap: 15px; margin-bottom: 20px; flex-wrap: wrap; align-items: center;">
                        <label>Show per page:</label>
                        <select id="wallets-per-page" onchange="changeWalletsPerPage()" style="padding: 5px;">
                            <option value="5">5</option>
                            <option value="10">10</option>
                            <option value="20">20</option>
                            <option value="50">50</option>
                            <option value="all">All</option>
                        </select>

                        <button class="btn btn-primary" onclick="sortWallets('balance')" style="font-size: 0.9em;">💰 Sort by Balance</button>
                        <button class="btn btn-primary" onclick="sortWallets('date')" style="font-size: 0.9em;">📅 Sort by Date</button>
                        <button class="btn btn-primary" onclick="sortWallets('network')" style="font-size: 0.9em;">🌐 Sort by Network</button>
                    </div>
                    <div id="api-test-result" style="margin-bottom: 15px;"></div>
                    <div id="all-wallets-info">
                        <p>Click "Test API" first to verify your Coinbase connection, then "Load All Wallets"</p>
                    </div>
                </div>

                <div class="card">
                    <h3>🤖 Trading Strategies</h3>
                    <div id="strategies-info">
                        <p>Loading strategies...</p>
                    </div>
                </div>

                <div class="card">
                    <h3>⟠ ETH Actions</h3>
                    <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                        <button class="btn btn-success" onclick="showSendEthForm()">📤 Send ETH</button>
                        <button class="btn btn-primary" onclick="showWalletAddress()">📋 Show Address</button>
                        <button class="btn btn-info" onclick="showMyWalletDetails()">🔍 My Wallet Details</button>
                        <button class="btn btn-success" onclick="fundDefaultWallet()">💰 Fund Default Wallet</button>
                        <button class="btn btn-primary" onclick="requestMaximumFunding()">🚀 Request Maximum Funding</button>
                        <button class="btn btn-primary" onclick="refreshData()">🔄 Refresh Data</button>
                    </div>

                    <!-- Send ETH Form (hidden by default) -->
                    <div id="send-eth-form" style="display: none; margin-top: 20px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                        <h4>Send ETH</h4>
                        <div style="margin-bottom: 15px;">
                            <label>To Address:</label>
                            <input type="text" id="send-to-address" placeholder="ETH address (0x...)" style="width: 100%; padding: 8px; margin-top: 5px;">
                        </div>
                        <div style="margin-bottom: 15px;">
                            <label>Amount (ETH):</label>
                            <input type="number" id="send-amount" placeholder="0.001" step="0.00000001" style="width: 100%; padding: 8px; margin-top: 5px;">
                        </div>
                        <div>
                            <button class="btn btn-success" onclick="executeSendEth()">Send</button>
                            <button class="btn btn-danger" onclick="hideSendEthForm()">Cancel</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let serviceStatus = 'stopped';

        function showMessage(message, type = 'info') {
            const container = document.getElementById('message-container');

            // Clear any existing message
            container.innerHTML = '';

            // Create new message element
            const messageElement = document.createElement('div');
            messageElement.className = `message ${type}`;
            messageElement.textContent = message;

            // Add to container
            container.appendChild(messageElement);

            // Auto-hide after 5 seconds with fade animation
            setTimeout(() => {
                messageElement.classList.add('message-fade-out');
                setTimeout(() => {
                    if (container.contains(messageElement)) {
                        container.removeChild(messageElement);
                    }
                }, 500); // Wait for fade animation to complete
            }, 5000);
        }

        function updateUI(status) {
            const statusDot = document.getElementById('status-dot');
            const statusText = document.getElementById('status-text');
            const startBtn = document.getElementById('start-btn');
            const stopBtn = document.getElementById('stop-btn');
            const tradingInterface = document.getElementById('trading-interface');

            // Check if elements exist before trying to modify them
            if (statusDot) {
                if (status === 'running') {
                    statusDot.classList.add('running');
                } else {
                    statusDot.classList.remove('running');
                }
            }

            if (statusText) {
                statusText.textContent = status === 'running'
                    ? 'Trading service is running'
                    : 'Trading service is stopped';
            }

            if (startBtn) {
                startBtn.disabled = (status === 'running');
            }

            if (stopBtn) {
                stopBtn.disabled = (status !== 'running');
            }

            if (tradingInterface) {
                tradingInterface.style.display = 'block'; // Always show the trading interface
            }
        }

        async function checkStatus() {
            try {
                const response = await fetch('/trading/status');
                const data = await response.json();
                serviceStatus = data.status;
                updateUI(serviceStatus);
            } catch (error) {
                console.error('Error checking status:', error);
                showMessage('Failed to check service status', 'error');
            }
        }

        async function startTradingService() {
            try {
                const loadingElement = document.getElementById('loading');
                if (loadingElement) {
                    loadingElement.style.display = 'block';
                }
                showMessage('Starting trading service...', 'info');

                const response = await fetch('/trading/start', { method: 'POST' });
                const data = await response.json();

                if (data.success) {
                    showMessage('Trading service started successfully!', 'success');
                    setTimeout(async () => {
                        await checkStatus();
                        await loadTradingData();
                    }, 1000);
                } else {
                    showMessage('Failed to start trading service: ' + data.message, 'error');
                }
            } catch (error) {
                console.error('Error starting service:', error);
                showMessage('Failed to start trading service', 'error');
            } finally {
                const loadingElement = document.getElementById('loading');
                if (loadingElement) {
                    loadingElement.style.display = 'none';
                }
            }
        }

        async function stopTradingService() {
            try {
                showMessage('Stopping trading service...', 'info');
                
                const response = await fetch('/trading/stop', { method: 'POST' });
                const data = await response.json();
                
                if (data.success) {
                    showMessage('Trading service stopped successfully!', 'success');
                    await checkStatus();
                    // Clear trading data when stopped
                    document.getElementById('wallet-info').innerHTML = '<p>Trading service stopped</p>';
                    document.getElementById('market-data').innerHTML = '<p>Trading service stopped</p>';
                    document.getElementById('strategies-info').innerHTML = '<p>Trading service stopped</p>';
                } else {
                    showMessage('Failed to stop trading service: ' + data.message, 'error');
                }
            } catch (error) {
                console.error('Error stopping service:', error);
                showMessage('Failed to stop trading service', 'error');
            }
        }

        // Load trading data
        async function loadTradingData() {
            try {
                // Load wallet data
                const walletResponse = await fetch('/api/trading/wallet');
                const walletData = await walletResponse.json();
                const walletSource = walletData.isReal ? '🟢 Real ETH Wallet' : '🔴 Demo Wallet';
                const networkBadge = walletData.network ? `<span style="background: #007bff; color: white; padding: 2px 6px; border-radius: 4px; font-size: 0.8em;">${walletData.network}</span>` : '';

                document.getElementById('wallet-info').innerHTML = `
                    <p><strong>Total Value:</strong> $${walletData.totalValue.toLocaleString()}</p>
                    <p><strong>ETH:</strong> ${walletData.holdings.ETH || 0} ETH</p>
                    <p><strong>USD:</strong> $${walletData.holdings.USD || 0}</p>
                    ${walletData.walletAddress ? `
                        <p><strong>Address:</strong>
                            <code style="font-size: 0.8em;">${walletData.walletAddress.substring(0, 20)}...</code>
                            <button onclick="copyAddressToClipboard('${walletData.walletAddress}')" style="margin-left: 10px; padding: 4px 8px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.8em;">📋 Copy Full Address</button>
                        </p>
                    ` : ''}
                    <p><small><strong>Source:</strong> ${walletSource} ${networkBadge}</small></p>
                    <p><small><strong>Status:</strong> ${walletData.status || 'unknown'}</small></p>
                `;

                // Load market data
                const marketResponse = await fetch('/api/trading/market');
                const marketData = await marketResponse.json();
                const dataSource = marketData.isReal ? '🟢 Live Data' : '🔴 Demo Data';
                document.getElementById('market-data').innerHTML = `
                    <p><strong>BTC:</strong> $${marketData.prices.BTC.toFixed(2)}</p>
                    <p><strong>ETH:</strong> $${marketData.prices.ETH.toFixed(2)}</p>
                    <p><strong>ADA:</strong> $${marketData.prices.ADA.toFixed(3)}</p>
                    <p><strong>SOL:</strong> $${marketData.prices.SOL.toFixed(2)}</p>
                    <p><small><strong>Source:</strong> ${dataSource}</small></p>
                `;

                // Load strategies
                const strategiesResponse = await fetch('/api/trading/strategies');
                const strategiesData = await strategiesResponse.json();
                document.getElementById('strategies-info').innerHTML = `
                    <p><strong>Active:</strong> ${strategiesData.active.join(', ')}</p>
                    <p><strong>Risk Level:</strong> ${strategiesData.riskLevel}</p>
                `;

            } catch (error) {
                console.error('Error loading trading data:', error);
                showMessage('Failed to load trading data', 'error');
            }
        }

        function showSendEthForm() {
            document.getElementById('send-eth-form').style.display = 'block';
        }

        function hideSendEthForm() {
            document.getElementById('send-eth-form').style.display = 'none';
            document.getElementById('send-to-address').value = '';
            document.getElementById('send-amount').value = '';
        }

        async function executeSendEth() {
            const sendButton = event.target;
            const originalText = sendButton.innerHTML;

            try {
                // Add button feedback
                sendButton.disabled = true;
                sendButton.style.opacity = '0.6';
                sendButton.innerHTML = '⏳ Sending...';

                const toAddress = document.getElementById('send-to-address').value;
                const amount = document.getElementById('send-amount').value;

                if (!toAddress || !amount) {
                    showMessage('Please enter both address and amount', 'error');
                    return;
                }

                showMessage('Sending ETH...', 'info');

                const response = await fetch('/api/trading/wallet/send', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        toAddress: toAddress,
                        amount: parseFloat(amount)
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showMessage(`ETH sent successfully! TX: ${result.transactionHash}`, 'success');
                    hideSendEthForm();
                    await loadTradingData(); // Refresh wallet data
                } else {
                    showMessage(`Failed to send ETH: ${result.error}`, 'error');
                }

            } catch (error) {
                console.error('Error sending ETH:', error);
                showMessage('Failed to send ETH: ' + error.message, 'error');
            } finally {
                // Reset button
                sendButton.disabled = false;
                sendButton.style.opacity = '1';
                sendButton.innerHTML = originalText;
            }
        }

        async function showWalletAddress() {
            try {
                const response = await fetch('/api/trading/wallet/address');
                const data = await response.json();

                if (data.address) {
                    const message = `Your Bitcoin Address: ${data.address}\nNetwork: ${data.network}`;
                    alert(message);

                    // Copy to clipboard if possible
                    if (navigator.clipboard) {
                        navigator.clipboard.writeText(data.address);
                        showMessage('Address copied to clipboard!', 'success');
                    }
                } else {
                    showMessage('Failed to get wallet address', 'error');
                }
            } catch (error) {
                console.error('Error getting wallet address:', error);
                showMessage('Failed to get wallet address', 'error');
            }
        }

        async function showMyWalletDetails() {
            try {
                showMessage('Loading your wallet details...', 'info');

                const response = await fetch('/api/trading/wallet/my-wallet');
                const data = await response.json();

                if (data.hasWallet) {
                    const details = `
🏦 YOUR ETH WALLET DETAILS

📍 Address: ${data.address}
🆔 Wallet ID: ${data.walletId}
🌐 Network: ${data.network} (Base Sepolia Testnet)
💰 Balance: ${data.balance.ETH || 0} ETH
💵 USD Value: $${data.balance.USD || 0}
📧 Email: ${data.userEmail}
📅 Created: ${new Date(data.createdAt).toLocaleString()}
✅ Status: ${data.isActive ? 'Active' : 'Inactive'}

This is your default ETH address for receiving funds on Base Sepolia.
                    `.trim();

                    alert(details);

                    // Copy address to clipboard
                    if (navigator.clipboard && data.address) {
                        navigator.clipboard.writeText(data.address);
                        showMessage('Wallet address copied to clipboard!', 'success');
                    }
                } else {
                    showMessage('No wallet found. ' + (data.message || 'Please create a wallet first.'), 'error');
                }
            } catch (error) {
                console.error('Error getting wallet details:', error);
                showMessage('Failed to get wallet details: ' + error.message, 'error');
            }
        }

        let allWalletsData = null;
        let showDetailedView = false;
        let walletsPerPage = 5;
        let currentPage = 1;

        async function testApiConnection() {
            try {
                showMessage('Testing Coinbase API connection...', 'info');

                const response = await fetch('/api/trading/wallet/test-api');
                const data = await response.json();

                const resultDiv = document.getElementById('api-test-result');

                if (data.success) {
                    resultDiv.innerHTML = `
                        <div style="background: #d4edda; color: #155724; padding: 10px; border-radius: 8px; border: 1px solid #c3e6cb;">
                            <strong>✅ API Connection Successful!</strong><br>
                            Found ${data.walletCount} wallets in your CDP project<br>
                            <small>Timestamp: ${data.timestamp}</small>
                        </div>
                    `;
                    showMessage('API connection test passed!', 'success');
                } else {
                    resultDiv.innerHTML = `
                        <div style="background: #f8d7da; color: #721c24; padding: 10px; border-radius: 8px; border: 1px solid #f5c6cb;">
                            <strong>❌ API Connection Failed</strong><br>
                            Error: ${data.error}<br>
                            ${data.httpCode ? `HTTP Code: ${data.httpCode}<br>` : ''}
                            ${data.apiCode ? `API Code: ${data.apiCode}<br>` : ''}
                            ${data.apiMessage ? `API Message: ${data.apiMessage}<br>` : ''}
                            ${data.correlationId ? `Correlation ID: ${data.correlationId}<br>` : ''}
                            <small>Timestamp: ${data.timestamp}</small>
                        </div>
                    `;
                    showMessage('API connection test failed: ' + data.error, 'error');
                }
            } catch (error) {
                console.error('Error testing API connection:', error);
                document.getElementById('api-test-result').innerHTML = `
                    <div style="background: #f8d7da; color: #721c24; padding: 10px; border-radius: 8px; border: 1px solid #f5c6cb;">
                        <strong>❌ Test Failed</strong><br>
                        Network Error: ${error.message}
                    </div>
                `;
                showMessage('Failed to test API connection: ' + error.message, 'error');
            }
        }

        async function loadAllWallets() {
            try {
                showMessage('Loading all Coinbase wallets...', 'info');

                const response = await fetch('/api/trading/wallet/list-all');
                const data = await response.json();

                if (data.success) {
                    allWalletsData = data;
                    displayAllWallets();
                    showMessage(`Loaded ${data.totalWallets} wallets successfully!`, 'success');
                } else {
                    showMessage('Failed to load wallets: ' + data.error, 'error');
                }
            } catch (error) {
                console.error('Error loading all wallets:', error);
                showMessage('Failed to load wallets: ' + error.message, 'error');
            }
        }

        function displayAllWallets() {
            if (!allWalletsData) {
                document.getElementById('all-wallets-info').innerHTML = '<p>No wallet data available</p>';
                return;
            }

            // Sort wallets by BTC balance (highest first)
            const sortedMainnetWallets = [...allWalletsData.mainnetWallets].sort((a, b) => {
                const balanceA = parseFloat(a.balance.BTC || 0);
                const balanceB = parseFloat(b.balance.BTC || 0);
                return balanceB - balanceA;
            });

            const sortedTestnetWallets = [...allWalletsData.testnetWallets].sort((a, b) => {
                const balanceA = parseFloat(a.balance.BTC || 0);
                const balanceB = parseFloat(b.balance.BTC || 0);
                return balanceB - balanceA;
            });

            const totalWallets = allWalletsData.totalWallets;

            let html = `
                <div style="margin-bottom: 20px;">
                    <h4>📊 Summary</h4>
                    <p><strong>Total Wallets:</strong> ${totalWallets}</p>
                    <p><strong>Mainnet Wallets:</strong> ${sortedMainnetWallets.length}</p>
                    <p><strong>Testnet Wallets:</strong> ${sortedTestnetWallets.length}</p>
                </div>
            `;

            if (showDetailedView) {
                // Detailed view with pagination
                const renderWalletSection = (wallets, title, color, isTestnet = false) => {
                    if (wallets.length === 0) return '';

                    const startIndex = walletsPerPage === 'all' ? 0 : (currentPage - 1) * walletsPerPage;
                    const endIndex = walletsPerPage === 'all' ? wallets.length : startIndex + walletsPerPage;
                    const paginatedWallets = wallets.slice(startIndex, endIndex);
                    const totalPages = walletsPerPage === 'all' ? 1 : Math.ceil(wallets.length / walletsPerPage);

                    return `
                        <div style="margin-bottom: 30px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                <h4>${title} (${wallets.length} total)</h4>
                                ${walletsPerPage !== 'all' ? `
                                    <div style="display: flex; gap: 10px; align-items: center;">
                                        <span>Page ${currentPage} of ${totalPages}</span>
                                        <button onclick="changePage(-1)" ${currentPage <= 1 ? 'disabled' : ''} class="btn btn-primary" style="padding: 4px 8px; font-size: 0.8em;">◀</button>
                                        <button onclick="changePage(1)" ${currentPage >= totalPages ? 'disabled' : ''} class="btn btn-primary" style="padding: 4px 8px; font-size: 0.8em;">▶</button>
                                    </div>
                                ` : ''}
                            </div>
                            ${paginatedWallets.map((wallet, index) => {
                                const globalIndex = startIndex + index + 1;
                                const btcBalance = parseFloat(wallet.balance.BTC || 0);
                                return `
                                    <div style="background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 4px solid ${color};">
                                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                                            <h5 style="margin: 0;">Wallet #${globalIndex} ${btcBalance > 0 ? '💰' : ''}</h5>
                                            <span style="background: ${color}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 0.8em;">${isTestnet ? 'TESTNET' : 'MAINNET'}</span>
                                        </div>

                                        <div style="margin-bottom: 10px;">
                                            <strong>Address:</strong>
                                            <div style="background: #e9ecef; padding: 8px; border-radius: 4px; margin-top: 5px; position: relative;">
                                                <code style="font-size: 0.75em; word-break: break-all; font-family: 'Courier New', monospace;">${wallet.address}</code>
                                                <button onclick="copyToClipboard('${wallet.address}')" style="position: absolute; top: 5px; right: 5px; background: #007bff; color: white; border: none; padding: 2px 6px; border-radius: 3px; font-size: 0.7em; cursor: pointer;">📋</button>
                                            </div>
                                        </div>

                                        <p><strong>Network:</strong> ${wallet.networkId}</p>
                                        <p><strong>Balance:</strong>
                                            <span style="color: ${btcBalance > 0 ? '#28a745' : '#6c757d'}; font-weight: bold;">
                                                ${btcBalance} BTC
                                            </span>
                                            ${wallet.balance.USD ? ` | $${wallet.balance.USD} USD` : ''}
                                        </p>

                                        <div style="margin-bottom: 10px;">
                                            <strong>Wallet ID:</strong>
                                            <div style="background: #e9ecef; padding: 8px; border-radius: 4px; margin-top: 5px; position: relative;">
                                                <code style="font-size: 0.7em; word-break: break-all; font-family: 'Courier New', monospace;">${wallet.id}</code>
                                                <button onclick="copyToClipboard('${wallet.id}')" style="position: absolute; top: 5px; right: 5px; background: #007bff; color: white; border: none; padding: 2px 6px; border-radius: 3px; font-size: 0.7em; cursor: pointer;">📋</button>
                                            </div>
                                        </div>

                                        <p><strong>Created:</strong> ${new Date(wallet.createdAt).toLocaleString()}</p>
                                        ${wallet.error ? `<p style="color: #dc3545;"><strong>Error:</strong> ${wallet.error}</p>` : ''}

                                        <div style="margin-top: 10px; display: flex; gap: 10px; flex-wrap: wrap;">
                                            ${isTestnet ? `
                                                <button class="btn btn-success" onclick="requestFaucet('${wallet.id}')" style="font-size: 0.8em; padding: 6px 12px;">💧 Request Faucet</button>
                                                <button class="btn btn-primary" onclick="fundWalletWithTest('${wallet.id}')" style="font-size: 0.8em; padding: 6px 12px;">💰 Fund 500 BTC</button>
                                            ` : `
                                                <button class="btn btn-info" onclick="showFundingInfo('${wallet.id}')" style="font-size: 0.8em; padding: 6px 12px;">ℹ️ Funding Info</button>
                                            `}
                                        </div>
                                    </div>
                                `;
                            }).join('')}
                        </div>
                    `;
                };

                if (sortedMainnetWallets.length > 0) {
                    html += renderWalletSection(sortedMainnetWallets, '🌐 Base Mainnet Wallets', '#28a745', false);
                }

                if (sortedTestnetWallets.length > 0) {
                    html += renderWalletSection(sortedTestnetWallets, '🧪 Base Sepolia Testnet Wallets', '#007bff', true);
                }
            } else {
                // Summary view
                html += `
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <h4>🌐 Mainnet (${sortedMainnetWallets.length})</h4>
                            ${sortedMainnetWallets.slice(0, 5).map((wallet, index) => {
                                const btcBalance = parseFloat(wallet.balance.BTC || 0);
                                return `
                                    <div style="background: #e8f5e9; padding: 10px; margin: 5px 0; border-radius: 6px; border-left: 3px solid ${btcBalance > 0 ? '#28a745' : '#6c757d'};">
                                        <p style="margin: 0;"><strong>BTC:</strong> <span style="color: ${btcBalance > 0 ? '#28a745' : '#6c757d'};">${btcBalance}</span> ${btcBalance > 0 ? '💰' : ''}</p>
                                        <p style="margin: 0; font-size: 0.75em; color: #666; font-family: monospace;">${wallet.address.substring(0, 25)}...${wallet.address.substring(wallet.address.length - 10)}</p>
                                    </div>
                                `;
                            }).join('')}
                            ${sortedMainnetWallets.length > 5 ? `
                                <button onclick="showAllMainnetWallets()" style="
                                    background: #28a745;
                                    color: white;
                                    border: none;
                                    padding: 8px 12px;
                                    border-radius: 6px;
                                    cursor: pointer;
                                    font-size: 0.9em;
                                    margin-top: 10px;
                                    width: 100%;
                                ">
                                    📋 Show All ${sortedMainnetWallets.length} Mainnet Wallets
                                </button>
                            ` : ''}
                        </div>

                        <div>
                            <h4>🧪 Testnet (${sortedTestnetWallets.length})</h4>
                            ${sortedTestnetWallets.slice(0, 5).map((wallet, index) => {
                                const btcBalance = parseFloat(wallet.balance.BTC || 0);
                                return `
                                    <div style="background: #e3f2fd; padding: 10px; margin: 5px 0; border-radius: 6px; border-left: 3px solid ${btcBalance > 0 ? '#007bff' : '#6c757d'};">
                                        <p style="margin: 0;"><strong>BTC:</strong> <span style="color: ${btcBalance > 0 ? '#007bff' : '#6c757d'};">${btcBalance}</span> ${btcBalance > 0 ? '💰' : ''}</p>
                                        <p style="margin: 0; font-size: 0.75em; color: #666; font-family: monospace;">${wallet.address.substring(0, 25)}...${wallet.address.substring(wallet.address.length - 10)}</p>
                                    </div>
                                `;
                            }).join('')}
                            ${sortedTestnetWallets.length > 5 ? `
                                <button onclick="showAllTestnetWallets()" style="
                                    background: #007bff;
                                    color: white;
                                    border: none;
                                    padding: 8px 12px;
                                    border-radius: 6px;
                                    cursor: pointer;
                                    font-size: 0.9em;
                                    margin-top: 10px;
                                    width: 100%;
                                ">
                                    📋 Show All ${sortedTestnetWallets.length} Testnet Wallets
                                </button>
                            ` : ''}
                        </div>
                    </div>
                `;
            }

            document.getElementById('all-wallets-info').innerHTML = html;
        }

        function toggleWalletView() {
            showDetailedView = !showDetailedView;
            if (allWalletsData) {
                displayAllWallets();
                showMessage(showDetailedView ? 'Switched to detailed view' : 'Switched to summary view', 'info');
            } else {
                showMessage('Load wallets first', 'error');
            }
        }

        async function createNewWallet() {
            try {
                showMessage('Creating new testnet wallet...', 'info');

                const response = await fetch('/api/trading/wallet/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        network: 'testnet'
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showMessage(`New testnet wallet created! Address: ${result.address}`, 'success');

                    // Copy the new address to clipboard
                    if (navigator.clipboard) {
                        navigator.clipboard.writeText(result.address);
                        setTimeout(() => {
                            showMessage('New wallet address copied to clipboard!', 'info');
                        }, 2000);
                    }

                    // Show the full address
                    setTimeout(() => {
                        alert(`New ETH Wallet Created!\n\nAddress: ${result.address}\nNetwork: ${result.networkId}\nWallet ID: ${result.id}\n\nAddress has been copied to clipboard.`);
                    }, 1000);

                    // Reload wallets to show the new one
                    await loadAllWallets();
                } else {
                    showMessage('Failed to create wallet: ' + result.error, 'error');
                    if (result.details) {
                        console.error('Wallet creation error details:', result.details);
                    }
                }

            } catch (error) {
                console.error('Error creating wallet:', error);
                showMessage('Failed to create wallet: ' + error.message, 'error');
            }
        }

        async function requestFaucet(walletId) {
            try {
                showMessage('Requesting faucet funds...', 'info');

                const response = await fetch('/api/trading/wallet/faucet', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        walletId: walletId
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showMessage(`Faucet request successful! Transaction: ${result.transactionHash}`, 'success');
                    // Reload wallets to show updated balance
                    setTimeout(() => loadAllWallets(), 3000);
                } else {
                    showMessage('Faucet request failed: ' + result.error, 'error');
                }

            } catch (error) {
                console.error('Error requesting faucet:', error);
                showMessage('Failed to request faucet: ' + error.message, 'error');
            }
        }

        async function fundDefaultWallet() {
            try {
                showMessage('Funding default wallet with 500 test BTC...', 'info');

                const response = await fetch('/api/trading/wallet/fund-default', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        amount: 500
                    })
                });

                const result = await response.json();

                if (result.success) {
                    let message = `Wallet funding successful!`;
                    if (result.isSimulated) {
                        message += ` (Simulated 500 BTC for testing)`;
                    } else {
                        message += ` Transaction: ${result.transactionHash}`;
                    }
                    showMessage(message, 'success');

                    // Reload wallet data to show updated balance
                    setTimeout(() => {
                        loadTradingData();
                        if (allWalletsData) loadAllWallets();
                    }, 2000);
                } else {
                    showMessage('Wallet funding failed: ' + result.error, 'error');
                }

            } catch (error) {
                console.error('Error funding default wallet:', error);
                showMessage('Failed to fund wallet: ' + error.message, 'error');
            }
        }

        async function fundWalletWithTest(walletId) {
            try {
                showMessage('Funding wallet with 500 test BTC...', 'info');

                const response = await fetch('/api/trading/wallet/fund-test', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        walletId: walletId,
                        amount: 500
                    })
                });

                const result = await response.json();

                if (result.success) {
                    let message = `Wallet funding successful!`;
                    if (result.isSimulated) {
                        message += ` (Simulated 500 BTC for testing)`;
                    } else {
                        message += ` Transaction: ${result.transactionHash}`;
                    }
                    showMessage(message, 'success');

                    // Reload wallets to show updated balance
                    setTimeout(() => loadAllWallets(), 3000);
                } else {
                    showMessage('Wallet funding failed: ' + result.error, 'error');
                }

            } catch (error) {
                console.error('Error funding wallet:', error);
                showMessage('Failed to fund wallet: ' + error.message, 'error');
            }
        }

        function showFundingInfo(walletId) {
            const message = `To fund this mainnet wallet:\n\n` +
                          `1. Send real BTC from another wallet or exchange\n` +
                          `2. Use the wallet address shown above\n` +
                          `3. Wait for network confirmations\n\n` +
                          `Note: This is a real mainnet wallet - only use real BTC!`;
            alert(message);
        }

        async function requestMaximumFunding() {
            try {
                showMessage('Requesting maximum funding from faucet...', 'info');

                // First get the current user's wallet
                const walletResponse = await fetch('/api/trading/wallet');
                const walletData = await walletResponse.json();

                if (!walletData.walletAddress) {
                    showMessage('No wallet found. Please create a wallet first.', 'error');
                    return;
                }

                // Find the wallet ID from the all wallets list
                const allWalletsResponse = await fetch('/api/trading/wallet/list-all');
                const allWalletsData = await allWalletsResponse.json();

                if (!allWalletsData.success) {
                    showMessage('Failed to get wallet list: ' + allWalletsData.error, 'error');
                    return;
                }

                // Find the wallet with matching address
                let targetWalletId = null;
                const allWallets = [...allWalletsData.mainnetWallets, ...allWalletsData.testnetWallets];

                for (const wallet of allWallets) {
                    if (wallet.address === walletData.walletAddress) {
                        targetWalletId = wallet.id;
                        break;
                    }
                }

                if (!targetWalletId) {
                    showMessage('Could not find wallet ID for funding', 'error');
                    return;
                }

                console.log(`Found wallet ID: ${targetWalletId} for address: ${walletData.walletAddress}`);

                const response = await fetch('/api/trading/wallet/fund-maximum', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        walletId: targetWalletId
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showMessage(`Maximum funding requested! ${result.message}. Estimated: ${result.estimatedAmount}`, 'success');

                    // Reload wallet data after a delay
                    setTimeout(() => {
                        loadTradingData();
                        if (allWalletsData) loadAllWallets();
                    }, 5000);
                } else {
                    showMessage('Maximum funding failed: ' + result.error, 'error');
                }

            } catch (error) {
                console.error('Error requesting maximum funding:', error);
                showMessage('Failed to request maximum funding: ' + error.message, 'error');
            }
        }

        function changeWalletsPerPage() {
            const select = document.getElementById('wallets-per-page');
            walletsPerPage = select.value === 'all' ? 'all' : parseInt(select.value);
            currentPage = 1; // Reset to first page
            if (allWalletsData) {
                displayAllWallets();
            }
        }

        function changePage(direction) {
            if (walletsPerPage === 'all') return;

            const maxWallets = Math.max(allWalletsData.mainnetWallets.length, allWalletsData.testnetWallets.length);
            const totalPages = Math.ceil(maxWallets / walletsPerPage);

            currentPage += direction;
            if (currentPage < 1) currentPage = 1;
            if (currentPage > totalPages) currentPage = totalPages;

            displayAllWallets();
        }

        function sortWallets(sortBy) {
            if (!allWalletsData) {
                showMessage('Load wallets first', 'error');
                return;
            }

            const sortFunction = (a, b) => {
                switch (sortBy) {
                    case 'balance':
                        const balanceA = parseFloat(a.balance.BTC || 0);
                        const balanceB = parseFloat(b.balance.BTC || 0);
                        return balanceB - balanceA; // Highest first
                    case 'date':
                        return new Date(b.createdAt) - new Date(a.createdAt); // Newest first
                    case 'network':
                        return a.networkId.localeCompare(b.networkId);
                    default:
                        return 0;
                }
            };

            allWalletsData.mainnetWallets.sort(sortFunction);
            allWalletsData.testnetWallets.sort(sortFunction);

            currentPage = 1; // Reset to first page
            displayAllWallets();
            showMessage(`Sorted by ${sortBy}`, 'info');
        }

        async function copyToClipboard(text) {
            try {
                if (navigator.clipboard) {
                    await navigator.clipboard.writeText(text);
                    showMessage('Copied to clipboard!', 'success');
                } else {
                    // Fallback for older browsers
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    showMessage('Copied to clipboard!', 'success');
                }
            } catch (error) {
                console.error('Failed to copy to clipboard:', error);
                showMessage('Failed to copy to clipboard', 'error');
            }
        }

        async function copyAddressToClipboard(address) {
            try {
                if (navigator.clipboard) {
                    await navigator.clipboard.writeText(address);
                    showMessage(`ETH Address copied: ${address}`, 'success');
                } else {
                    // Fallback for older browsers
                    const textArea = document.createElement('textarea');
                    textArea.value = address;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    showMessage(`ETH Address copied: ${address}`, 'success');
                }

                // Also show the full address in an alert for easy viewing
                alert(`Your ETH Address (Base Sepolia):\n\n${address}\n\nThis address has been copied to your clipboard.`);

            } catch (error) {
                console.error('Failed to copy address to clipboard:', error);
                showMessage('Failed to copy address to clipboard', 'error');
            }
        }

        function showAllMainnetWallets() {
            if (!showDetailedView) {
                showDetailedView = true;
                displayAllWallets();
                showMessage('Switched to detailed view - showing all mainnet wallets', 'info');
            } else {
                showMessage('Already in detailed view - mainnet wallets are visible above', 'info');
            }

            // Scroll to the wallet section
            setTimeout(() => {
                const walletSection = document.getElementById('all-wallets-info');
                if (walletSection) {
                    walletSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
            }, 200);
        }

        function showAllTestnetWallets() {
            if (!showDetailedView) {
                showDetailedView = true;
                displayAllWallets();
                showMessage('Switched to detailed view - showing all testnet wallets', 'info');
            } else {
                showMessage('Already in detailed view - testnet wallets are visible below', 'info');
            }

            // Scroll to the wallet section
            setTimeout(() => {
                const walletSection = document.getElementById('all-wallets-info');
                if (walletSection) {
                    walletSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
            }, 200);
        }

        async function refreshData() {
            showMessage('Refreshing trading data...', 'info');
            await loadTradingData();
            await checkStatus();
            showMessage('Data refreshed successfully!', 'success');
        }

        // Initialize trading platform (don't auto-start)
        async function initializeTradingPlatform() {
            await checkStatus();
            // Only load data if service is running
            if (serviceStatus === 'running') {
                await loadTradingData();
            }
        }

        // Initialize on page load
        initializeTradingPlatform();

        // Auto-refresh data every 30 seconds (only when running)
        setInterval(async () => {
            await checkStatus();
            if (serviceStatus === 'running') {
                await loadTradingData();
            }
        }, 30000);
    </script>
</body>
</html>
