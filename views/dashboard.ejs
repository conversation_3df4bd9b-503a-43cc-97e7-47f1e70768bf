<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - MultiLogin</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            color: #333;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 1.1rem;
        }

        .user-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
        }

        .section {
            margin-bottom: 40px;
        }

        .section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .passkey-list {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
        }

        .passkey-item {
            background: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .passkey-info h4 {
            color: #333;
            margin-bottom: 5px;
        }

        .passkey-info p {
            color: #666;
            font-size: 0.9rem;
        }

        .message-placeholder {
            min-height: 60px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .message {
            padding: 15px;
            border-radius: 10px;
            width: 100%;
            box-sizing: border-box;
            animation: slideIn 0.3s ease-out;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message-fade-out {
            animation: fadeOut 0.5s ease-out forwards;
        }

        @keyframes fadeOut {
            from {
                opacity: 1;
                transform: translateY(0);
            }
            to {
                opacity: 0;
                transform: translateY(-10px);
            }
        }

        .logout-section {
            text-align: center;
            padding-top: 30px;
            border-top: 1px solid #e1e5e9;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏔️ Dashboard</h1>
            <p>Manage your account and security settings</p>
        </div>

        <div class="user-info">
            <h3>Welcome, <%= user.name || user.email %>!</h3>
            <p><strong>Email:</strong> <%= user.email %></p>
            <% if (user.provider) { %>
                <p><strong>Login Provider:</strong> <%= user.provider %></p>
            <% } %>
        </div>

        <div id="message-container" class="message-placeholder">
            <!-- Fixed space for messages to prevent layout shifts -->
        </div>

        <div class="section">
            <h2>🔐 Biometric Authentication</h2>
            <p>Secure your account with biometric authentication using passkeys. This allows you to log in using your fingerprint, face recognition, or other biometric methods.</p>

            <div style="background: #e3f2fd; padding: 15px; border-radius: 10px; margin: 20px 0; border-left: 4px solid #2196f3;">
                <h4 style="color: #1976d2; margin-bottom: 10px;">📋 How it works:</h4>
                <ol style="color: #1976d2; margin-left: 20px;">
                    <li>Register a passkey below using your device's biometric authentication</li>
                    <li>Your device creates a secure key pair (private key stays on your device)</li>
                    <li>Next time you visit the login page, you'll see a "Login with Biometrics" button</li>
                    <li>Use your fingerprint, face, or PIN to authenticate instantly!</li>
                </ol>
            </div>
            
            <div style="margin: 20px 0;">
                <button id="register-passkey" class="btn btn-success">
                    ➕ Register New Passkey
                </button>
                <button id="refresh-passkeys" class="btn btn-primary">
                    🔄 Refresh List
                </button>
            </div>

            <div id="loading" class="loading">
                <div class="spinner"></div>
                <p>Processing...</p>
            </div>

            <div id="passkey-container">
                <div class="passkey-list">
                    <div id="passkey-list">
                        <!-- Passkeys will be loaded here -->
                    </div>
                </div>
            </div>
        </div>

        <div class="logout-section">
            <a href="/logout" class="btn btn-danger">🚪 Logout</a>
        </div>
    </div>

    <script>
        let passkeys = [];

        function showMessage(message, type = 'success') {
            const container = document.getElementById('message-container');

            // Clear any existing message
            container.innerHTML = '';

            // Create new message element
            const messageElement = document.createElement('div');
            messageElement.className = `message ${type}`;
            messageElement.textContent = message;

            // Add to container
            container.appendChild(messageElement);

            // Auto-hide after 5 seconds with fade animation
            setTimeout(() => {
                messageElement.classList.add('message-fade-out');
                setTimeout(() => {
                    if (container.contains(messageElement)) {
                        container.removeChild(messageElement);
                    }
                }, 500); // Wait for fade animation to complete
            }, 5000);
        }

        function showLoading(show = true) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        async function loadPasskeys() {
            try {
                showLoading(true);
                const response = await fetch('/auth/biometric/status');
                const data = await response.json();
                
                passkeys = data.passkeys || [];
                renderPasskeys();
            } catch (error) {
                console.error('Error loading passkeys:', error);
                showMessage('Failed to load passkeys', 'error');
            } finally {
                showLoading(false);
            }
        }

        function renderPasskeys() {
            const container = document.getElementById('passkey-list');
            
            if (passkeys.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #666;">No passkeys registered yet. Register your first passkey to enable biometric login.</p>';
                return;
            }

            container.innerHTML = passkeys.map(passkey => `
                <div class="passkey-item">
                    <div class="passkey-info">
                        <h4>${passkey.name}</h4>
                        <p>Created: ${new Date(passkey.createdAt).toLocaleDateString()}</p>
                    </div>
                    <button onclick="removePasskey(${passkey.id})" class="btn btn-danger" style="padding: 8px 16px; font-size: 14px;">
                        🗑️ Remove
                    </button>
                </div>
            `).join('');
        }

        async function registerPasskey() {
            try {
                showLoading(true);
                
                // Begin registration
                const beginResponse = await fetch('/auth/biometric/register/begin', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });

                if (!beginResponse.ok) {
                    throw new Error('Failed to begin registration');
                }

                const options = await beginResponse.json();
                
                // Convert base64url to ArrayBuffer
                options.challenge = Uint8Array.from(atob(options.challenge.replace(/-/g, '+').replace(/_/g, '/')), c => c.charCodeAt(0));
                options.user.id = Uint8Array.from(atob(options.user.id.replace(/-/g, '+').replace(/_/g, '/')), c => c.charCodeAt(0));

                // Create credential
                const credential = await navigator.credentials.create({
                    publicKey: options
                });

                // Complete registration
                const completeResponse = await fetch('/auth/biometric/register/complete', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        credential: {
                            id: credential.id,
                            rawId: Array.from(new Uint8Array(credential.rawId)),
                            response: {
                                attestationObject: Array.from(new Uint8Array(credential.response.attestationObject)),
                                clientDataJSON: Array.from(new Uint8Array(credential.response.clientDataJSON)),
                                publicKey: Array.from(new Uint8Array(credential.response.getPublicKey()))
                            },
                            type: credential.type
                        },
                        name: prompt('Enter a name for this passkey:') || 'Biometric Key'
                    })
                });

                const result = await completeResponse.json();

                if (result.success) {
                    showMessage('Passkey registered successfully!');
                    loadPasskeys();
                } else {
                    throw new Error(result.error || 'Registration failed');
                }

            } catch (error) {
                console.error('Passkey registration error:', error);
                showMessage('Failed to register passkey: ' + error.message, 'error');
            } finally {
                showLoading(false);
            }
        }

        async function removePasskey(id) {
            if (!confirm('Are you sure you want to remove this passkey?')) {
                return;
            }

            try {
                showLoading(true);
                const response = await fetch(`/auth/biometric/remove/${id}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    showMessage('Passkey removed successfully!');
                    loadPasskeys();
                } else {
                    throw new Error(result.error || 'Failed to remove passkey');
                }
            } catch (error) {
                console.error('Error removing passkey:', error);
                showMessage('Failed to remove passkey: ' + error.message, 'error');
            } finally {
                showLoading(false);
            }
        }

        // Event listeners
        document.getElementById('register-passkey').addEventListener('click', registerPasskey);
        document.getElementById('refresh-passkeys').addEventListener('click', loadPasskeys);

        // Check WebAuthn support
        if (!window.PublicKeyCredential) {
            document.getElementById('register-passkey').disabled = true;
            showMessage('WebAuthn is not supported in this browser', 'error');
        }

        // Load passkeys on page load
        loadPasskeys();
    </script>
</body>
</html>
