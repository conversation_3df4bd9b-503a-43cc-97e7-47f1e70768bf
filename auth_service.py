"""
Comprehensive Authentication Service
Handles all authentication operations with proper error handling
"""

import os
import json
import secrets
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from flask import request
from flask_jwt_extended import create_access_token
from sqlalchemy.orm import Session
from auth_models import auth_db, User, OAuthAccount, UserSession, SecurityLog, WebAuthnCredential, TradingAccount

class AuthService:
    """Centralized authentication service"""
    
    def __init__(self):
        self.auth_db = auth_db
    
    def register_user(self, email: str, password: str, **kwargs) -> Dict[str, Any]:
        """Register new user with email/password"""
        try:
            # Validate password requirements
            password_validation = self._validate_password(password)
            if not password_validation["valid"]:
                return {"error": password_validation["message"], "code": 400}
            
            # Check if user exists
            existing_user = self.auth_db.get_user_by_email(email)
            if existing_user:
                return {"error": "User already exists", "code": 409}
            
            # Create user
            with self.auth_db.get_session() as session:
                user = User(
                    email=email,
                    first_name=kwargs.get('first_name', ''),
                    last_name=kwargs.get('last_name', ''),
                    username=kwargs.get('username'),
                    is_verified=False
                )
                user.set_password(password)
                session.add(user)
                session.commit()
                session.refresh(user)
                
                # Create default trading account
                trading_account = TradingAccount(
                    user_id=user.id,
                    account_name="Default Account",
                    account_type="demo"
                )
                session.add(trading_account)
                session.commit()
                
                # Log registration
                self._log_security_event(
                    user.id, "user_registered", 
                    "Email registration", session
                )
                
                access_token = create_access_token(identity=user.id)
                
                return {
                    "success": True,
                    "user": user.to_dict(),
                    "access_token": access_token,
                    "message": "Registration successful"
                }
                
        except Exception as e:
            return {"error": f"Registration failed: {str(e)}", "code": 500}
    
    def authenticate_user(self, email: str, password: str, mfa_token: Optional[str] = None) -> Dict[str, Any]:
        """Authenticate user with email/password and optional MFA"""
        try:
            user = self.auth_db.get_user_by_email(email)
            
            if not user or not user.check_password(password):
                # Log failed login
                self._log_security_event(
                    user.id if user else None,
                    "login_failed",
                    f"Invalid credentials for {email}",
                    risk_level='medium'
                )
                return {"error": "Invalid credentials", "code": 401}
            
            if not user.is_active:
                return {"error": "Account disabled", "code": 403}
            
            # Check MFA if enabled
            if user.mfa_enabled:
                if not mfa_token:
                    return {"error": "MFA token required", "mfa_required": True, "code": 422}
                
                if not user.verify_mfa_token(mfa_token):
                    self._log_security_event(
                        user.id, "mfa_failed", 
                        "Invalid MFA token",
                        risk_level='high'
                    )
                    return {"error": "Invalid MFA token", "code": 401}
            
            # Update last login
            with self.auth_db.get_session() as session:
                user.last_login = datetime.utcnow()
                session.add(user)
                session.commit()
                
                # Log successful login
                self._log_security_event(
                    user.id, "login_success", 
                    "Email/password authentication", session
                )
            
            access_token = create_access_token(identity=user.id)
            
            return {
                "success": True,
                "user": user.to_dict(),
                "access_token": access_token,
                "message": "Login successful"
            }
            
        except Exception as e:
            return {"error": f"Authentication failed: {str(e)}", "code": 500}
    
    def oauth_login(self, provider: str, provider_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle OAuth login/registration"""
        try:
            email = provider_data.get('email')
            provider_id = provider_data.get('id')
            
            if not email or not provider_id:
                return {"error": "Invalid OAuth data", "code": 400}
            
            with self.auth_db.get_session() as session:
                # Check existing user
                user = session.query(User).filter(User.email == email).first()
                
                if not user:
                    # Create new user
                    user = User(
                        email=email,
                        first_name=provider_data.get('given_name', ''),
                        last_name=provider_data.get('family_name', ''),
                        profile_picture_url=provider_data.get('picture', ''),
                        is_verified=True,
                        email_verified_at=datetime.utcnow()
                    )
                    session.add(user)
                    session.flush()  # Get user ID
                    
                    # Create default trading account
                    trading_account = TradingAccount(
                        user_id=user.id,
                        account_name="Default Account",
                        account_type="demo"
                    )
                    session.add(trading_account)
                    session.commit()  # Commit everything together
                
                # Check/create OAuth account
                oauth_account = session.query(OAuthAccount).filter(
                    OAuthAccount.user_id == user.id,
                    OAuthAccount.provider == provider
                ).first()
                
                if not oauth_account:
                    oauth_account = OAuthAccount(
                        user_id=user.id,
                        provider=provider,
                        provider_id=str(provider_id),
                        provider_email=email,
                        provider_name=provider_data.get('name', ''),
                        provider_picture=provider_data.get('picture', '')
                    )
                    session.add(oauth_account)
                else:
                    # Update existing OAuth account
                    oauth_account.provider_name = provider_data.get('name', '')
                    oauth_account.provider_picture = provider_data.get('picture', '')
                
                # Update user last login
                user.last_login = datetime.utcnow()
                session.commit()
                
                access_token = create_access_token(identity=user.id)
                
                return {
                    "success": True,
                    "user": user.to_dict(),
                    "access_token": access_token,
                    "message": f"{provider.title()} login successful"
                }
                
        except Exception as e:
            return {"error": f"OAuth login failed: {str(e)}", "code": 500}
    
    def enable_mfa(self, user_id: str) -> Dict[str, Any]:
        """Enable MFA for user"""
        try:
            with self.auth_db.get_session() as session:
                user = session.query(User).filter(User.id == user_id).first()
                
                if not user:
                    return {"error": "User not found", "code": 404}
                
                if user.mfa_enabled:
                    return {"error": "MFA already enabled", "code": 400}
                
                secret = user.enable_mfa()
                qr_code = user.get_mfa_qr_code()
                
                session.commit()
                
                # Log MFA enable
                self._log_security_event(
                    user_id, "mfa_enabled",
                    "MFA enabled by user", session
                )
                
                return {
                    "success": True,
                    "secret": secret,
                    "qr_code": qr_code,
                    "message": "MFA enabled successfully"
                }
                
        except Exception as e:
            return {"error": f"MFA enable failed: {str(e)}", "code": 500}
    
    def create_session(self, user_id: str, device_info: Optional[Dict] = None) -> UserSession:
        """Create user session"""
        with self.auth_db.get_session() as session:
            user_session = UserSession.create_session(user_id, device_info)
            session.add(user_session)
            session.commit()
            return user_session
    
    def get_user_profile(self, user_id: str) -> Dict[str, Any]:
        """Get user profile with trading accounts"""
        try:
            with self.auth_db.get_session() as session:
                user = session.query(User).filter(User.id == user_id).first()
                
                if not user:
                    return {"error": "User not found", "code": 404}
                
                # Get trading accounts
                trading_accounts = session.query(TradingAccount).filter(
                    TradingAccount.user_id == user_id,
                    TradingAccount.is_active == True
                ).all()
                
                profile = user.to_dict()
                profile['trading_accounts'] = [
                    {
                        'id': acc.id,
                        'name': acc.account_name,
                        'type': acc.account_type,
                        'risk_level': acc.risk_level,
                        'created_at': acc.created_at.isoformat()
                    }
                    for acc in trading_accounts
                ]
                
                return {"success": True, "profile": profile}
                
        except Exception as e:
            return {"error": f"Profile fetch failed: {str(e)}", "code": 500}
    
    def _validate_password(self, password: str) -> Dict[str, Any]:
        """Validate password meets security requirements"""
        if len(password) < 8:
            return {"valid": False, "message": "Password must be at least 8 characters long"}
        
        if not any(c.isupper() for c in password):
            return {"valid": False, "message": "Password must contain at least one uppercase letter"}
        
        if not any(c.islower() for c in password):
            return {"valid": False, "message": "Password must contain at least one lowercase letter"}
        
        if not any(c.isdigit() for c in password):
            return {"valid": False, "message": "Password must contain at least one number"}
        
        special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
        if not any(c in special_chars for c in password):
            return {"valid": False, "message": "Password must contain at least one special character (!@#$%^&*()_+-=[]{}|;:,.<>?)"}
        
        return {"valid": True, "message": "Password is valid"}
    
    def _log_security_event(self, user_id: Optional[str], event_type: str, 
                           description: str, session: Optional[Session] = None,
                           risk_level: str = 'low'):
        """Log security event"""
        try:
            log = SecurityLog(
                user_id=user_id,
                event_type=event_type,
                event_description=description,
                ip_address=request.remote_addr if request else None,
                user_agent=request.headers.get('User-Agent') if request else None,
                risk_level=risk_level
            )
            
            if session:
                session.add(log)
            else:
                with self.auth_db.get_session() as db_session:
                    db_session.add(log)
                    db_session.commit()
                    
        except Exception as e:
            print(f"Failed to log security event: {e}")

# Global auth service instance
auth_service = AuthService()